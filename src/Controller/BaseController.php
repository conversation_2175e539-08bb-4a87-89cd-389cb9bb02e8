<?php

namespace Wb3\SharedBundle\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Wb3\SharedBundle\Helper\ContentHelper;

class BaseController extends AbstractController
{
    private ContentHelper $content;

    public function __construct()
    {
        $this->content = new ContentHelper();
    }

    public function getContent(): ContentHelper
    {
        return $this->content;
    }

    public function setContent(ContentHelper $content): BaseController
    {
        $this->content = $content;
        return $this;
    }


}