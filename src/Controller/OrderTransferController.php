<?php

namespace App\Controller;

use App\Repository\BrandRepository;
use App\Repository\LocationRepository;
use App\Repository\PlatformRepository;
use App\Service\Order\OrderItemService;
use App\Service\Order\OrderItemTransitionService;
use App\Service\Order\OrderService;
use App\Service\Search\SearchService;
use Symfony\Bridge\Twig\Attribute\Template;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;
use Symfony\Component\Routing\Attribute\Route;
use Wb3\SharedBundle\Controller\BaseController;
use Wb3\SharedBundle\Entity\Order;
use Wb3\SharedBundle\Entity\OrderItem;
use Wb3\SharedBundle\Entity\User;
use Wb3\SharedBundle\Helper\BreadcrumbItemHelper;
use Wb3\SharedBundle\Helper\FilterUtility;

#[Route('/order-transfer')]
class OrderTransferController extends BaseController
{
    /**
     * Sipariş yönetimi ana sayfasını gösterir
     * 
     * Bu sayfa, filtrelenebilir ve sıralanabilir sipariş listesi sunar.
     * Siparişler durumlarına göre filtrelenebilir ve farklı kriterlere göre sıralanabilir.
     */
    #[Route('')]
    #[Template('order_transfer/index.html.twig')]
    public function index(Request $request, OrderService $orderService,
                          PlatformRepository $platformRepository,
                          LocationRepository $locationRepository,
                          BrandRepository $brandRepository,
    SearchService $searchService
    ):array

    {
        $page = $request->query->getInt('page', 1);
        $limit = $request->query->getInt('limit', 100);
        $content = $this->getContent();
        $filters = FilterUtility::decorateFilter($request->query->all());
        $filters['sortBy'] ??= 'orderAtAsc';

        $user =  $this->getUser();

//        if($user->getLocations()) {
//            $filters['locationCodes'] = $user->getLocations();
//        }

        //$orderStates = $orderService->repository->allGroupByState($filterStates);
         $filters['orderStates'] = [Order::STATE_NEW]; //transfer de gorunecekler.
         $filters['itemStates'] = [
             OrderItem::STATE_READY,
             OrderItem::STATE_LOCATION_DEFINED,
             OrderItem::STATE_PRICE_ERROR,
             OrderItem::STATE_PRICE_ERROR_NOTIFIED,
             OrderItem::STATE_TRANSFERRED
         ];
        //$filters['state'] = (isset($filters['state'])) ? $filters['state'] : implode(",", $saleStates); //typesense filtrelenecek state ler
        //$states = [...$saleStates, Order::STATE_CUSTOMER_CANCEL_REQUESTED, Order::STATE_DELIVERED]; //hepsi
        /** @var User $user */

        $sortByOptions = [
            'orderAtAsc' => 'Sipariş Tarihine Göre (Eskiden Yeniye)',
            'orderAtDesc' => 'Sipariş Tarihine Göre (Yeniden Eskiye)',
            'lastShippingAtAsc' => 'Kargo Süresine Göre (Eskiden Yeniye)',
            'lastShippingAtDesc' => 'Kargo Süresine Göre (Yeniden Eskiye)',
        ];
        $content->addBreadcrumb(new BreadcrumbItemHelper('Siparişler', 'app_order_index'));
        $content->addBreadcrumb(new BreadcrumbItemHelper('Transfer'));

        $pagination = $searchService->filterItemByStateWithPaginate($filters, $page, $limit); // gecici kapali.

        $locations = $locationRepository->findAll();
        $brands = $brandRepository->findBy([], ['name' => 'ASC']);
        $platforms = $platformRepository->findBy([], ['title' => 'ASC']);


        //$pagination['items'] = [$pagination['items'][0]];
        return $this->getContent()->setData([
            'filters' => $filters,
            'brands' => $brands,
            'locations' => $locations,
            'platforms' => $platforms,
            'pagination' => $pagination,
            'sortByOptions' => $sortByOptions,
            'itemStates' => $filters['itemStates'],
        ])->toArray();

    }

    #[Route('/{orderItem}/transfer')]
    #[Template('order_transfer/index.html.twig')]
    public function transfer(Request $request, OrderItem $orderItem,OrderItemService $orderItemService)
    {
        $name = $request->query->get('name');
        if(!$name) {
            throw new UnprocessableEntityHttpException('NAME_NOT_FOUND');
        }

        $orderItemService->transfer($orderItem,$name);
    }
}