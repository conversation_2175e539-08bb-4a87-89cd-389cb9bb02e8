<?php

namespace App\Controller;

use App\Repository\PlatformRepository;
use App\Service\Order\OrderItemCheckService;
use App\Service\Order\OrderItemService;
use App\Service\Order\OrderItemTransitionService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bridge\Twig\Attribute\Template;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Workflow\Exception\TransitionException;
use Wb3\SharedBundle\Controller\BaseController;
use Wb3\SharedBundle\Entity\OrderItem;
use Wb3\SharedBundle\Entity\OrderItemCheck;
use Wb3\SharedBundle\Helper\BreadcrumbItemHelper;
use Wb3\SharedBundle\Helper\FilterUtility;

class OrderCheckController extends BaseController
{
    public function __construct(private readonly PlatformRepository $platformRepository)
    {
        parent::__construct();
    }

    #[Route('/order-check')]
    #[Template('order_check/index.html.twig')]
    public function index(Request $request, OrderItemCheckService $service): array
    {
        $limit = $request->query->getInt('limit', 10);
        $page = $request->query->getInt('page', 1);
        $filters = FilterUtility::decorateFilter($request->query->all()['filters'] ?? []);

        $content = $this->getContent();
        $content->addBreadcrumb(new BreadcrumbItemHelper('Sipariş Kontrol'));



        $filters['isApproved'] = false;
        $checkPagination = $service->allWithPaginate($page, $limit, $filters);
        $filters['isApproved'] = true;
        $approvedPagination = $service->allWithPaginate($page, $limit, $filters);
        $platforms = $this->platformRepository->findBy([],['title'=>'asc']);

        // İçeriği ayarlayıp döndürün
        return $content
            ->setData([
                'platforms' => $platforms,
                'checkPagination' => $checkPagination,
                'approvedPagination' => $approvedPagination,
                'filters' => $filters,
            ])
            ->toArray();
    }

    #[Route('/order-check/{orderItemCheck}/approve')]
    public function approve(Request $request, OrderItemCheck $orderItemCheck, OrderItemService $service, EntityManagerInterface $em)
    {
        $description = $request->query->get('description');
        $message = '';
        $isSuccess = true;
        try {
            $service->approve($orderItemCheck, $description);
           $orderItemCheck->getItem()->setState(OrderItem::STATE_READY);
           $em->flush();
        } catch (\Exception $e) {
            $isSuccess = false;
            $message = $e->getMessage();
        }

       return new JsonResponse(['isSuccess'=>$isSuccess, 'message'=>$message]);;
    }
    #[Route('/order-check/{orderItem}/notify-price-error')]
    public function notifyPriceError(OrderItem $orderItem, OrderItemService $service, OrderItemTransitionService $transitionService): Response
    {
        $isSuccess = true;
        $message = '';
        try {
            $transitionService->validateCan($orderItem, OrderItem::TRANSITION_TO_PRICE_ERROR_NOTIFIED);

            $service->notifyPriceError($orderItem);

            $transitionService->changeTransition($orderItem, OrderItem::TRANSITION_TO_PRICE_ERROR_NOTIFIED);
        } catch (TransitionException $e) {
            $isSuccess = false;
            $message = $e->getMessage();
        }

        return new JsonResponse(['isSuccess'=>$isSuccess, 'message'=>$message]);
    }
}
