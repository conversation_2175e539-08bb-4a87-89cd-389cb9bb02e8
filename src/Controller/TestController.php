<?php

namespace App\Controller;

use App\Repository\Wb2\OrderRepository;
use App\Service\Cargo\Surat\SuratCargoService;
use App\Service\Korgun\Soap\KorgunSoapService;
use App\Service\QueService;
use App\Service\Search\SearchService;
use App\Type\Platform\Idefix\Pool\List\Product;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\Persistence\ManagerRegistry;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\Routing\Attribute\Route;
use Wb3\SharedBundle\Entity\Order;
use Wb3\SharedBundle\Entity\OrderAudit;
use Wb3\SharedBundle\Entity\OrderItem;
use Wb3\SharedBundle\Helper\ObjectHydrator;

#[Route('/test')]
class TestController extends AbstractController
{

    public function __construct(private readonly EntityManagerInterface $entityManager)
    {
    }

    #[Route('/api')]
    public function apiTest(IdefixApiService $idefixApiService, ParameterBagInterface $parameterBag, IdefixMarketplaceService $idefixMpService)
    {
        $response = $idefixApiService->call('GET','/pool/'.$parameterBag->get('PLATFORM_IDEFIX_VENDOR_ID').'/list');

        /** @var Array<Product> $products */
        $products = array_map(function($product){return ObjectHydrator::hydrate($product,new Product());},$response->data['products']);

        dd($products[0]->title);
    }

    #[Route('/upi')]
    public function upi(IdefixMarketplaceService $idefixPlatformService)
    {
        dd($idefixPlatformService->updatePriceAndInventory(['369 VN0A36EM-M - 12827 - 44,5']));
    }
    #[Route('/sync-products')]
    public function syncProducts(IdefixMarketplaceService $idefixPlatformService)
    {
        dd($idefixPlatformService->syncProducts());
    }

    #[Route('/queue-upi')]
    public function queueUpi(QueService $queService)
    {

        $queService->add(['vendorStockCodes'=>['369 VN0A36EM-M - 12827 - 44,5']],"IdefixUpdatePriceAndInventoryProcessor");
        dd("gonderildi");
    }

    #[Route('/queue-sap')]
    public function queueSap(QueService $queService)
    {

        $queService->add(['platformCode'=>'if','vendorStockCodes'=>[]],"SyncProductsProcessor");
        dd("gonderildi");
    }

    #[Route('/search')]
    public function search(SearchService $searchService)
    {
        dd($searchService->search(['orderNumber'=>'IDE666234f2edec9']));
    }

    #[Route('/cargo/surat/send')]
    public function cargoSuratSend(SuratCargoService $cargoService, EntityManagerInterface $entityManager)
    {
        $order = $entityManager->getReference(Order::class,1);
        $cargoService->send($order);
    }
    #[Route('/order/save-purchase/{order}')]
    public function orderSavePurchase(Order $order,KorgunSoapService $korgunSoapService): void
    {
        dd($korgunSoapService->savePurchase($order));
    }


    #[Route('/order/{order}/change-state')]
    public function getAttribute(Order $order, EntityManagerInterface $entityManager): void
    {
        $order->setState('deneme12345');
        $order->setOrderNumber('orderNumber changed');
        $order->setGift(true);
        $order->setPackageNumber(123456789);
        $this->entityManager->flush();
        dd("tamam");
        exit;

    }
    #[Route('/order-audit/{audit}')]
    public function getOrderAudit(OrderAudit $audit): void
    {
        dd($audit);
        exit;

    }

    #[Route('/order-item/{orderItem}/change-state')]
    public function orderItemAudit(OrderItem $orderItem,EntityManagerInterface $entityManager) : void
    {
        $orderItem->setState('hebele-3');
        $entityManager->flush();
        dd("OK");
        exit;

    }

    #[Route('/wb2-db/{orderId}')]
    public function wb2Db(int $orderId, ManagerRegistry $doctrine, OrderRepository $orderRepository) : void
    {
        // Retrieves a repository managed by the "default" entity manager
        //$order = $orderRepository->getOrder();
        dd($orderRepository->updateStatusToInCargo('hb',"123445"));
        //dd($order);

    }


}