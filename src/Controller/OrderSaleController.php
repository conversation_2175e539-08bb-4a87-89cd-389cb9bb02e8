<?php

namespace App\Controller;

use App\FormType\OrderSaleInvoiceFormType;
use App\FormType\OrderSalePlatformPaidFormType;
use App\FormType\OrderSaleVendorPaidFormType;
use App\Repository\BrandRepository;
use App\Repository\LocationRepository;
use App\Repository\OrderAddressRepository;
use App\Repository\OrderAddressTypeRepository;
use App\Repository\PlatformRepository;
use App\Service\Cargo\CargoService;
use App\Service\Korgun\Soap\KorgunSoapService;
use App\Service\Order\OrderItemService;
use App\Service\Order\OrderItemTransitionService;
use App\Service\Order\OrderService;
use App\Service\Order\OrderTransitionService;
use App\Service\Search\SearchService;
use Symfony\Bridge\Twig\Attribute\Template;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Workflow\Exception\TransitionException;
use Wb3\SharedBundle\Controller\BaseController;
use Wb3\SharedBundle\Entity\Order;
use Wb3\SharedBundle\Entity\OrderAddressType;
use Wb3\SharedBundle\Entity\OrderItem;
use Wb3\SharedBundle\Helper\BreadcrumbItemHelper;
use Wb3\SharedBundle\Helper\FilterUtility;

#[Route('/order-sale')]
class OrderSaleController extends BaseController
{
    /**
     * Sipariş yönetimi ana sayfasını gösterir
     * 
     * Bu sayfa, filtrelenebilir ve sıralanabilir sipariş listesi sunar.
     * Siparişler durumlarına göre filtrelenebilir ve farklı kriterlere göre sıralanabilir.
     */
    #[Route('')]
    #[Template('order_sale/index.html.twig')]
    public function index(Request $request, OrderService $orderService,
                          PlatformRepository $platformRepository,
                          LocationRepository $locationRepository,
                          BrandRepository $brandRepository,
    SearchService $searchService
    ):array

    {   $page = $request->query->getInt('page', 1);
        $limit = $request->query->getInt('limit', 20);
        $content = $this->getContent();
        $filters = FilterUtility::decorateFilter($request->query->all());
        $filters['sortBy'] ??= 'orderAtAsc';
        //$orderStates = $orderService->repository->allGroupByState($filterStates);
        $saleStates = [Order::STATE_NEW,Order::STATE_INVOICED,Order::STATE_WAITING_PAYMENT]; //sale de gorunecekler.
        $filters['state'] = (isset($filters['state'])) ? $filters['state']: implode(",",$saleStates); //typesense filtrelenecek state ler
        $states =[...$saleStates,Order::STATE_CUSTOMER_CANCEL_REQUESTED,Order::STATE_DELIVERED]; //hepsi

        $sortByOptions = [
            'orderAtAsc' => 'Sipariş Tarihine Göre (Eskiden Yeniye)',
            'orderAtDesc' => 'Sipariş Tarihine Göre (Yeniden Eskiye)',
            'lastShippingAtAsc' => 'Kargo Süresine Göre (Eskiden Yeniye)',
            'lastShippingAtDesc' => 'Kargo Süresine Göre (Yeniden Eskiye)',
        ];
        $content->addBreadcrumb(new BreadcrumbItemHelper('Siparişler','app_order_index'));
        $content->addBreadcrumb(new BreadcrumbItemHelper('Satış'));

        $pagination = $searchService->allWithPaginate($filters,$page,$limit); // gecici kapali.


        unset($filters['sortBy']);
        $groupByFilters = array_merge(['q'=>$filters['q'] ?? '*','states'=>$saleStates],$filters);
        $states = $searchService->groupBy('state',$groupByFilters);
        $platforms = $searchService->groupBy('platformTitle',$groupByFilters);
        $brands = $searchService->groupBy('items.brandName',$groupByFilters);
        $locations = $searchService->groupBy('items.locationName',$groupByFilters);


        //$pagination['items'] = [$pagination['items'][0]];
            return $this->getContent()->setData([
                'filters' => $filters,
                'brands' => $brands,
                'locations' => $locations,
                'platforms' => $platforms,
                'pagination' => $pagination,
                'sortByOptions' => $sortByOptions,
                'states' => $states,
            ])->toArray();

    }
    /**
     * Fatura bilgilerini düzenleme formunu gösterir
     * 
     * Bu sayfa sipariş fatura bilgilerinin düzenlenmesi ve onaylanması için kullanılır.
     * Fatura adresi bilgileri kontrol edilebilir ve değiştirilebilir.
     */
    #[Route('/{order}/invoice-form')]
    #[Template('order_sale/invoice_form.html.twig')]
    public function invoiceForm(Request $request,
                            Order $order,
                            OrderAddressRepository $addressRepository,
                            OrderAddressTypeRepository $typeRepository): RedirectResponse|array
    {
        $content = $this->getContent();
        //modifiye edilse bile en sonuncu adresi al.
        $invoiceAddress = $addressRepository->findOneBy([
            'order' => $order->getId(),
            'type' => [OrderAddressType::ID_INVOICE_ADDRESS,OrderAddressType::ID_INVOICE_ADDRESS_MODIFIED]
        ],['id'=>'DESC']);
        $modifiedAddress = clone $invoiceAddress;
        if (!$invoiceAddress)
            throw new UnprocessableEntityHttpException('INVOICE_ADDRESS_NOT_FOUND');

        //invoice address formform
        $form = $this->createForm(OrderSaleInvoiceFormType::class, $modifiedAddress);

        $form->handleRequest($request);

        // Handle form submission
        if ($form->isSubmitted() && $form->isValid()) {
            $modifiedAddressType = $typeRepository->find(OrderAddressType::ID_INVOICE_ADDRESS_MODIFIED); // ID 3 olan adres tipini getir
            $modifiedAddress->setType($modifiedAddressType);
            $addressRepository->insert($modifiedAddress);

            $this->addFlash('success', 'Başarıyla kaydedildi.');
            return $this->redirectToRoute('app_ordersale_invoiceform', ['order' => $order->getId()]);
        }


        return $content->setData(
            [
                'order' => $order,
                'form'  => $form->createView(),
            ]
        )->toArray();
    }
    /**
     * Sipariş faturalama işlemini gerçekleştirir
     * 
     * Bu metod sipariş için faturalama işlemini başlatır ve tamamlar.
     * İşlem sonrası sipariş durumu faturalandırıldı olarak güncellenir.
     */
    #[Route('/{order}/invoice')]
    public function invoice(Order $order,
                            KorgunSoapService $korgunSoapService,
                            OrderService $orderService,
                            OrderTransitionService $transitionService
    ): RedirectResponse
    {
        $orderService->purchase($order);

        return $this->redirectToRoute('app_ordersale_cargoform', ['order' => $order->getId()]);
    }

    /**
     * Kargo işlemleri için form sayfasını gösterir
     * 
     * Bu sayfa siparişin kargo sürecinin başlatılması öncesinde
     * kargo bilgilerinin girilmesi ve kargo ödemesi ile ilgili bilgilerin
     * yönetilmesi için kullanılır.
     */
    #[Route('/{order}/cargo-form')]
    #[Template('order_sale/cargo_form.html.twig')]
    public function cargoForm(Request $request, Order $order,  FormFactoryInterface $formFactory)
    {
        $data = [];

        if($order->getPlatform()->isPlatformCargo()) { //platform
            $form = $formFactory->create(OrderSalePlatformPaidFormType::class, [], [
                'data_class' => null,
                'method' => 'post',
                'allow_extra_fields' => true,
                'csrf_protection' => false,
                'attr' => [
                    'id' => 'filter-form',
                    'class' => 'form d-flex flex-column flex-lg-row'
                ]
            ]);
        } else { //vendor

        //    if($request->get('order_sale_vendor_paid_form') !== null){
        //        $formData = $request->get('order_sale_vendor_paid_form');
        //    $data = [
        //        'invoiceSerialNumber' => $formData['invoiceSerialNumber'] ?? null,
        //        'invoiceNumber' => $formData['invoiceNumber'] ?? null,
        //    ];
        //}
            $form = $formFactory->create(OrderSaleVendorPaidFormType::class, $data, [
                'data_class' => null,
                'method' => 'post',
                'allow_extra_fields' => true,
                'csrf_protection' => false,
                'attr' => [
                    'id' => 'filter-form',
                    'class' => 'form d-flex flex-column flex-lg-row'
                ]
            ]);
        }
        $form->handleRequest($request);
        if($form->isSubmitted() && $form->isValid()) {
            //satıcı oder icin kargoya gonderilecek ve sonuc alınacak.
            return $this->redirectToRoute('app_ordersale_cargo', ['order' => $order->getId()]);
        }
        return $this->getContent()->setData([
            'order' => $order,
            'form' => $form->createView(),
        ])->toArray();
    }

    /**
     * Kargo gönderme işlemini gerçekleştirir
     * 
     * Bu metod siparişin kargo sürecini başlatır, kargo firmasına gönderilmesini sağlar
     * ve başarılı olması durumunda sipariş durumunu kargoda olarak günceller.
     * Başarısız olması durumunda hata fırlatır.
     */
    #[Route('/{order}/cargo')]
    #[Template('order_sale/cargo.html.twig')]
    public function cargo(Order $order,
                          OrderTransitionService $transitionService,
                          CargoService $cargoService,
    ): array
    {
        //state kontrol
        $transitionService->can($order,Order::TRANSITION_TO_IN_CARGO);
        //kargoyu gonder
        $result = $cargoService->send($order);

        if($result) {
            //state i atlat
            $transitionService->changeTransition($order, Order::TRANSITION_TO_IN_CARGO);
        } else {
            throw new \Exception('CARGO_INFORMATION_NOT_DELIVERED_TO_PROVIDER');
        }

        return $this->getContent()->setData([
            'order'=>$order
        ])->toArray();
    }

    #[Route('/{orderItem}/notify-price-error')]
    #[Template('order_sale/notify_price_error.html.twig')]
    public function notifyPriceError(OrderItem $orderItem,OrderItemService $service, OrderItemTransitionService $transitionService): array
    {
        //state kontrol
        if(!$transitionService->can($orderItem,OrderItem::TRANSITION_TO_PRICE_ERROR_NOTIFIED)){
            throw  new TransitionException($orderItem,OrderItem::TRANSITION_TO_PRICE_ERROR_NOTIFIED,$transitionService->orderItemStateMachine,'TRANSITION_NOT_ALLOWED');
        }

        $service->notifyPriceError($orderItem);

        $transitionService->changeTransition($orderItem,OrderItem::TRANSITION_TO_PRICE_ERROR_NOTIFIED);

        return $this->getContent()->setData([])->toArray();
    }

}