<?php

namespace App\Controller;

use Symfony\Bridge\Twig\Attribute\Template;
use Symfony\Component\Routing\Attribute\Route;
use Wb3\SharedBundle\Controller\BaseController;

class CargoController extends BaseController
{
    #[Route('/cargo')]
    #[Template('cargo/index.html.twig')]
    public function index(): array
    {
        return [];
    }

    #[Route('cargo/edit')]
    #[Template('cargo/edit/index.html.twig')]
    public function edit(): array
    {
        return [];
    }

    #[Route('cargo/add')]
    #[Template('cargo/add/index.html.twig')]
    public function add(): array
    {
        return [];
    }
}
