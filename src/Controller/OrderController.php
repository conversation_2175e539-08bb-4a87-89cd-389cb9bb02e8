<?php

namespace App\Controller;

use App\FormType\AddressChangeFormType;
use App\Repository\BrandRepository;
use App\Repository\LocationRepository;
use App\Repository\OrderAddressRepository;
use App\Repository\PlatformRepository;
use App\Repository\OrderRepository;
use App\Service\AuditService;
use App\Service\Order\OrderService;
use App\Service\QueService;
use App\Service\Search\SearchService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bridge\Twig\Attribute\Template;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Wb3\SharedBundle\Controller\BaseController;
use Wb3\SharedBundle\Entity\Order;
use Wb3\SharedBundle\Entity\OrderAudit;
use Wb3\SharedBundle\Entity\OrderItemAudit;
use Wb3\SharedBundle\Helper\BreadcrumbItemHelper;
use Wb3\SharedBundle\Helper\FilterUtility;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use Symfony\Component\HttpFoundation\ResponseHeaderBag;

#[Route('/order')]
class OrderController extends BaseController
{

    #[Route('')]
    #[Template('order/index.html.twig')]
    public function index(
        Request $request,
        SearchService $searchService ,
        OrderService $orderService,
        PlatformRepository $platformRepository,
        BrandRepository $brandRepository,
        LocationRepository$locationRepository
    )
    {
        $content = $this->getContent();
        $limit = $request->query->getInt('limit', 10);
        $page = $request->query->getInt('page', 1);
        $content = $this->getContent();
        $filters = FilterUtility::decorateFilter($request->query->all());
        $filters['sortBy'] ??= 'orderAtDesc';
        $states = [Order::STATE_NEW,Order::STATE_INVOICED,Order::STATE_IN_CARGO,Order::STATE_WAITING_PAYMENT,Order::STATE_CUSTOMER_CANCEL_REQUESTED,Order::STATE_DELIVERED];

        //if(!isset($filters['state'])) {
        //    $filters['state'] = $filterStates;
        //}
        $orderByOptions = [
            'dueDate' => 'Kargolanma süresi',
            'platform' => 'Platform',
            'oldOrders' => 'Eskiden yeniye',
            'newOrders' => 'Yeniden eskiye'
        ];

        $content->addBreadcrumb(new BreadcrumbItemHelper('Siparişler','app_order_index'));
        $content->addBreadcrumb(new BreadcrumbItemHelper('Sipariş Listesi'));

        $platforms = $platformRepository->findAll();
        $pagination = $searchService->allWithPaginate($filters,$page,$limit);
        $locations = $locationRepository->findAll();
        $brands = $brandRepository->findBy([],['name'=>'ASC']);

        return $this->getContent()->setData([
            'filters' => $filters,
            'pagination' => $pagination,
            'platforms' => $platforms,
            'states' => $states,
            'locations' => $locations,
            'brands' => $brands,
            'orderByOptions' => $orderByOptions,
            'uniqueStates'=> [],
        ])->toArray();
    }
    #[Route('/{order}/detail')]
    #[Template('order/detail.html.twig')]
    public function detail(Order $order,OrderAddressRepository $orderAddressRepository,AuditService $auditService)
    {
        $orderItemAudits = [];

        $content = $this->getContent();
        $content->addBreadcrumb(new BreadcrumbItemHelper('Siparişler','app_order_index'));
        $content->addBreadcrumb(new BreadcrumbItemHelper('Sipariş Detayı'));
        // breadcrumb

        $audits = $auditService->getWithPaginate(OrderAudit::class,$order->getId());

        foreach ($order->getItems() AS $item) {
            $orderItemAudits[] = $auditService->getWithPaginate(OrderItemAudit::class,$item->getId());
        }

        return $this->getContent()->setData([
            'order' => $order,
            'shippingAddress' => $orderAddressRepository->getShippingAddressByOrderId($order->getId()),
            'invoiceAddress' => $orderAddressRepository->getShippingAddressByOrderId($order->getId()),
            'audits' => $audits,
            'orderItemAudits' => $orderItemAudits
        ])->toArray();
    }

    #[Route('/{orderId}/re-index')]
    public function reIndex(int $orderId, QueService $queService) : Response
    {
        $queService->addToIndexOrder($orderId);

        return new Response($orderId . ' indekslenmek için kuyruğa gönderildi.');
    }

    #[Route('/personel-sales-report', name: 'app_order_personelsalesreport')]
    public function personelSalesReport(OrderService $orderService): Response
    {
        $results = $orderService->personalSaleReport();
        // Excel dosyası oluştur
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        
        // Başlıkları ekle
        $sheet->setCellValue('A1', 'Sipariş Numarası');
        $sheet->setCellValue('B1', 'İşleme Alan');
        $sheet->setCellValue('C1', 'Sipariş Tarihi');
        $sheet->setCellValue('D1', 'Fatura Tarihi');
        
        // Başlık stilini ayarla
        $sheet->getStyle('A1:D1')->getFont()->setBold(true);
        $sheet->getStyle('A1:D1')->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setARGB('FFCCCCCC');
        
        // Verileri ekle
        $row = 2;
        foreach ($results as $result) {
            $sheet->setCellValue('A1', 'Sipariş Numarası');
            $sheet->setCellValue('B1', 'İşleme Alan');
            $sheet->setCellValue('C1', 'Sipariş Tarihi');
            $sheet->setCellValue('D1', 'Fatura Tarihi');
        }
        // Sütun genişliklerini otomatik ayarla
        foreach (range('A', 'D') as $col) {
            $sheet->getColumnDimension($col)->setAutoSize(true);
        }
        
        // Excel dosyasını oluştur
        $writer = new Xlsx($spreadsheet);
        $fileName = 'Personel_Satis_Raporu_' . date('Y-m-d') . '.xlsx';
        
        $temp_file = tempnam(sys_get_temp_dir(), $fileName);
        $writer->save($temp_file);
        
        // Dosyayı indir
        return $this->file($temp_file, $fileName, ResponseHeaderBag::DISPOSITION_INLINE);
    }
}
