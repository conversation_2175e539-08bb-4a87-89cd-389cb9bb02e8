<?php

namespace App\Controller;

use Symfony\Bridge\Twig\Attribute\Template;
use Symfony\Component\Routing\Attribute\Route;
use Wb3\SharedBundle\Controller\BaseController;
use Wb3\SharedBundle\Helper\BreadcrumbItemHelper;
use App\Service\CurrencyService;

class CurrencyController extends BaseController
{
    #[Route('/base')]
    #[Template('base/base.html.twig')]
    public function index(CurrencyService $currencyService)
    {
        $content = $this->getContent();
        $content->addBreadcrumb(new BreadcrumbItemHelper('Döviz Kurları'));

        $currencies = $currencyService->getAllCurrencies(1, 50, []);

        return $content->setData([
            'currencies' => $currencies
        ])->toArray();
    }

}