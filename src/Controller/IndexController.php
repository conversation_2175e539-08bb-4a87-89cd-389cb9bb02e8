<?php

namespace App\Controller;
use App\Service\Search\SearchService;
use Symfony\Bridge\Twig\Attribute\Template;
use Symfony\Component\Routing\Attribute\Route;
use Wb3\SharedBundle\Controller\BaseController;
use Wb3\SharedBundle\Entity\Order;
use Wb3\SharedBundle\Entity\OrderItem;

class IndexController extends BaseController
{
    #[Route('/')]
    #[Template('index/index.html.twig')]
    public function index(SearchService $searchService)
    {
        //new,location_defined,price_error,price_error_defined,stock_error_storck_error_defined,ready,
        $filters['states'] = [
            Order::STATE_NEW,
            Order::STATE_INVOICED,
            Order::STATE_WAITING_PAYMENT,
            Order::STATE_UNDELIVERED,
        ];
        $states = $searchService->groupBy('state',$filters);
        $platforms = $searchService->groupBy('platformTitle',$filters);
        $brands = $searchService->groupBy('items.brandName',$filters);
        $locations = $searchService->groupBy('items.locationName',$filters);

        $latestOrder = $searchService->allWithPaginate(['sortBy'=>'orderAtDesc'],1,1);

        $latestOrder = $latestOrder['items'][0] ?? null;

        return $this->getContent()->setData([
            'states' => $states,
            'platforms' => $platforms,
            'brands' => $brands,
            'locations' => $locations,
            'latestOrder' => $latestOrder,
        ])->toArray();
    }
}