<?php

namespace App\Controller;

use Symfony\Bridge\Twig\Attribute\Template;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;
use Symfony\Component\Routing\Attribute\Route;
use Wb3\SharedBundle\Controller\BaseController;
use Wb3\SharedBundle\Entity\OrderCargo;
use Wb3\SharedBundle\Entity\OrderCargoDelivery;
use Wb3\SharedBundle\Helper\BreadcrumbItemHelper;

#[Route('/order-cargo')]
class OrderCargoController extends BaseController
{
    #[Route('/{orderCargo}', requirements: ['orderCargo' => '\d+'])]
    #[Template('order_cargo/index.html.twig')]
    public function index(OrderCargo $orderCargo): array
    {
        $content = $this->getContent();
        $content->addBreadcrumb(new BreadcrumbItemHelper('Kargo'));
        $content->addBreadcrumb(new BreadcrumbItemHelper($orderCargo->getName()));
        // breadcrumb


        return $this->getContent()->setData([
            'orderCargo' => $orderCargo,
        ])->toArray();
    }

    #[Route('/delivery/{orderCargoDelivery}')]
    public function report(OrderCargoDelivery $orderCargoDelivery): Response
    {
       $json = $orderCargoDelivery->getJson();
        return $this->render('order_cargo/report/index.html.twig',['json'=>$json]);

    }
    }
