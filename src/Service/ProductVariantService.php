<?php

namespace App\Service;

use App\Repository\ProductVariantRepository;
use Wb3\SharedBundle\Entity\ProductVariant;

class ProductVariantService
{
    public ProductVariantRepository $repository;

    public function __construct(ProductVariantRepository $repository)
    {
        $this->repository = $repository;
    }

    public function getByVendorStockCode(string $vendorStockCode) : ProductVariant | null
    {
        return $this->repository->findOneBy(['vendorStockCode' => $vendorStockCode]);
    }
}