<?php

namespace App\Service\Cargo;

use App\Service\Cargo\Surat\SuratCargoService;
use Wb3\SharedBundle\Entity\Order;
use Wb3\SharedBundle\Entity\OrderCargo;

/**
 * Kargo işlemlerini yöneten servis
 * 
 * Bu servis, siparişlerin kargo firmalarına gönderilmesi, 
 * kargo takip numaralarının oluşturulması ve kargo durumlarının
 * izlenmesi gibi kargo süreçlerini yönetir.
 */
class CargoService
{
    /**
     * CargoService yapıcı metodu
     * 
     * Kargo servisleri bağımlılıklarını oluşturur.
     * 
     * @param SuratCargoService $suratCargoService Sürat Kargo servisi
     */
    public function __construct(
        private readonly SuratCargoService $suratCargoService
    ){}
    
    /**
     * Siparişi kargo firmasına gönderir
     * 
     * Siparişin kargo bilgilerine göre uygun kargo servisi ile
     * sipariş gönderim işlemini başlatır.
     * 
     * @param Order $order Gönderilecek sipariş
     * @return bool İşlem başarılı ise true, başarısız ise false
     */
    public function send(Order $order): bool
    {
        $cargo = $order->getCargo();

        //Sura kargo ile gonder
        if($cargo->getId() == OrderCargo::ID_SURAT) {
            return $this->suratCargoService->send($order);
        }
        return true; /** @TODO Kargo servisleri eklenecek */
    }

}