<?php

namespace App\Service\Cargo\Surat;

use App\Repository\OrderAddressRepository;
use App\Service\Cargo\ICargo;
use App\Type\Cargo\Surat\GonderiType;
use App\Type\Cargo\Surat\SendRequestType;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use Wb3\SharedBundle\Entity\Order;
use Wb3\SharedBundle\Helper\ObjectHydrator;
use Wb3\SharedBundle\Helper\StringUtility;

class SuratCargoService implements ICargo
{
    public function __construct(
        private  readonly  SuratCargoClient $client,
        private readonly ValidatorInterface $validator,
        private readonly OrderAddressRepository $orderAddressRepository
    )
    {
    }

    //$platform, $orderNumber, $quantity, $address, $city, $district, $serialNo, $invoiceNo, $cashOnDeliveryCost = 0, $phone = '', $email = '',$name='N/A'
    public function send(Order $order,float $cashOnDeliveryCost=0): bool
    {
        $shippingAddress = $this->orderAddressRepository->getShippingAddressByOrderId($order->getId());
        $orderInvoice = $order->getInvoice();

        if(!$orderInvoice) {
            throw  new UnprocessableEntityHttpException('ORDER_INVOICE_NOT_FOUND');
        }

        $name = StringUtility::makeWordstoURL($shippingAddress->getFistName()." ".$shippingAddress->getLastName());
        $city = StringUtility::makeWordstoURL($shippingAddress->getCity());
        $district = StringUtility::makeWordstoURL($shippingAddress->getTown());

        $gonderiType = new GonderiType();
        $gonderiType->KisiKurum = ucwords($name);
        $gonderiType->SahisBirim = '';
        $gonderiType->AliciAdresi = $shippingAddress->getAddress();
        $gonderiType->Il = $city;
        $gonderiType->Ilce = $district;
        $gonderiType->TelefonEv = '';
        $gonderiType->TelefonIs = '';
        $gonderiType->TelefonCep = '';
        $gonderiType->Email = $shippingAddress->getOrder()->getCustomer()->getMail();
        $gonderiType->AliciKodu = '';
        $gonderiType->KargoTuru = 3;
        $gonderiType->OdemeTipi = 1;
        $gonderiType->IrsaliyeSeriNo = substr($orderInvoice->getInvoiceNumber(),0,3); //@TODO Fatura Seni NO;
        $gonderiType->IrsaliyeSiraNo = substr($orderInvoice->getInvoiceNumber(),3); //@TODO Fatura InvoiceNo;
        $gonderiType->ReferansNo = '';
        $gonderiType->OzelKargoTakipNo = $order->getPlatform()->getCode() . $order->getOrderNumber();
        $gonderiType->Adet = count($order->getItems());
        $gonderiType->BirimDesi = 1;
        $gonderiType->BirimKg = 1;
        $gonderiType->KargoIcerigi = '';
        $gonderiType->KapidanOdemeTahsilatTipi = ((double)str_replace(',', '.', $cashOnDeliveryCost)) > 0 ? 1 : 0;
        $gonderiType->KapidanOdemeTutari = ((double)str_replace(',', '.', $cashOnDeliveryCost)) > 0 ? ((double)str_replace(',', '.', $cashOnDeliveryCost)) : 0;
        $gonderiType->EkHizmetler = '';
        $gonderiType->SevkAdresi = '';
        $gonderiType->TeslimSekli = 1;
        $gonderiType->TasimaSekli = 1;
        $gonderiType->GonderiSekli = 0;
        $gonderiType->TeslimSubeKodu = '';
        $gonderiType->Pazaryerimi = 0;
        $gonderiType->EntegrasyonFirmasi = '';
        $gonderiType->Iademi = 0;

        $reqType = new SendRequestType();
        $reqType->KullaniciAdi = $cashOnDeliveryCost > 0 ? $_ENV['CARGO_SURAT_AOK_USER'] : $_ENV['CARGO_SURAT_USER'];
        $reqType->Sifre = $cashOnDeliveryCost > 0 ? $_ENV['CARGO_SURAT_AOK_PASS'] : $_ENV['CARGO_SURAT_PASS'];
        $reqType->Gonderi = $gonderiType;

        $errors = $this->validator->validate($reqType);

        if (count($errors) > 0) {
            $errorsString = (string) $errors;
            dd($errorsString);
        }

        $reqTypeArray =  ObjectHydrator::normalize($reqType);

        dd($reqTypeArray);

        $request = $this->client->call('GonderiyiKargoyaGonderYeni',$reqTypeArray); //@TODO test edilecek.

        return !empty($request->GonderiyiKargoyaGonderYeniResult);

        //$parameters = [
        //    'KullaniciAdi' => $cashOnDeliveryCost > 0 ? $_ENV['CARGO_SURAT_AOK_USER'] : $_ENV['CARGO_SURAT_USER'],
        //    'Sifre' => $cashOnDeliveryCost > 0 ? $_ENV['CARGO_SURAT_AOK_PASS'] : $_ENV['CARGO_SURAT_PASS'],
        //    'Gonderi' => [
        //        'KisiKurum' => ucwords($name),
        //        'SahisBirim' => '',
        //        'AliciAdresi' => $shippingAddress->getAddress(),
        //        'Il' => $city,
        //        'Ilce' => $district,
        //        'TelefonEv' => '',
        //        'TelefonIs' => '',
        //        'TelefonCep' => $shippingAddress->getPhone(),
        //        'Email' => $shippingAddress->getOrder()->getCustomer()->getMail(),
        //        'AliciKodu' => '',
        //        'KargoTuru' => 3,
        //        'OdemeTipi' => 1,
        //        'IrsaliyeSeriNo' => substr($orderInvoice->getInvoiceNumber(),0,3), //@TODO Fatura Seni NO
        //        'IrsaliyeSiraNo' => substr($orderInvoice->getInvoiceNumber(),3), //@TODO Fatura InvoiceNo
        //        'ReferansNo' => '',
        //        'OzelKargoTakipNo' => $order->getPlatform()->getCode() . $order->getOrderNumber(),
        //        'Adet' => count($order->getItems()),
        //        'BirimDesi' => 1,
        //        'BirimKg' => 1,
        //        'KargoIcerigi' => '',
        //        'KapidanOdemeTahsilatTipi' => ((double)str_replace(',', '.', $cashOnDeliveryCost)) > 0 ? 1 : 0,
        //        'KapidanOdemeTutari' => ((double)str_replace(',', '.', $cashOnDeliveryCost)) > 0 ? ((double)str_replace(',', '.', $cashOnDeliveryCost)) : 0,
        //        'EkHizmetler' => '',
        //        'SevkAdresi' => '',
        //        'TeslimSekli' => 1,
        //        'TasimaSekli' => 1,
        //        //'BayiNo' => '',
        //        //'EntegrasyonId' => '',
        //        //'EntegrasyonHesaplamaTuru' => '',
        //        'GonderiSekli' => 0, //0 Standart gönderi, 5 Bukoli, 8 Pudo
        //        'TeslimSubeKodu' => '', //Sürat Kargo Teslim Noktasını (Şubeleri) ifade etmektedir.
        //        'Pazaryerimi' => 0, //1 Pazaryeri ise, 0 Pazaryeri değil ise
        //        'EntegrasyonFirmasi' => '',
        //        'Iademi' => 0, //0 Standart gönderi, 1 İade
        //    ]
       // ];
    }
}