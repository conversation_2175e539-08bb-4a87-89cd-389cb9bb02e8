<?php

namespace App\Service\Cargo\Surat;


use App\Service\SoapApiService;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;

class SuratCargoClient  extends SoapApiService{

    public function __construct(LoggerInterface $logger, TokenStorageInterface $tokenStorage, ParameterBagInterface $parameter)
    {
        parent::__construct( $logger, $tokenStorage, $parameter);
        $this->apiEndpoint = $_ENV['CARGO_SURAT_GONDERI_API_URL'];
        $this->setHeaders(['Content-Type' => 'text/xml']);
    }
}
