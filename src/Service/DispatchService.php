<?php

namespace App\Service;

use App\Service\Order\OrderAddressService;
use Bulut\eFaturaUBL\XMLHelper;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;
use Wb3\SharedBundle\Entity\Order;
use Wb3\SharedBundle\Entity\OrderItem;
use Wb3\SharedBundle\Entity\Platform;

class DispatchService
{

    public function __construct(
        private readonly  OrderAddressService $orderAddressService,
        private readonly ForibaService $foribaService,
    ){}

    public function sendDispatch(Order $order): string
    {
        $uuid = XMLHelper::CreateGUID();
        $dispatch = $this->createDispatch($uuid, $order);
        try {
            $response = $this->foribaService->sendDispatch($uuid,$dispatch);
        } catch (\Exception $e) {
            throw new UnprocessableEntityHttpException('FORIBA_DESPATCH_CANNOT_PROCCESS Message:'.$e->getMessage());
        }
        return $response[0]->getUUID();
}

private function createDispatch(string $uuid, Order $order): string
{
    if(in_array($order->getPlatform()->getCode(),[Platform::CODE_TRENDYOL,Platform::CODE_TRENDYOL_ALI_SOLMAZ])) {
        return $this->createDispatchForTY($uuid, $order);
    }
        throw new UnprocessableEntityHttpException('DISPATCH_NOT_IMPLEMENTED_YET_FOR_THIS_PLATFORM');
}
    private function createDispatchForTY(string $uuid, Order $order): string
    {
        $extras = ['note'=>'Mikro ihracat satışıdır.'];
        $shipmentAddress = $this->orderAddressService->getShipmentAddress($order->getId());
            $xml = '<DespatchAdvice xmlns="urn:oasis:names:specification:ubl:schema:xsd:DespatchAdvice-2" 
                xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2" 
                xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2" 
                xmlns:ds="http://www.w3.org/2000/09/xmldsig#" xmlns:ext="urn:oasis:names:specification:ubl:schema:xsd:CommonExtensionComponents-2" xmlns:xades="http://uri.etsi.org/01903/v1.3.2#" 
                xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="urn:oasis:names:specification:ubl:schema:xsd:DespatchAdvice-2 ..\xsdrt\maindoc\UBL-DespatchAdvice-2.1.xsd">
	<cbc:UBLVersionID>2.1</cbc:UBLVersionID>
	<cbc:CustomizationID>TR1.2</cbc:CustomizationID>
	<cbc:ProfileID>TEMELIRSALIYE</cbc:ProfileID>
	<cbc:CopyIndicator>false</cbc:CopyIndicator>
	<cbc:UUID>' . $uuid . '</cbc:UUID>
	<cbc:IssueDate>' . date("Y-m-d") . '</cbc:IssueDate>
	<cbc:IssueTime>' . date('H:i:s') . '</cbc:IssueTime>
	<cbc:DespatchAdviceTypeCode>SEVK</cbc:DespatchAdviceTypeCode>
	<cbc:Note>'.$extras['note'] .'</cbc:Note>
	<cac:OrderReference>
		<cbc:ID>' . $order->getOrderNumber() . '</cbc:ID>
		<cbc:IssueDate>' . date("Y-m-d") . '</cbc:IssueDate>
	</cac:OrderReference>
	    <cac:AdditionalDocumentReference>
        <cbc:ID>' . time() . '</cbc:ID>
        <!-- Customer Despatch Number (only if ID is autogenerated and not present in this XML) -->
        <cbc:IssueDate>' . date("Y-m-d") . '</cbc:IssueDate>
        <cbc:DocumentTypeCode>CUST_DES_ID</cbc:DocumentTypeCode>
        <cbc:DocumentType>CUST_DES_ID</cbc:DocumentType>
    </cac:AdditionalDocumentReference>
	<cac:DespatchSupplierParty>
		<cac:Party>
			<cbc:WebsiteURI>http://www.ayakkabionline.com</cbc:WebsiteURI>
			<cac:PartyIdentification>
				<cbc:ID schemeID="VKN">6490430026</cbc:ID>
			</cac:PartyIdentification>
			<cac:PartyName>
				<cbc:Name>Otto Solmaz Ayakkabı Deri Ür. Tur. San. Dış Tic. Dnş. Ltd. Şti.</cbc:Name>
			</cac:PartyName>
			<cac:PostalAddress>
				<cbc:ID>17000</cbc:ID>
				<cbc:StreetName>Barbaros Mahallesi Troya Caddesi</cbc:StreetName>
				<cbc:BuildingNumber>40-42</cbc:BuildingNumber>
				<cbc:CitySubdivisionName>Merkez</cbc:CitySubdivisionName>
				<cbc:CityName>Çanakkale</cbc:CityName>
				<cbc:PostalZone>17000</cbc:PostalZone>
				<cac:Country>
					<cbc:Name>Türkiye</cbc:Name>
				</cac:Country>
			</cac:PostalAddress>
			<!--<cac:PhysicalLocation>
				<cbc:ID>Depo Şube</cbc:ID>
				<cac:Address>
					<cbc:ID>1234567895</cbc:ID>
					<cbc:StreetName>Ihlamur Mahallesi Selvi Caddesi Sedir Sokak</cbc:StreetName>
					<cbc:BuildingNumber>79/A</cbc:BuildingNumber>
					<cbc:CitySubdivisionName>Balgat</cbc:CitySubdivisionName>
					<cbc:CityName>Ankara</cbc:CityName>
					<cbc:PostalZone>06800</cbc:PostalZone>
					<cac:Country>
						<cbc:Name>Türkiye</cbc:Name>
					</cac:Country>
				</cac:Address>
			</cac:PhysicalLocation> -->
			<cac:PartyTaxScheme>
				<cac:TaxScheme>
					<cbc:Name>Çanakkale</cbc:Name>
				</cac:TaxScheme>
			</cac:PartyTaxScheme>
			<cac:Contact>
				<cbc:Telephone>(*************</cbc:Telephone>
				<cbc:Telefax>(286) 2170545</cbc:Telefax>
				<cbc:ElectronicMail><EMAIL></cbc:ElectronicMail>
			</cac:Contact>
		</cac:Party>
		<cac:DespatchContact>
			<cbc:Name>Esra Camisap</cbc:Name>
		</cac:DespatchContact>
	</cac:DespatchSupplierParty>
	<cac:DeliveryCustomerParty>
		<cac:Party>
			<!--<cbc:WebsiteURI>http://www.bbb.com.tr/</cbc:WebsiteURI> -->
			<cac:PartyIdentification>
				<cbc:ID schemeID="VKN">5555555555</cbc:ID> <!-- calisan yazan: 9205121120 -->
			</cac:PartyIdentification>
			<!--<cac:PartyIdentification>
				<cbc:ID schemeID="MUSTERINO">*********</cbc:ID>
			</cac:PartyIdentification> -->
			<cac:PartyName>
				<cbc:Name>' . $shipmentAddress->getCompany() . '</cbc:Name>
			</cac:PartyName>
			<cac:PostalAddress>
				<cbc:StreetName>' . $shipmentAddress->getAddress() . '</cbc:StreetName>
				<cbc:CitySubdivisionName>' . $shipmentAddress->getTown() . '</cbc:CitySubdivisionName>
				<cbc:CityName>' . $shipmentAddress->getCity() . '</cbc:CityName>
				<cbc:PostalZone/>
				<cac:Country>
					<cbc:Name>'.$shipmentAddress->getCountry().'</cbc:Name>
				</cac:Country>
			</cac:PostalAddress>
			<!--<cac:PartyTaxScheme>
				<cac:TaxScheme>
					<cbc:Name>Çankaya</cbc:Name>
				</cac:TaxScheme>
			</cac:PartyTaxScheme>-->
			<!--cac:Contact>
				<cbc:Telephone></cbc:Telephone>
				<cbc:Telefax>(*************</cbc:Telefax>
				<cbc:ElectronicMail></cbc:ElectronicMail>
			</cac:Contact-->
		</cac:Party>
	</cac:DeliveryCustomerParty>
	<cac:BuyerCustomerParty>
		<cac:Party>
			<!--<cac:PartyIdentification>
				<cbc:ID schemeID="VKN">2650179910</cbc:ID>
			</cac:PartyIdentification>-->
			<cac:PartyIdentification>
				<cbc:ID schemeID="VKN">5555555555</cbc:ID>
			</cac:PartyIdentification>
			<cac:PartyName>
				<cbc:Name>' .$shipmentAddress->getCompany() . '</cbc:Name>
			</cac:PartyName>>
			<cac:PostalAddress>
				<cbc:ID>1234547810</cbc:ID>
				<cbc:StreetName>Papatya Mahallesi Çınar Caddesi Çam Sokak</cbc:StreetName>
				<cbc:BuildingNumber>74/A</cbc:BuildingNumber>
				<cbc:CitySubdivisionName>Balgat</cbc:CitySubdivisionName>
				<cbc:CityName>Ankara</cbc:CityName>
				<cbc:PostalZone>06100</cbc:PostalZone>
				<cac:Country>
					<cbc:Name>Türkiye</cbc:Name>
				</cac:Country>
			</cac:PostalAddress>
			<!--cac:PartyTaxScheme>
				<cac:TaxScheme>
					<cbc:Name>Çankaya</cbc:Name>
				</cac:TaxScheme>
			</cac:PartyTaxScheme>
			<cac:Contact>
				<cbc:Telephone>(*************</cbc:Telephone>
				<cbc:Telefax>(*************</cbc:Telefax>
				<cbc:ElectronicMail><EMAIL></cbc:ElectronicMail>
			</cac:Contact-->
		</cac:Party>
	</cac:BuyerCustomerParty>
	<cac:SellerSupplierParty>
		<cac:Party>
		<cac:PartyIdentification>
				<cbc:ID schemeID="VKN">6490430026</cbc:ID>
			</cac:PartyIdentification>
			<!--cac:PartyIdentification>
				<cbc:ID schemeID="MUSTERINO">*********</cbc:ID>
			</cac:PartyIdentification-->
			<cac:PartyName>
				<cbc:Name>Otto Solmaz Ayakkabı Deri Ür. Tur. San. Dış Tic. Dnş. Ltd. Şti.</cbc:Name>
			</cac:PartyName>
			<cac:PostalAddress>
				<cbc:ID>1234527790</cbc:ID>
				<cbc:StreetName>Ada Mahallesi Meşe Caddesi Gül Sokak</cbc:StreetName>
				<cbc:BuildingNumber>73/A</cbc:BuildingNumber>
				<cbc:CitySubdivisionName>Çukurambar</cbc:CitySubdivisionName>
				<cbc:CityName>Ankara</cbc:CityName>
				<cbc:PostalZone>06100</cbc:PostalZone>
				<cac:Country>
					<cbc:Name>Türkiye</cbc:Name>
				</cac:Country>
			</cac:PostalAddress>
			<!--cac:PartyTaxScheme>
				<cac:TaxScheme>
					<cbc:Name>Çankaya</cbc:Name>
				</cac:TaxScheme>
			</cac:PartyTaxScheme>
			<cac:Contact>
				<cbc:Telephone>(*************</cbc:Telephone>
				<cbc:Telefax>(*************</cbc:Telefax>
				<cbc:ElectronicMail><EMAIL></cbc:ElectronicMail>
			</cac:Contact-->
		</cac:Party>
	</cac:SellerSupplierParty>
	<!--<cac:OriginatorCustomerParty>
		<cac:Party>
			<cbc:WebsiteURI>http://www.eee.com.tr/</cbc:WebsiteURI>
			<cac:PartyIdentification>
				<cbc:ID schemeID="VKN">9205121283</cbc:ID>
			</cac:PartyIdentification>
			<cac:PartyIdentification>
				<cbc:ID schemeID="MUSTERINO">*********</cbc:ID>
			</cac:PartyIdentification>
			<cac:PartyName>
				<cbc:Name>EEE Limited Şirketi</cbc:Name>
			</cac:PartyName>
			<cac:PostalAddress>
				<cbc:ID>1534566790</cbc:ID>
				<cbc:StreetName>Sümer Mahallesi Kale Caddesi Başak Sokak</cbc:StreetName>
				<cbc:BuildingNumber>72/A</cbc:BuildingNumber>
				<cbc:CitySubdivisionName>Küçükesat</cbc:CitySubdivisionName>
				<cbc:CityName>Ankara</cbc:CityName>
				<cbc:PostalZone>06100</cbc:PostalZone>
				<cac:Country>
					<cbc:Name>Türkiye</cbc:Name>
				</cac:Country>
			</cac:PostalAddress>
			<cac:PartyTaxScheme>
				<cac:TaxScheme>
					<cbc:Name>Çankaya</cbc:Name>
				</cac:TaxScheme>
			</cac:PartyTaxScheme>
			<cac:Contact>
				<cbc:Telephone>(*************</cbc:Telephone>
				<cbc:Telefax>(*************</cbc:Telefax>
				<cbc:ElectronicMail><EMAIL></cbc:ElectronicMail>
			</cac:Contact>
		</cac:Party>
	</cac:OriginatorCustomerParty>-->
	<cac:Shipment>
		<cbc:ID>0</cbc:ID>
		<cac:GoodsItem>
			<cbc:ValueAmount currencyID="TRY">' . $order->getTotalPrice() . '</cbc:ValueAmount>
		</cac:GoodsItem>
	<cac:Delivery>
      <cac:DeliveryAddress>
        <cbc:StreetName>' . $shipmentAddress->getAddress() . '</cbc:StreetName>
        <cbc:CitySubdivisionName>' . $shipmentAddress->getTown() . '</cbc:CitySubdivisionName>
        <cbc:CityName>' . $shipmentAddress->getCity() . '</cbc:CityName>
        <cbc:PostalZone>11111</cbc:PostalZone>
        <cac:Country>
          <cbc:Name>'.$shipmentAddress->getCountry().'</cbc:Name>
        </cac:Country>
      </cac:DeliveryAddress>
      <cac:CarrierParty>
        <cbc:WebsiteURI />
        <cac:PartyIdentification>
          <cbc:ID schemeID="VKN">6490430026</cbc:ID>
        </cac:PartyIdentification>
        <cac:PartyName>
          <cbc:Name>otto solmaz</cbc:Name>
        </cac:PartyName>
        <cac:PostalAddress>
          <cbc:Room />
          <cbc:StreetName />
          <cbc:BuildingName />
          <cbc:BuildingNumber />
          <cbc:CitySubdivisionName />
          <cbc:CityName />
          <cbc:PostalZone />
          <cbc:Region />
          <cbc:District />
          <cac:Country>
            <cbc:Name />
          </cac:Country>
        </cac:PostalAddress>
        <cac:PartyTaxScheme>
          <cac:TaxScheme>
            <cbc:Name />
            <cbc:TaxTypeCode>0</cbc:TaxTypeCode>
          </cac:TaxScheme>
        </cac:PartyTaxScheme>
        <cac:Contact>
          <cbc:Telephone />
          <cbc:Telefax />
          <cbc:ElectronicMail />
        </cac:Contact>
      </cac:CarrierParty>
      <cac:Despatch>
        <cbc:ActualDespatchDate>2024-01-20</cbc:ActualDespatchDate>
        <cbc:ActualDespatchTime>12:05:00.7460000+03:00</cbc:ActualDespatchTime>
      </cac:Despatch>
    </cac:Delivery>
		<!--cac:TransportHandlingUnit>
			<cac:TransportEquipment>
				<cbc:ID schemeID="DORSEPLAKA">06DR4088</cbc:ID>
			</cac:TransportEquipment>
		</cac:TransportHandlingUnit-->
	</cac:Shipment>
	<!-- <cac:DespatchLine>
		<cbc:ID>1</cbc:ID>
		<cbc:DeliveredQuantity unitCode="C62">20</cbc:DeliveredQuantity>
		<cac:OrderLineReference>
			<cbc:LineID>1</cbc:LineID>
		</cac:OrderLineReference>
		<cac:Item>
			<cbc:Name>Masa Üstü Bilgisayar</cbc:Name>
			<cac:SellersItemIdentification>
				<cbc:ID>PNC1234</cbc:ID>
			</cac:SellersItemIdentification>
		</cac:Item>
		<cac:Shipment>
			<cbc:ID/>
			<cac:GoodsItem>
				<cac:InvoiceLine>
					<cbc:ID/>
					<cbc:InvoicedQuantity>0</cbc:InvoicedQuantity>
					<cbc:LineExtensionAmount currencyID="TRY">20000</cbc:LineExtensionAmount>
					<cac:Item>
						<cbc:Name/>
					</cac:Item>
					<cac:Price>
						<cbc:PriceAmount currencyID="TRY">1000</cbc:PriceAmount>
					</cac:Price>
				</cac:InvoiceLine>
			</cac:GoodsItem>
		</cac:Shipment>
	</cac:DespatchLine> -->
	<!-- order items is here -->
	' . $this->getDespatchLines($order) . '
</DespatchAdvice>';

        return $xml;
    }

    private function getDespatchLines(Order $order)
    {
        $despatchLineText = null;

        foreach ($order->getItems() AS $orderItem) {
            $orderItemGroups[$orderItem->getProductVariant()->getStockCode()] = ['item'=>null,'qty'=>null];
            $orderItemGroups[$orderItem->getProductVariant()->getStockCode()]['item'] = $orderItem;
            $orderItemGroups[$orderItem->getProductVariant()->getStockCode()]['qty'] += 1;
        }

        $i=0;
        foreach ($orderItemGroups AS  $orderItemGroup) {
            $lineNo = $i + 1;
            /** @var OrderItem $orderItem */
            $orderItem = $orderItemGroup['item'];
            $despatchLineText .= "<cac:DespatchLine>
		<cbc:ID>{$lineNo}</cbc:ID>
		<cbc:DeliveredQuantity unitCode=\"C62\">{$orderItemGroup['qty']}</cbc:DeliveredQuantity>
		<cac:OrderLineReference>
			<cbc:LineID>{$lineNo}</cbc:LineID>
		</cac:OrderLineReference>
		<cac:Item>
			<cbc:Name>{$orderItem->getProductVariant()->getProduct()->getTitle()} </cbc:Name>
			<cac:SellersItemIdentification>
				<cbc:ID/>
			</cac:SellersItemIdentification>
		</cac:Item>
		<cac:Shipment>
			<cbc:ID/>
			<cac:GoodsItem>
				<cac:InvoiceLine>
					<cbc:ID/>
					<cbc:InvoicedQuantity>{$lineNo}</cbc:InvoicedQuantity>
					<cbc:LineExtensionAmount currencyID=\"TRY\">{$orderItem->getPrice()}</cbc:LineExtensionAmount>
					<cac:Item>
						<cbc:Name/>
					</cac:Item>
					<cac:Price>
						<cbc:PriceAmount currencyID=\"TRY\">{$orderItem->getPrice()}</cbc:PriceAmount>
					</cac:Price>
				</cac:InvoiceLine>
			</cac:GoodsItem>
		</cac:Shipment>
	</cac:DespatchLine>";
            $i++;
        }
        return $despatchLineText;
    }
}