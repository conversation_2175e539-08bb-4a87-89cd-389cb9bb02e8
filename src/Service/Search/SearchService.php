<?php

namespace App\Service\Search;

use App\Repository\OrderRepository;
use Doctrine\ORM\QueryBuilder;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;
use Wb3\SharedBundle\Entity\Order;
use Wb3\SharedBundle\Entity\User;

/**
 * Arama işlemlerini gerçekleştiren servis
 * 
 * Bu servis, sistem genelinde arama işlemlerini kolaylaştırmak ve
 * standartlaştırmak için kullanılır. Özellikle sipariş ve sipariş
 * kalemleri üzerinde çeşitli kriterlere göre arama yapmayı sağlar.
 */
class SearchService
{
    /**
     * Sipariş alt öğe sorgu isimleri 
     * 
     * Sipariş alt öğelerinde arama yaparken kullanılacak alanlar
     */
    CONST ORDER_SUB_ITEM_QUERY_NAMES = ['brandName','locationCode'];
    
    /**
     * SearchService yapıcı metodu
     * 
     * TypeSense API servisi bağımlılığını oluşturur.
     * 
     * @param TypeSenseApiService $apiService TypeSense API servisi
     */
    public function __construct(
        private readonly  TypeSenseApiService $apiService,
        private readonly  OrderRepository $orderRepository,
        private readonly  Security $security,
    )
    {
    }
    
    /**
     * Sayfalandırılmış arama sonuçlarını döndürür
     * 
     * Belirtilen filtrelere göre siparişleri arar ve sonuçları sayfalandırır.
     * TypeSense arama motorunu kullanarak hızlı arama sağlar.
     * 
     * @param array $filters Uygulanacak filtreler
     * @param int $page Sayfa numarası
     * @param int $limit Sayfa başına kayıt sayısı
     * @return array Sayfalandırılmış arama sonuçları
     */
    public function allWithPaginate(array $filters = [], int $page = 1, $limit = 10): array
    {
        $orderIds = $facets = [];
        $parameters = ['q'=>$filters['q'] ?? '*','limit'=>$limit,'page'=>$page];
        $parameters['query_by'] = '*';
        $facets = $this->setFilter($filters);
        $parameters['sort_by'] = $this->setSortBy($filters['sortBy']);
        $url = "/collections/order/documents/search";
        $filterBy = [];
        foreach($facets AS $key => $value){
            if(in_array($key,self::ORDER_SUB_ITEM_QUERY_NAMES)) {
                $filterBy[] = "items.$key:$value";
            } else {
                $filterBy[] = "$key:=$value";
            }
        }
        $filterBy = implode("&&", $filterBy);
        $parameters['filter_by'] = $filterBy ?? '*';
        $parameters['num_typos'] = 0;
        $parameters['prefix'] = false;
        /**
         * 'match_type' => 'exact', // Tam eşleşme
         * 'prefix' => 'false', // Önek araması kapalı
         * 'num_typos' => 0 // Yazım hatası toleransı yok
        */

        $response = $this->apiService->call('GET', $url,[],$parameters);
    
        foreach($response->data['hits'] AS $order) {
            $orderIds[] = $order['document']['id'];
        }

        $orders = $this->orderRepository->getByIds($orderIds);

        //id ye gore sırala
        usort($orders, function ($a, $b) use ($orderIds) {
            $aIndex = array_search($a->getId(), $orderIds);
            $bIndex = array_search($b->getId(), $orderIds);

            return $aIndex <=> $bIndex;
        });
    
        return [
            'totalCount' => $response->data['found'],//Toplam Satır Sayısı
            'itemCount' => count($orderIds), // Response Dönen Satır Sayısı
            'pageCount' => ceil($response->data['found']/$limit), // Toplam Sayfa Sayısı
            'currentPage' => $response->data['page'], // Şuanki Sayfa
            'limit' => $limit, // items da olması beklenen satır sayısı
            'items' => $orders, // Satır Listesi
        ];
    }


    public function groupBy(string $groupField, array $filters = []) : array
    {
        $result = [];
        $parameters['group_by'] = $groupField;
        $parameters['q'] = '*';
        $url = "/collections/order/documents/search";
        $response = $this->apiService->call('GET', $url,[],$parameters);

        foreach($response->data['grouped_hits'] AS $hit) {
            $data = ['key'=>$hit['group_key'][0],'count'=>$hit['found']];
            $result[] = $data;
        }

        return $result;
    }
    
    /**
     * Filtreleri TypeSense sorgu formatına dönüştürür
     * 
     * Gelen filtreleri TypeSense'in anlayacağı facet formatına dönüştürür.
     * 
     * @param array $filters Dönüştürülecek filtreler
     * @return array TypeSense facet formatında filtreler
     */
    private function setFilter(array $filters): array
    {
        $facets = [];

        if(isset($filters['locationCode'])) {
            $facets['items.locationCode'] = $filters['locationCode'];
        }
        if(isset($filters['platformCode'])) {
            $facets['platformCode'] = $filters['platformCode'];
        }
        if(isset($filters['brandName'])) {
            $facets['brandName'] = $filters['brandName'];
        }


        if(isset($filters['state'])) {
            $facets['state'] = "[".$filters['state']."]";
        }

        return $facets;
    }

    private function setSortBy(string $sortBy)
    {

        if($sortBy == 'orderAtAsc') {
            return 'orderAt:asc';
        }
        if($sortBy == 'orderAtDesc') {
            return 'orderAt:desc';
        }
        if($sortBy == 'lastShippingAtAsc') {
            return 'lastShippingAt:asc';
        }
        if($sortBy == 'lastShippingAtDesc') {
            return 'lastShippingAt:desc';
        }
    }

    public function filterItemByStateWithPaginate(array $filters, int $page, int $limit)
    {
        $orderIds = [];
        $parameters = ['q'=>$filters['q'] ?? '*','limit'=>$limit,'page'=>$page];
        $parameters['query_by'] = '*';
        $facets = $this->setFilter($filters);
        $parameters['sort_by'] = $this->setSortBy($filters['sortBy']);
        $url = "/collections/order/documents/search";
        $filterBy = [];
        foreach($facets AS $key => $value){
                $filterBy[] = "$key:=$value";
        }
        if(isset($filters['orderStates']) and count($filters['orderStates'])) {
            $filterBy[] = "state:=[".implode(",",$filters['orderStates'])."]";
        }
        if(isset($filters['itemState']))
            $filterBy[] = "items.state:=".$filters['itemState'];
            else
        $filterBy[] = "items.state:=[".implode(",",$filters['itemStates'])."]";

        if(isset($filters['locationCodes']) and count($filters['locationCodes'])) {
            $filterBy[] = "items.locationCode:=[" . implode(',', $filters['locationCodes']) . "]";
        }
        $filterBy = implode("&&", $filterBy);
        $parameters['filter_by'] = $filterBy ?? '*';




        $response = $this->apiService->call('GET', $url,[],$parameters);

        foreach($response->data['hits'] AS $order) {
            $orderIds[] = $order['document']['id'];
        }

        $orders = $this->orderRepository->getByIds($orderIds,$filters['locationCodes'] ?? [],$filters['itemStates'] ?? []);

        //id ye gore sırala
        usort($orders, function ($a, $b) use ($orderIds) {
            $aIndex = array_search($a->getId(), $orderIds);
            $bIndex = array_search($b->getId(), $orderIds);

            return $aIndex <=> $bIndex;
        });

        return [
            'totalCount' => $response->data['found'],//Toplam Satır Sayısı
            'itemCount' => count($orderIds), // Response Dönen Satır Sayısı
            'pageCount' => ceil($response->data['found']/$limit), // Toplam Sayfa Sayısı
            'currentPage' => $response->data['page'], // Şuanki Sayfa
            'limit' => $limit, // items da olması beklenen satır sayısı
            'items' => $orders, // Satır Listesi
        ];
    }


    /**
     * rivate function setFilters(array $filters, QueryBuilder $qb): QueryBuilder
     * {
     * // Filter by 'q' for customer name, stock code, order number, or product name
     * if (!empty($filters['q'])) {
     * $qb->innerJoin('o.customer', 'oii')
     * ->join('o.orderItems', 'oi')
     * ->join('oi.productVariant', 'pv')
     * ->join('pv.product', 'p')
     * ->leftJoin('p.attributeValueProducts', 'avp')
     * ->leftJoin('avp.productAttribute', 'pa')
     * ->leftJoin('avp.productAttributeValue', 'pav');
     * $qb->andWhere('oii.name LIKE :q OR p.skuColor LIKE :q OR o.orderNumber LIKE :q OR pav.name LIKE :q')
     * ->setParameter('q', '%' . $filters['q'] . '%');
     * }
     *
     * // Filter by 'locationId'
     * if (!empty($filters['locationId'])) {
     * // Join with OrderItem to access location
     * $qb
     * ->andWhere('oi.location = :locationId')
     * ->setParameter('locationId', $filters['locationId']);
     * }
     *
     * // Filter by 'platformId'
     * if (!empty($filters['platformId'])) {
     * $qb->andWhere('o.platform = :platformId')
     * ->setParameter('platformId', $filters['platformId']);
     * }
     *
     * // Filter by 'brandId'
     * if (!empty($filters['brandId'])) {
     * $qb
     * ->andWhere('avp.productAttribute = :brandAttributeId')
     * ->andWhere('avp.productAttributeValue = :brandId')
     * ->setParameter('brandAttributeId', 1) // Assuming id=1 represents the brand attribute
     * ->setParameter('brandId', $filters['brandId']);
     * }
     *
     * // Filter by 'barcode'
     * if (!empty($filters['barcode'])) {
     * $qb
     * ->andWhere('pv.barcode LIKE :barcode')
     * ->setParameter('barcode', '%' . $filters['barcode'] . '%');
     * }
     *
     * return $qb;
     * }
     */
}