<?php

namespace App\Service\Search;

use App\Service\ApiService;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;

class TypeSenseApiService extends ApiService
{
    public function __construct(LoggerInterface $logger, TokenStorageInterface $tokenStorage, ParameterBagInterface $parameter)
    {
        $apiKey = $_ENV['TYPESENSE_API_KEY'];
        $headers = [
            'accept' => 'application/json',
            'Content-Type' => 'application/json',
            'X-TYPESENSE-API-KEY' => $apiKey
        ];
        parent::__construct( $logger, $tokenStorage, $parameter,$headers);
        $this->apiEndpoint = $_ENV['TYPESENSE_API_URL'];
    }

}