<?php

namespace App\Service;

use Wb3\SharedBundle\Entity\Product;
use Wb3\SharedBundle\Entity\ProductPrice;

class ProductPriceService
{
    public function __construct(
    ){}

    public function getOneByProductAndType(Product $product, int $typeId) : ?ProductPrice
    {
        $filterPrices = $product->getPrices()->filter(function($price) use ($typeId) {return $price->getType()->getId() == $typeId;});

        if($filterPrices->isEmpty()) {
            return null;
        }

        /** @var ProductPrice $filterPrice */
        return $filterPrices->first();
    }
}