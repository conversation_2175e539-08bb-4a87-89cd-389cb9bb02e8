<?php

namespace App\Service;

use App\Repository\ProductAttributeValueRepository;
use Wb3\SharedBundle\Entity\Product;
use Wb3\SharedBundle\Entity\ProductAttributeValueProduct;

class ProductAttributeValueService
{

    public ProductAttributeValueRepository $repository;

    public function __construct(
        ProductAttributeValueRepository $repository,

    )
    {
        $this->repository = $repository;
    }


    public function listByAttribute(int $attributeId) : array
    {
        return $this->repository->findBy(['attribute'=>$attributeId],['name'=>'ASC']);
    }

    public function allWithPaginate(array $filters = [], int $page = 1, $limit = 10): array
    {

        return $this->repository->allWithPaginate($page, $limit, $filters);
    }

    public function getOneFromAttributeNameByProduct(Product $product, int $attributeId) : string
    {
        $filterAttributes = $product->getAttributes()->filter(function($attribute) use ($attributeId) {return $attribute->getAttribute()?->getId() == $attributeId;});
        if($filterAttributes->isEmpty()) {
            return '';
        }
        /** @var ProductAttributeValueProduct $filteredAttribute */
        $filteredAttribute = $filterAttributes->first();


        return $filteredAttribute->getValue()->getFriendlyName() ?? $filteredAttribute->getValue()->getName();
    }


}