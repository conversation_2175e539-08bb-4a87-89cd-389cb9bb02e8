<?php

namespace App\Service;

use App\Repository\OrderAuditRepository;
use DH\Auditor\Provider\Doctrine\Auditing\Annotation\Auditable;
use Doctrine\ORM\EntityManagerInterface;
use Wb3\SharedBundle\Entity\OrderAudit;
use Wb3\SharedBundle\Entity\User;

/**
 * Denetim kayıtları (audit log) yönetim servisi
 * 
 * Bu servis, sistemdeki veritabanı işlemlerinin izlenmesini ve 
 * değişikliklerin kayıt altına alınmasını sağlar. Özellikle sipariş
 * işlemlerindeki değişikliklerin hangi kullanıcı tarafından ne zaman
 * yapıldığı bilgisini sunar.
 */
class AuditService
{
    /**
     * AuditService yapıcı metodu
     * 
     * EntityManager bağımlılığını oluşturur.
     * 
     * @param EntityManagerInterface $entityManager Doctrine entity manager
     */
    public function __construct(
        private readonly  EntityManagerInterface $entityManager
    )
    {
    }
    
    /**
     * Sayfalandırılmış denetim kayıtlarını döndürür
     * 
     * Belirtilen nesne sınıfı ve kimliğe göre denetim kayıtlarını filtreler.
     * 
     * @param string $className Denetim kaydı sınıf adı
     * @param int|null $objectId Nesne kimliği (null ise tüm nesneler)
     * @param string|null $diffs Değişiklik içeriğine göre filtreleme
     * @return array İşlenmiş denetim kayıtları listesi
     */
    public function getWithPaginate(string $className, int $objectId = null,$diffs = null)
    {
        $results = [];
    
        /** @var OrderAuditRepository $repository */
        $repository = $this->entityManager->getRepository($className);
        $data = $repository->getByLikeDiff($objectId, $diffs);
    
    
        foreach($data AS $datum) {
            $results[] = $this->decorateItem($datum);
        }
        return $results;
    }
    
    /**
     * Ham denetim kaydını işleyerek formatlar
     * 
     * Veritabanından alınan denetim kaydını kullanıcı dostu formata dönüştürür.
     * 
     * @param array $item İşlenecek ham denetim kaydı
     * @return array İşlenmiş ve formatlanmış denetim kaydı
     */
    private function decorateItem(array $item)
    {
        $user = $item['blame_user'];
        return [
            'type' => $item['type'],
            'diffs' => $item['diffs'],
            'changes' => json_decode($item['diffs']),
          //  'user' => $this->entityManager->getRepository(User::class)->findOneBy(['email'=>$item['blame_user']]) ?? $item['blame_user']  ?? "-",
            'user' => $user,
            'createdAt' => date('d.m.Y H:i:s',strtotime($item['created_at'])),
        ];
    }
}