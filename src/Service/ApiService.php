<?php

namespace App\Service;


use App\Type\ApiResponseType;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpClient\HttpClient;
use Symfony\Component\HttpKernel\Exception\UnauthorizedHttpException;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Symfony\Component\Security\Core\Security;
use Psr\Log\LoggerInterface;

class ApiService
{


    private $client;

    private $headers = [];


    protected string $apiEndpoint;
    
    private string $apiPlatform ;

    ///**
    // * @var Security
    // */
    //private $security;
    /**
     * @var TokenStorageInterface
     */
    private $tokenStorage;

    /**
     * @var LoggerInterface
     */
    private $coreapiLogger;

    private ParameterBagInterface $parameter;


    public function __construct(/*Security $security,*/ LoggerInterface $logger, TokenStorageInterface $tokenStorage, ParameterBagInterface $parameter, array $headers = [])
    {
        //$this->security = $security;
        $this->tokenStorage = $tokenStorage;
        $this->coreapiLogger = $logger;
        $this->client = HttpClient::create(['verify_peer' => false, 'verify_host' => false]);

        $this->parameter = $parameter;
        $this->headers = $headers;
    }


    /**
     * @param $method
     * @param $uri
     * @param array $postData
     * @param array $getParams
     * @return ApiResponseType
     */
    public function call($method, $uri, $postData = array(), $getParams = array())
    {
        return $this->consume(
            $method,
            $this->createUri($uri, $getParams),
            $postData
        );

    }

    /**
     * @param string $uri
     * @param array $getParams
     *
     * @return string
     */
    private function createUri($uri, array $getParams = ['v' => 1])
    {
        return sprintf(
            '%s%s?%s',
            '',
            $uri,
            http_build_query($getParams)
        );
    }



    /**
     * @param string $method
     * @param string $uri
     * @param array $postData
     * @return ApiResponseType
     */
    private function consume($method, $uri, $postData = array())
    {
        //if ($user = $this->security->getUser()) {
        //    $this->headers['Token'] = $user->getToken();
        //}

        $options = [
            'headers' => $this->headers
        ];

        if (count($postData) > 0) {
            $options['body'] = json_encode($postData,JSON_UNESCAPED_UNICODE);
        }

        //$this->coreapiLogger->info("request",$options);
        $response = $this->client->request($method, $this->apiEndpoint . $uri, $options);
        $responseJson = $response->getContent(false);
        $data = json_decode($responseJson, true);
        //$this->coreapiLogger->info("response",(array)$data);


        if ($response->getStatusCode() == 401) {
            $this->forceToLogout();
            //  throw new \Exception($exception->getMessage(), $exception->getCode());
            throw new UnauthorizedHttpException("", $data->message);
        }
        if($response->getStatusCode() >= 300){
           dd($data);
        }

        $apiResponse = new ApiResponseType();
        $apiResponse->success = ($response->getStatusCode() < 300) ? true: false;
        $apiResponse->errorCode = $response->getStatusCode();
        $apiResponse->message = (isset($data->message)) ? $data->message : null ;
        $apiResponse->errors = (isset($data->violations)) ? $data->violations : null;
        $apiResponse->data = $data;
        $apiResponse->request = $options;

        return $apiResponse;
    }

    public function forceToLogout()
    {
        /* change the user email */
        $this->tokenStorage->setToken();
        /* choose the route to redirect to */

    }


}