<?php

namespace App\Service;


use Enqueue\Client\Message;
use Enqueue\Client\ProducerInterface;
use Enqueue\Rpc\Promise;
use Enqueue\Util\JSON;
use Psr\Log\LoggerInterface;
use Wb3\SharedBundle\Helper\ObjectHydrator;
use Wb3\SharedBundle\Type\NotificationRequestType;

class QueService
{


    public function __construct(
        private readonly  producerInterface $producer,
        private readonly LoggerInterface $logger
    ){}

    public function add(array $data, string $command)
    {
        $serialized = JSON::encode($data);
        $message = new Message($serialized);
        return  $this->producer->sendCommand($command, $message);
    }

    public function addToIndexOrder(int $orderId): ?Promise
    {
        $serialized = $orderId;
        $message = new Message($serialized);
        $promise = $this->producer->sendCommand('IndexOrderProcessor', $message);
        $this->logger->info("orderId:$orderId indekslenmesi için kuyruga gonderildi.");
        return $promise;
    }

    public function addQueueForSendNotification(NotificationRequestType $notificationRequestType)
    {
        $dataArray = ObjectHydrator::normalize($notificationRequestType);

        $serialized = JSON::encode($dataArray);
        $message = new Message($serialized);

        return $this->producer->sendCommand('SendNotificationProcessor', $message, false);
    }

}


