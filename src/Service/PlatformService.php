<?php

namespace App\Service;

use App\Repository\PlatformRepository;
use App\Service\Platform\Idefix\IdefixMarketplaceService;
use App\Service\Platform\PlatformServiceInterface;
use Wb3\SharedBundle\Entity\Platform;

/**
 * Satış platformlarını yöneten servis
 * 
 * Bu servis, sistemdeki farklı satış platformlarına (marketplace) 
 * erişimi sağlar ve platform koduna göre platform bilgilerini
 * getirmek için kullanılır.
 */
class PlatformService
{
    public PlatformRepository $repository;
    
    /**
     * PlatformService yapıcı metodu
     * 
     * Platform repository bağımlılığını oluşturur.
     * 
     * @param PlatformRepository $repository Platform repository'si
     */
    public function __construct(PlatformRepository $repository)
    {
        $this->repository = $repository;
    }
    
    /**
     * Platform koduna göre platform nesnesini döndürür
     * 
     * Verilen kod ile eşleşen platform kaydını veritabanından bulur.
     * 
     * @param string $code Platform kodu
     * @return Platform Platform nesnesi
     */
    public function getByCode(string $code): Platform
    {
        return $this->repository->findOneBy(['code' => $code]);
    }
}