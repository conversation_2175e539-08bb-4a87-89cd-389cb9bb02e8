<?php

namespace App\Service\Wb2;

use App\Repository\Wb2\OrderRepository;
use Wb3\SharedBundle\Entity\Order;

class OrderService
{

    public function __construct(
        private readonly  OrderRepository $orderRepository
    ){}

    public function updateStatusToInCargo(Order $order)
    {
        $this->orderRepository->updateStatusToInCargo($order->getPlatform()->getCode(),$order->getOrderNumber());
    }



}