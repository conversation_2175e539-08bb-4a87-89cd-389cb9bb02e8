<?php

namespace App\Service;

use App\Repository\ProductVariantPlatformRepository;
use App\Service\Platform\Idefix\IdefixMapperService;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;
use Wb3\SharedBundle\Entity\Platform;
use Wb3\SharedBundle\Entity\ProductVariantPlatform;

class ProductVariantPlatformService
{

    private ProductVariantService $productVariantService;
    private LoggerInterface $logger;
    private IdefixMapperService $idefixPlatformMapperService;
    public ProductVariantPlatformRepository $repository;
    private EntityManagerInterface $em;

    public function __construct(IdefixMapperService $idefixPlatformMapperService,
                                ProductVariantService $productVariantService,
                                LoggerInterface $logger,
                                ProductVariantPlatformRepository $repository,
                                EntityManagerInterface $em,
    )
    {
        $this->idefixPlatformMapperService = $idefixPlatformMapperService;
        $this->productVariantService = $productVariantService;
        $this->logger = $logger;
        $this->repository = $repository;
        $this->em = $em;
    }

    /**
     * @param Platform $platform
     * @param ProductVariantPlatform[] $newPvps
     * @return array
     */
    public function sync(Platform $platform, array $newPvps) : array
    {
        $info = ['changed'=>0,'added'=>0];
        $newPvpWithVsc = $pvpWithVsc = $willBeAdd = $willBeChange =  [];

        //Veritabaninda olanlar
        $pvps = $this->repository->findBy(['platform'=>$platform]);
        foreach ($pvps AS $pvp) {
            $pvpWithVsc[$pvp->getVendorStockCode()] = $pvp;
        }

        //Platformdan gelenler
        foreach($newPvps AS $newPvp) {
            $newPvpWithVsc[$newPvp->getVendorStockCode()] = $newPvp;
            if(!isset($pvpWithVsc[$newPvp->getVendorStockCode()])) {
                $willBeAdd[] = $newPvp;
            } elseif($pvpWithVsc[$newPvp->getVendorStockCode()]->getState() != $newPvp->getState()) {
                $willBeChange[] = $newPvp;
            }
        }

        if(count($willBeAdd)) {
            foreach($willBeAdd AS $newPvp) {
                $productVariant = $this->productVariantService->repository->findOneBy([], []);
                if (!$productVariant) {
                    $this->logger->error('ProductVariant not found for '.$newPvp->getVendorStockCode());
                    throw new UnprocessableEntityHttpException('ProductVariant not found.');
                }

                // yoksa eklenecek.
                $newPvp->setProductVariant($productVariant);
                $this->em->persist($newPvp);
                $this->logger->info($newPvp->getVendorStockCode().' ürünü veritabanına eklendi.');
                $this->em->flush();
                $info['added']++;
            }
        }
        $this->logger->info("Ekelencek ürün sayısı: ".count($willBeAdd)." Güncellenecek ürün sayısı:".count($willBeChange));
        if(count($willBeChange)) {
            foreach($willBeChange AS $newPvp) {
                $productVariant = $this->productVariantService->repository->findOneBy([], []);
                $pvp = $pvpWithVsc[$newPvp->getVendorStockCode()];
                if (!$productVariant) {
                    $this->logger->error('ProductVariant not found for '.$newPvp->getVendorStockCode());
                    throw new UnprocessableEntityHttpException('ProductVariant not found.');
                }
                // eşitse degilse guncelle.
                $pvp->setState($newPvp->getState());
                $this->logger->info($newPvp->getVendorStockCode()." ürün state guncellemesi yapildi.");
                $this->em->flush();
                $info['changed']++;
            }
        }
        return $info;
    }

    public function getByVendorStockCodes(Platform $platform, array $vendorStockCodes)
    {
        return $this->repository->getByVendorStockCodes($platform,$vendorStockCodes);
    }

    public function getByBarcode(Platform $platform, string $barcode)
    {
        return $this->repository->findOneBy(['platform'=>$platform,'barcode'=>$barcode]);
    }

    public function getByBarcodes(Platform $platform, array $barcodes,$states=[])
    {
        return $this->repository->getByBarcodes($platform,$barcodes,$states);
    }
}