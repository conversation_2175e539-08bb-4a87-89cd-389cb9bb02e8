<?php

namespace App\Service;

use Bulut\DespatchService\SendDespatch;
use Bulut\FITApi\FITDespatchService;
use Symfony\Component\DependencyInjection\ContainerInterface;
use ZipArchive;

class ForibaService {

    public function __construct(
        //ntegrationRepository $repo, SqlServerRepository $sqlServerRepository
    ) {
        //$this->_container = $container;
        //$this->_repo = $repo;
        //$this->_sqlRepo = $sqlServerRepository;
        //
        //$this->username = $container->getParameter('Foriba')['Username'];
        //$this->password = $container->getParameter('Foriba')['Password'];
        //$this->vkn = $container->getParameter('Foriba')['VKN'];
        //$this->identifier = $container->getParameter('Foriba')['Identifier'];
        //$this->despatchIdentifier = $container->getParameter('Foriba')['despatchIdentifier'];
    }
//<
//    public function getService() {
//        return new FITInvoiceService([
//            'username' => $this->username,
//            'password' => $this->password
//        ], $this->isTest);
//    }
//
//    public function getArchiveService() {
//        return new FITArchiveService([
//            'username' => $this->username,
//            'password' => $this->password
//        ],$this->isTest);
//    }

    public function getDespatchService(): FITDespatchService
    {
        return new FITDespatchService([
            'username' => $_ENV['FORIBA_USERNAME'],
            'password' => $_ENV['FORIBA_PASSWORD']
        ],filter_var($_ENV['FORIBA_IS_TEST'], FILTER_VALIDATE_BOOLEAN));
    }

//    public function getUblList($fromDate, $toDate, $docType = 'INVOICE', $type = 'INBOUND') {
//        $service = $this->getService();
//        $getUblRequest = new GetUblList();
//        $getUblRequest->setIdentifier($this->identifier);
//        $getUblRequest->setVKNTCKN((string) $this->vkn);
//        $getUblRequest->setDocType($docType); // INVOICE & ENVOLOPE
//        $getUblRequest->setType($type); // INBOUND & OUTBOUND
//        $getUblRequest->setFromDate($fromDate); // yyyy-MM-dd
//        $getUblRequest->setToDate($toDate); // yyyy-MM-dd
//        $getUblRequest->setFromDateSpecified(true);
//        $getUblRequest->setToDateSpecified(true);
//        return $service->GetUblListRequest($getUblRequest);
//    }
//
//    public function getUbl($invoiceID, $docType = 'INVOICE', $type = 'INBOUND') {
//        $service = $this->getService();
//        $getUblRequest = new GetUbl();
//        $getUblRequest->setIdentifier($this->identifier);
//        $getUblRequest->setVKNTCKN($this->vkn);
//        $getUblRequest->setDocType($docType); // INVOICE & ENVOLOPE
//        $getUblRequest->setType($type); // INBOUND & OUTBOUND
//        $getUblRequest->setUUID($invoiceID);
//        return base64_decode($service->GetUblRequest($getUblRequest)[0]->DocData);
//    }
//
//    public function getInvoiceView($invoiceId, $docType = "HTML", $type = 'INBOUND') {
//        $service = $this->getService();
//        $getInvoiceRequest = new GetInvoiceView();
//        $getInvoiceRequest->setUUID($invoiceId);
//        $getInvoiceRequest->setIdentifier($this->identifier);
//        $getInvoiceRequest->setVKNTCKN($this->vkn);
//        $getInvoiceRequest->setType($type); // INBOUND & OUTBOUND
//        $getInvoiceRequest->setDocType($docType); // PDF, PDF_DEFAULT, HTML
//        return base64_decode($service->GetInvoiceViewRequest($getInvoiceRequest)->DocData);
//    }
//
//    public function getDesUblList($fromDate, $toDate, $docType = 'DESPATCH', $type = 'INBOUND') {
//        $service = $this->getDespatchService();
//        $getUblRequest = new GetDesUBLList();
//        $getUblRequest->setIdentifier($this->despatchIdentifier);
//        $getUblRequest->setVKNTCKN((string) $this->vkn);
//        $getUblRequest->setDocType($docType); // INVOICE & ENVOLOPE
//        $getUblRequest->setType($type); // INBOUND & OUTBOUND
//        $getUblRequest->setFromDate($fromDate); // yyyy-MM-dd
//        $getUblRequest->setToDate($toDate); // yyyy-MM-dd
//
//        return $service->GetUblListRequest($getUblRequest);
//    }
//    public function getDesUbl($invoiceID, $docType = 'DESPATCH', $type = 'OUTBOUND') {
//        $service = $this->getDespatchService();
//        $getUblRequest = new GetDesUBL();
//        $getUblRequest->setIdentifier($this->despatchIdentifier);
//        $getUblRequest->setVKNTCKN($this->vkn);
//        $getUblRequest->setDocType($docType); // INVOICE & ENVOLOPE
//        $getUblRequest->setType($type); // INBOUND & OUTBOUND
//        $getUblRequest->setUUID($invoiceID);
//
//        return base64_decode($service->GetUblRequest($getUblRequest)[0]->DocData);
//    }
//
//    /**
//     * @param string $invoiceNumber OIA... ile baslayan fatura numarasi. Korgunden verilen veri
//     * @param string $docType PDF veya HTML
//     * @return mixed
//     */
//    public function getArchiveInvoiceView(string $invoiceNumber, string $docType = "PDF") {
//        $service = $this->getArchiveService();
//        $getInvoiceRequest = new GetInvoiceDocument();
//        $getInvoiceRequest->setVkn($this->vkn);
//        $getInvoiceRequest->setInvoiceNumber($invoiceNumber);
//        $getInvoiceRequest->setOutputType($docType); // PDF, HTML
//
//        $getInvoiceDocumentResponse = $service->GetInvoiceDocumentRequest($getInvoiceRequest);
//        return $getInvoiceDocumentResponse;
//
//    }
//
//    public function getUserList() {
//        $service = $this->getService();
//        $userListRequest = new GetUserList();
//        $userListRequest->setIdentifier($this->identifier);
//        $userListRequest->setVKNTCKN($this->vkn);
//        $userListRequest->setRole("GB");
//        $userListRequest->setFilterVKNTCKN($this->vkn);
//        return $service->GetUserListRequest($userListRequest);
//    }
//
//    public function test($startDate, $endDate) {
//        $objPHPExcel = new PHPExcel();
//        $objPHPExcel->getProperties()->setCreator("Otto Solmaz Workboard")->setLastModifiedBy("Workboard");
//
//
//        /* CREATE FORIBA SHEET START */
//        $sh = $objPHPExcel->createSheet(0);
//        $sh->getStyle('A1:AF1')->getAlignment()->applyFromArray(array('horizontal' => PHPExcel_Style_Alignment::HORIZONTAL_CENTER,));
//        $i = 0;
//        foreach (['ARAMA', 'FATURA ID', 'MÜŞTERİ FAT NO', 'GÖNDERİCİ VKN/TCKN', 'GÖNDERİCİ ETİKET', 'GÖNDERİCİ UNVAN', 'DÜZENLEME TARİHİ', 'GELİŞ TARİHİ', 'SENARYO', 'FATURA TİPİ', 'FATURA DURUMU', 'DURUM TARİHİ', 'UYGULAMA YANITI', 'UYGULAMA TARİHİ', 'MAL HİZMET TOPLAM TUTAR', 'TOPLAM İSKONTO', 'VERGİLER TOPLAM TUTAR', 'VERGİLER HARİÇ TOPLAM TUTAR', 'KDV (%0) TUTAR', 'KDV (%0) MATRAH', 'KDV (%1) TUTAR', 'KDV (%1) MATRAH', 'KDV (%8) TUTAR', 'KDV (%8) MATRAH', 'KDV (%18) TUTAR', 'KDV (%18) MATRAH', 'KDV TOPLAM TUTAR', 'KDV TOPLAM MATRAH', 'TUTAR', 'PARA BİRİMİ', 'TRY-TUTAR', 'DÖVİZ KURU'] as $item) {
//            $sh->setCellValueByColumnAndRow($i, 1, $item);
//            $sh->getColumnDimensionByColumn($i)->setAutoSize(true);
//            $i++;
//        }
//        // Header Styles Start
//        $sh->getStyle('A1:AF1')->getFill()->setFillType(PHPExcel_Style_Fill::FILL_SOLID);
//        $sh->getStyle('A1:AF1')->getFill()->getStartColor()->setRGB('02225e');
//        $sh->getStyle('A1:AF1')->getFont()->setBold(true);
//        $sh->getStyle('A1:AF1')->getFont()->setColor(
//            new \PHPExcel_Style_Color(\PHPExcel_Style_Color::COLOR_WHITE)
//        );
//        // Header Styles END
//
//        $sh->setTitle('foriba gelen');
//        /* CREATE FORIBA SHEET END */
//
//
//        /* CREATE KORGUN SHEET START */
//        $sh = $objPHPExcel->createSheet(1);
//        $sh->getStyle('A1:Y1')->getAlignment()->applyFromArray(array('horizontal' => PHPExcel_Style_Alignment::HORIZONTAL_CENTER,));
//        $i = 0;
//        foreach (['SOVOS','korgün','Cari Kod','resmuhkod','Cari Tanım','Belge No','FSeri','Fatura No','Fatura Tarihi','Stok Kodu','Stok Kodu Tanımı','Kdv Oranı','Fatura Miktarı','Birim','Fiyat','NET FİYAT','FİŞ TOPLAM','İskonto Tutarı','İskonto Düşülmüş Matrah', 'KDV Tutarı ','Tutar','Kur','Ozel Kod 1','Ozel Kod 1','kontrol'] as $item) {
//            $sh->setCellValueByColumnAndRow($i, 1, $item);
//            $sh->getColumnDimensionByColumn($i)->setAutoSize(true);
//            $i++;
//        }
//        // Header Styles Start
//        $sh->getStyle('A1:Y1')->getFill()->setFillType(PHPExcel_Style_Fill::FILL_SOLID);
//        $sh->getStyle('A1:Y1')->getFill()->getStartColor()->setRGB('02225e');
//        $sh->getStyle('A1:Y1')->getFont()->setBold(true);
//        $sh->getStyle('A1:Y1')->getFont()->setColor(
//            new \PHPExcel_Style_Color(\PHPExcel_Style_Color::COLOR_WHITE)
//        );
//        // Header Styles END
//
//        $sh->setTitle('korgün');
//        /* CREATE KORGUN SHEET END */
//
//
//        /* CREATE Manuel Girilecek FTlar SHEET START */
//        $sh = $objPHPExcel->createSheet(2);
//        $sh->getStyle('A1:X1')->getAlignment()->applyFromArray(array('horizontal' => PHPExcel_Style_Alignment::HORIZONTAL_CENTER,));
//        $i = 0;
//        foreach (['SOVOS','korgün','Cari Kod','resmuhkod','Cari Tanım','Belge No','FSeri','Fatura No','Fatura Tarihi','Stok Kodu','Stok Kodu Tanımı','Kdv Oranı','Fatura Miktarı','Birim','Fiyat','NET FİYAT','FİŞ TOPLAM','İskonto Tutarı','İskonto Düşülmüş Matrah','KDV Tutarı','Tutar','Kur','Ozel Kod 1','Ozel Kod 1','kontrol'] as $item) {
//            $sh->setCellValueByColumnAndRow($i, 1, $item);
//            $sh->getColumnDimensionByColumn($i)->setAutoSize(true);
//            $i++;
//        }
//        // Header Styles Start
//        $sh->getStyle('A1:X1')->getFill()->setFillType(PHPExcel_Style_Fill::FILL_SOLID);
//        $sh->getStyle('A1:X1')->getFill()->getStartColor()->setRGB('02225e');
//        $sh->getStyle('A1:X1')->getFont()->setBold(true);
//        $sh->getStyle('A1:X1')->getFont()->setColor(
//            new \PHPExcel_Style_Color(\PHPExcel_Style_Color::COLOR_WHITE)
//        );
//        // Header Styles END
//
//        $sh->setTitle('Manuel Girilecek FTlar');
//        /* CREATE E-ARSIV SHEET END */
//
//
//        /* CREATE KORGUN SHEET START */
//        $sh = $objPHPExcel->createSheet(3);
//        $sh->getStyle('A1:X1')->getAlignment()->applyFromArray(array('horizontal' => PHPExcel_Style_Alignment::HORIZONTAL_CENTER,));
//        $i = 0;
//        foreach (['SOVOS','Ozel Kod 1','Unvan','MuhCari','Fatura Tarihi','%0 Matrah','%1 Matrah','%8 Matrah','%18 Matrah','%1 KDV','%8 KDV','%18 KDV','Genel Toplam','Manuel Toplam KDV','','%1 KDV','%8 KDV','%18 KDV','KDV TOPLam','','KDV Kontorl','','FORİBA+eARŞİV Kontrol','Foriba Farkı'] as $item) {
//            $sh->setCellValueByColumnAndRow($i, 1, $item);
//            $sh->getColumnDimensionByColumn($i)->setAutoSize(true);
//            $i++;
//        }
//        // Header Styles Start
//        $sh->getStyle('A1:X1')->getFill()->setFillType(PHPExcel_Style_Fill::FILL_SOLID);
//        $sh->getStyle('A1:X1')->getFill()->getStartColor()->setRGB('02225e');
//        $sh->getStyle('A1:X1')->getFont()->setBold(true);
//        $sh->getStyle('A1:X1')->getFont()->setColor(
//            new \PHPExcel_Style_Color(\PHPExcel_Style_Color::COLOR_WHITE)
//        );
//        // Header Styles END
//
//        $sh->setTitle('AKTARILACAK DATA');
//        /* CREATE KORGUN SHEET END */
//
//
//        /* CREATE E-ARSIV SHEET START */
//        $sh = $objPHPExcel->createSheet(4);
//        $sh->getStyle('A1:X1')->getAlignment()->applyFromArray(array('horizontal' => PHPExcel_Style_Alignment::HORIZONTAL_CENTER,));
//        $i = 0;
//        foreach (['SOVOS','korgün','Cari Kod','resmuhkod','Cari Tanım','Belge No','FSeri','Fatura No','Fatura Tarihi','Stok Kodu','Stok Kodu Tanımı','Kdv Oranı','Fatura Miktarı','Birim','Fiyat','NET FİYAT','FİŞ TOPLAM','İskonto Tutarı','İskonto Düşülmüş Matrah','KDV Tutarı','Tutar','Kur','Ozel Kod 1','Ozel Kod 1'] as $item) {
//            $sh->setCellValueByColumnAndRow($i, 1, $item);
//            $sh->getColumnDimensionByColumn($i)->setAutoSize(true);
//            $i++;
//        }
//        // Header Styles Start
//        $sh->getStyle('A1:X1')->getFill()->setFillType(PHPExcel_Style_Fill::FILL_SOLID);
//        $sh->getStyle('A1:X1')->getFill()->getStartColor()->setRGB('02225e');
//        $sh->getStyle('A1:X1')->getFont()->setBold(true);
//        $sh->getStyle('A1:X1')->getFont()->setColor(
//            new \PHPExcel_Style_Color(\PHPExcel_Style_Color::COLOR_WHITE)
//        );
//        // Header Styles END
//
//        $sh->setTitle('e-arşiv');
//        /* CREATE E-ARSIV SHEET END */
//
//
//        /* CREATE KONTROL SHEET START */
//        $sh = $objPHPExcel->createSheet(5);
//        $sh->getStyle('A1:D1')->getAlignment()->applyFromArray(array('horizontal' => PHPExcel_Style_Alignment::HORIZONTAL_CENTER,));
//        $i = 0;
//        foreach (['SOVOS','korgün ft tutar','sovos','kontrol'] as $item) {
//            $sh->setCellValueByColumnAndRow($i, 1, $item);
//            $sh->getColumnDimensionByColumn($i)->setAutoSize(true);
//            $i++;
//        }
//        // Header Styles Start
//        $sh->getStyle('A1:D1')->getFill()->setFillType(PHPExcel_Style_Fill::FILL_SOLID);
//        $sh->getStyle('A1:D1')->getFill()->getStartColor()->setRGB('02225e');
//        $sh->getStyle('A1:D1')->getFont()->setBold(true);
//        $sh->getStyle('A1:D1')->getFont()->setColor(
//            new \PHPExcel_Style_Color(\PHPExcel_Style_Color::COLOR_WHITE)
//        );
//        // Header Styles END
//
//        $sh->setTitle('KONTROL');
//        /* CREATE KONTROL SHEET END */
//
//
//
//
//        /** CONTENT MANAGER */
//        $styleArray = array(
//            'borders' => array(
//                'allborders' => array(
//                    'style' => \PHPExcel_Style_Border::BORDER_THIN
//                )
//            )
//        );
//
//        // Foriba Start | Not: A sütunu düşey ara sorunu
//        $sovosDocument = $this->readExcel($this->_container->get('kernel')->getRootDir(). '/../web/temp/foribaReport.xlsx', 'e-Fatura Gelen Faturalar');
////        dump($sovosDocument);exit;
////        $sovosDocument = [];
//        $transferData = [];
//        $sh = $objPHPExcel->setActiveSheetIndexByName('foriba gelen');
//        $row = 2;
//        foreach ($sovosDocument as $item) {
//            $transferData[] = $item['fatura-id'];
//            $column = 0;
//            $sh->setCellValueByColumnAndRow($column++, $row, "DÜŞEYARA(B$row; 'korgün'!B:X; 1; 0)");
//            $sh->setCellValueByColumnAndRow($column++, $row, $item['fatura-id']);
//            $sh->setCellValueByColumnAndRow($column++, $row, !empty($item['musteri-fat-no']) ? $item['musteri-fat-no'] : $item['mter-fat-no']);
//            $sh->setCellValueByColumnAndRow($column++, $row, !empty($item['gonderici-vkn-tckn']) ? $item['gonderici-vkn-tckn'] : $item['gnderc-vkn-tckn']);
//            $sh->setCellValueByColumnAndRow($column++, $row, !empty($item['gonderici-etiket']) ? $item['gonderici-etiket'] : $item['gnderc-etket']);
//            $sh->setCellValueByColumnAndRow($column++, $row, !empty($item['gonderici-unvan']) ? $item['gonderici-unvan'] : $item['gnderc-unvan']);
//            $sh->setCellValueByColumnAndRow($column++, $row, !empty($item['duzenleme-tarihi']) ? $item['duzenleme-tarihi'] : $item['dzenleme-tarh']);
//            $sh->setCellValueByColumnAndRow($column++, $row, !empty($item['gelis-tarihi']) ? $item['gelis-tarihi'] : $item['gel-tarh']);
//            $sh->setCellValueByColumnAndRow($column++, $row, $item['senaryo']);
//            $sh->setCellValueByColumnAndRow($column++, $row, !empty($item['fatura-tipi']) ? $item['fatura-tipi'] : $item['fatura-tp']);
//            $sh->setCellValueByColumnAndRow($column++, $row, $item['fatura-durumu']);
//            $sh->setCellValueByColumnAndRow($column++, $row, !empty($item['durum-tarihi']) ? $item['durum-tarihi'] : $item['durum-tarh']);
//            $sh->setCellValueByColumnAndRow($column++, $row, $item['uygulama-yaniti']);
//            $sh->setCellValueByColumnAndRow($column++, $row, !empty($item['uygulama-tarihi']) ? $item['uygulama-tarihi'] : $item['uygulama-tarh']);
//            $sh->setCellValueByColumnAndRow($column++, $row, !empty($item['mal-hizmet-toplam-tutar']) ? $item['mal-hizmet-toplam-tutar'] : $item['mal-hzmet-toplam-tutar']);
//            $sh->setCellValueByColumnAndRow($column++, $row, !empty($item['toplam-iskonto']) ? $item['toplam-iskonto'] : $item['toplam-skonto']);
//            $sh->setCellValueByColumnAndRow($column++, $row, !empty($item['vergiler-toplam-tutar']) ? $item['vergiler-toplam-tutar'] : $item['vergler-toplam-tutar']);
//            $sh->setCellValueByColumnAndRow($column++, $row, !empty($item['vergiler-haric-toplam-tutar']) ? $item['vergiler-haric-toplam-tutar'] : $item['vergler-har-toplam-tutar']);
//            $sh->setCellValueByColumnAndRow($column++, $row, $item['kdv-0-tutar']);
//            $sh->setCellValueByColumnAndRow($column++, $row, $item['kdv-0-matrah']);
//            $sh->setCellValueByColumnAndRow($column++, $row, $item['kdv-1-tutar']);
//            $sh->setCellValueByColumnAndRow($column++, $row, $item['kdv-1-matrah']);
//            $sh->setCellValueByColumnAndRow($column++, $row, $item['kdv-8-tutar']);
//            $sh->setCellValueByColumnAndRow($column++, $row, $item['kdv-8-matrah']);
//            $sh->setCellValueByColumnAndRow($column++, $row, $item['kdv-18-tutar']);
//            $sh->setCellValueByColumnAndRow($column++, $row, $item['kdv-18-matrah']);
//            $sh->setCellValueByColumnAndRow($column++, $row, $item['kdv-toplam-tutar']);
//            $sh->setCellValueByColumnAndRow($column++, $row, $item['kdv-toplam-matrah']);
//            $sh->setCellValueByColumnAndRow($column++, $row, $item['tutar']);
//            $sh->setCellValueByColumnAndRow($column++, $row, !empty($item['para-birimi']) ? $item['para-birimi'] : $item['para-brm']);
//            $sh->setCellValueByColumnAndRow($column++, $row, $item['try-tutar']);
//            $sh->setCellValueByColumnAndRow($column++, $row, !empty($item['doviz-kuru']) ? $item['doviz-kuru'] : $item['dvz-kuru']);
//            $sh->getColumnDimensionByColumn($row)->setAutoSize(true);
//            $row++;
//        }
//        $sh->getStyle('A1:AF' . --$row)->applyFromArray($styleArray);
//        // Foriba End
//
//        // Korgun Start | Not: A sütunu düşey ara sorunu, P sütunu Net Fiyat eksikliği, X sütunu Ozel Kod 1 eksikliği
//        $foribaDocument = $this->_sqlRepo->getInvoiceForForiba($startDate, $endDate);
//        $foribaServiceDocument = $this->_sqlRepo->getServiceInvoiceForForiba($startDate, $endDate);
////        $foribaDocument = [];
//        $sh = $objPHPExcel->setActiveSheetIndexByName('korgün');
//        $row = 2;
//        foreach ($foribaDocument as $item) {
//            $column = 0;
//            $sh->setCellValueByColumnAndRow($column++, $row, "DÜŞEYARA(B2; 'foriba gelen'!B:AF; 1; 0)");
//            $sh->setCellValueByColumnAndRow($column++, $row, "=G$row & H$row");
//            $sh->setCellValueByColumnAndRow($column++, $row, $item['CariKod']);
//            $sh->setCellValueByColumnAndRow($column++, $row, $item['Firma_Saticilar_Hesabi']);
//            $sh->setCellValueByColumnAndRow($column++, $row, $item['CariKod_Tanim']);
//            $sh->setCellValueByColumnAndRow($column++, $row, $item['Belgeno']);
//            $sh->setCellValueByColumnAndRow($column++, $row, $item['FSeri']);
//            $sh->setCellValueByColumnAndRow($column++, $row, $item['FaturaNo']);
//            $sh->setCellValueByColumnAndRow($column++, $row, date('d.m.Y H:i:s', strtotime($item['FatTar'])));
//            $sh->setCellValueByColumnAndRow($column++, $row, $item['skod']);
//            $sh->setCellValueByColumnAndRow($column++, $row, $item['SKod_Tanim']);
//            $sh->setCellValueByColumnAndRow($column++, $row, $item['KDVOran']);
//            $sh->setCellValueByColumnAndRow($column++, $row, $item['Fatura_Miktari']);
//            $sh->setCellValueByColumnAndRow($column++, $row, $item['Birim']);
//            $sh->setCellValueByColumnAndRow($column++, $row, $item['Fiyat']);
//            $sh->setCellValueByColumnAndRow($column++, $row, $item['Fiyat']);
//            $sh->setCellValueByColumnAndRow($column++, $row, "=O$row * M$row");
//            $sh->setCellValueByColumnAndRow($column++, $row, $item['iskontoTut']);
//            $sh->setCellValueByColumnAndRow($column++, $row, "=+Q$row - R$row");
//            $sh->setCellValueByColumnAndRow($column++, $row, $item['KdvTutari']);
//            $sh->setCellValueByColumnAndRow($column++, $row, $item['Tutar']);
//            $sh->setCellValueByColumnAndRow($column++, $row, $item['ParaCinsi']);
//            $sh->setCellValueByColumnAndRow($column++, $row, str_replace(',', '.', !empty(explode('_', $item['Resmi_Muhasebe_Kodu'])[1]) ? explode('_', $item['Resmi_Muhasebe_Kodu'])[1] : ''));
//            $sh->setCellValueByColumnAndRow($column++, $row, str_replace(',', '.', !empty(explode('_', $item['Resmi_Muhasebe_Kodu'])[0]) ? explode('_', $item['Resmi_Muhasebe_Kodu'])[0] : ''));
//            $sh->getColumnDimensionByColumn($row)->setAutoSize(true);
//            $row++;
//        }
//        foreach ($foribaServiceDocument as $item) {
//            $column = 0;
//            $sh->setCellValueByColumnAndRow($column++, $row, "DÜŞEYARA(B2; 'foriba gelen'!B:AF; 1; 0)");
//            $sh->setCellValueByColumnAndRow($column++, $row, "=G$row & H$row");
//            $sh->setCellValueByColumnAndRow($column++, $row, $item['CariKod']);
//            $sh->setCellValueByColumnAndRow($column++, $row, $item['Firma_Saticilar_Hesabi']);
//            $sh->setCellValueByColumnAndRow($column++, $row, $item['CariKod_Tanim']);
//            $sh->setCellValueByColumnAndRow($column++, $row, $item['Belgeno']);
//            $sh->setCellValueByColumnAndRow($column++, $row, $item['FSeri']);
//            $sh->setCellValueByColumnAndRow($column++, $row, $item['FaturaNo']);
//            $sh->setCellValueByColumnAndRow($column++, $row, date('d.m.Y H:i:s', strtotime($item['FatTar'])));
//            $sh->setCellValueByColumnAndRow($column++, $row, $item['skod']);
//            $sh->setCellValueByColumnAndRow($column++, $row, $item['SKod_Tanim']);
//            $sh->setCellValueByColumnAndRow($column++, $row, $item['KDVOran']);
//            $sh->setCellValueByColumnAndRow($column++, $row, $item['Fatura_Miktari']);
//            $sh->setCellValueByColumnAndRow($column++, $row, $item['Birim']);
//            $sh->setCellValueByColumnAndRow($column++, $row, $item['Fiyat']);
//            $sh->setCellValueByColumnAndRow($column++, $row, $item['Fiyat']);
//            $sh->setCellValueByColumnAndRow($column++, $row, "=O$row * M$row");
//            $sh->setCellValueByColumnAndRow($column++, $row, $item['iskontoTut']);
//            $sh->setCellValueByColumnAndRow($column++, $row, "=+Q$row - R$row");
//            $sh->setCellValueByColumnAndRow($column++, $row, $item['KdvTutari']);
//            $sh->setCellValueByColumnAndRow($column++, $row, $item['Tutar']);
//            $sh->setCellValueByColumnAndRow($column++, $row, $item['ParaCinsi']);
//            $sh->setCellValueByColumnAndRow($column++, $row, str_replace(',', '.', !empty(explode('_', $item['Resmi_Muhasebe_Kodu'])[1]) ? explode('_', $item['Resmi_Muhasebe_Kodu'])[1] : ''));
//            $sh->setCellValueByColumnAndRow($column++, $row, str_replace(',', '.', !empty(explode('_', $item['Resmi_Muhasebe_Kodu'])[0]) ? explode('_', $item['Resmi_Muhasebe_Kodu'])[0] : ''));
//            $sh->getColumnDimensionByColumn($row)->setAutoSize(true);
//            $row++;
//        }
//        $sh->getStyle('A1:Y' . --$row)->applyFromArray($styleArray);
//        // Korgun End
//
//        // Aktarılacak Data Start
//        $sh = $objPHPExcel->setActiveSheetIndexByName('AKTARILACAK DATA');
//        $row = 2;
//        foreach ($transferData as $item) {
//            $column = 0;
//            $sh->setCellValueByColumnAndRow($column++, $row, $item);
//            $sh->setCellValueByColumnAndRow($column++, $row, "DÜŞEYARA(A2;korgün!A:X;24;0)");
//            $sh->setCellValueByColumnAndRow($column++, $row, 'Düşeyara(A2; \'korgün\'!A:E; 5; 0)');
//            $sh->setCellValueByColumnAndRow($column++, $row, 'DÜŞEYARA(A2; \'korgün\'!A:E; 4; 0)');
//            $sh->setCellValueByColumnAndRow($column++, $row, 'DÜŞEYARA(A2; \'korgün\'!A:I; 9; 0)');
//            $sh->setCellValueByColumnAndRow($column++, $row, 'ÇOKETOPLA(\'korgün\'!S:S;\'korgün\'!A:A;A2;\'korgün\'!X:X;B2;\'korgün\'!L:L;"0")');
//            $sh->setCellValueByColumnAndRow($column++, $row, 'ÇOKETOPLA(\'korgün\'!S:S;\'korgün\'!A:A;A2;\'korgün\'!X:X;B2;\'korgün\'!L:L;"1")');
//            $sh->setCellValueByColumnAndRow($column++, $row, 'ÇOKETOPLA(\'korgün\'!S:S;\'korgün\'!A:A;A2;\'korgün\'!X:X;B2;\'korgün\'!L:L;"8")');
//            $sh->setCellValueByColumnAndRow($column++, $row, 'ÇOKETOPLA(\'korgün\'!S:S;\'korgün\'!A:A;A2;\'korgün\'!X:X;B2;\'korgün\'!L:L;"18")');
//            $sh->setCellValueByColumnAndRow($column++, $row, "=G$row * 0.01");
//            $sh->setCellValueByColumnAndRow($column++, $row, "=H$row * 0.08");
//            $sh->setCellValueByColumnAndRow($column++, $row, "=I$row * 0.18");
//            $sh->setCellValueByColumnAndRow($column++, $row, "=F$row + G$row + H$row + I$row + N$row");
//            $sh->setCellValueByColumnAndRow($column++, $row, "=(G$row * 0.01) + (H$row * 0.08) + (I$row * 0.18)");
//            $sh->setCellValueByColumnAndRow($column++, $row, '');
//            $sh->setCellValueByColumnAndRow($column++, $row, 'ÇOKETOPLA(\'korgün\'!T:T; \'korgün\'!A:A; A2; \'korgün\'!X:X; B2; \'korgün\'!L:L; "1")');
//            $sh->setCellValueByColumnAndRow($column++, $row, 'ÇOKETOPLA(\'korgün\'!T:T; \'korgün\'!A:A; A2; \'korgün\'!X:X; B2; \'korgün\'!L:L; "8")');
//            $sh->setCellValueByColumnAndRow($column++, $row, 'ÇOKETOPLA(\'korgün\'!T:T; \'korgün\'!A:A; A2; \'korgün\'!X:X; B2; \'korgün\'!L:L; "18")');
//            $sh->setCellValueByColumnAndRow($column++, $row, "=O$row + P$row + Q$row");
//            $sh->setCellValueByColumnAndRow($column++, $row, "");
//            $sh->setCellValueByColumnAndRow($column++, $row, "=N$row - R$row");
//            $sh->setCellValueByColumnAndRow($column++, $row, "");
//            $sh->setCellValueByColumnAndRow($column++, $row, 'ETOPLA(\'foriba gelen\'!A:A; A2; \'foriba gelen\'!AA:AA) + ETOPLA(\'e-arşiv\'!B:B; A2; \'e-arşiv\'!T:T)');
//            $sh->setCellValueByColumnAndRow($column++, $row, "=S$row - W$row");
//            $sh->getColumnDimensionByColumn($row)->setAutoSize(true);
//            $row++;
//        }
//        $sh->getStyle('A1:X' . --$row)->applyFromArray($styleArray);
//        // Aktarılacak Data End
//
//        // Aktarılacak Data Start
//        $sh = $objPHPExcel->setActiveSheetIndexByName('KONTROL');
//        $row = 2;
//        foreach ($transferData as $item) {
//            $column = 0;
//            $sh->setCellValueByColumnAndRow($column++, $row, $item);
//            $sh->setCellValueByColumnAndRow($column++, $row, 'YUVARLA(ÇOKETOPLA(korgün!U:U;korgün!B:B;A2);2)');
//            $sh->setCellValueByColumnAndRow($column++, $row, "YUVARLA(ÇOKETOPLA('foriba gelen'!AC:AC;'foriba gelen'!B:B;A2);2)");
//            $sh->setCellValueByColumnAndRow($column++, $row, 'EĞER(EĞER(B2=C2;"EŞİT";B2-C2)>-0,9;"EŞİT";B2-C2)');
//            $row++;
//        }
//        $sh->getStyle('A1:D' . --$row)->applyFromArray($styleArray);
//        // Aktarılacak Data End
//
//        /** CONTENT MANAGER */
//
//
//
//
//        $objPHPExcel->setActiveSheetIndexByName('Worksheet');
//        $sheetIndex = $objPHPExcel->getActiveSheetIndex();
//        $objPHPExcel->removeSheetByIndex($sheetIndex);
//        $objPHPExcel->setActiveSheetIndex(0);
//        $fileName = __DIR__."../../../../../web/temp/dataTransferReport.xlsx";
//        $objWriter = new PHPExcel_Writer_Excel2007($objPHPExcel);
//        $objWriter->save($fileName);
//        return $fileName;
//    }
//
//    public function readExcel($file, $sheetName) {
//        $objReader = \PHPExcel_IOFactory::createReader('Excel2007');
//        $objReader->setReadDataOnly(true);
//        $objPHPExcel = $objReader->load($file);
//        $objWorksheet = $objPHPExcel->getActiveSheet();
//        $highestRow = $objWorksheet->getHighestRow();
//        $highestColumn = $objWorksheet->getHighestColumn();
//        $highestColumnIndex = \PHPExcel_Cell::columnIndexFromString($highestColumn);
//        $rows = [];
//        for ($row = 2; $row <= $highestRow; ++$row) {
//            for ($col = 0; $col < $highestColumnIndex; ++$col) {
//                $rows[$row][$this->slugify($objWorksheet->getCellByColumnAndRow($col, 1)->getValue())] = (string) $objWorksheet->getCellByColumnAndRow($col, $row)->getValue();
//            }
//        }
//        $rows = array_values($rows);
//        return $rows;
//    }
//    public static function slugify($text, $divider = '-') {
//        // replace non letter or digits by divider
//        $text = preg_replace('~[^\pL\d]+~u', $divider, $text);
//
//        // transliterate
//        $text = iconv('utf-8', 'us-ascii//TRANSLIT', $text);
//
//        // remove unwanted characters
//        $text = preg_replace('~[^-\w]+~', '', $text);
//
//        // trim
//        $text = trim($text, $divider);
//
//        // remove duplicate divider
//        $text = preg_replace('~-+~', $divider, $text);
//
//        // lowercase
//        $text = TitleHelper::tr_mb_strtolower($text);
//
//        if (empty($text)) {
//            return 'n-a';
//        }
//
//        return $text;
//    }>

    public function sendDispatch(string $uuid, string $xml): bool|array
    {
        $path = '../var/tmp/dispatch';
        if (!file_exists($path)) {
            mkdir($path, 0777, true);
        }

        $destination = "$path/$uuid.zip";

        $zip = new ZipArchive();
        if ($zip->open($destination, ZIPARCHIVE::CREATE) !== true) {
            return false;
        }

        $zip->addFromString($uuid . '.xml', $xml);
        $zip->close();

        $dispatchService = $this->getDespatchService();

        $despatchRequest = new SendDespatch();
        $despatchRequest->setVKNTCKN($_ENV['FORIBA_VKN']);
        $despatchRequest->setDocType('DESPATCH');
        $despatchRequest->setSenderIdentifier($_ENV['FORIBA_DISPATCH_IDENTIFIER']);
        $despatchRequest->setReceiverIdentifier($_ENV['FORIBA_RECEIVER_IDENTIFIER']);
        $despatchRequest->setDocData(base64_encode(file_get_contents($destination)));
        return $dispatchService->SendUBLRequest($despatchRequest);
    }
}
