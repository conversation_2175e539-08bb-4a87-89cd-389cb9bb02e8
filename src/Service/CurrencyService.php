<?php
namespace App\Service;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use App\Repository\ToolCurrencyRepository;
use Doctrine\ORM\EntityManagerInterface;
use Wb3\SharedBundle\Entity\KorgunParakende;

class CurrencyService
{
    public ToolCurrencyRepository $repository;
    public EntityManagerInterface $em;

    public function __construct(ManagerRegistry $registry, ToolCurrencyRepository $repository,EntityManagerInterface $em)
    {
        $this->em = $em;


        $this->repository = $repository;
    }


    public function getAllCurrencies(int $page = 1, $limit = 50, array $filters = []): array
    {
        return $this->repository->allWithPaginate($page, $limit, $filters);
    }
    public function insert(ToolCurrency $toolCurrency)
    {
        $this->em->persist($toolCurrency);
        $this->em->flush();
    }

}