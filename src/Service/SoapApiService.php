<?php

namespace App\Service;


use App\Type\ApiResponseType;
use Exception;
use Psr\Log\LoggerInterface;
use SimpleXMLElement;
use SoapClient;
use SoapFault;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\HttpClient\HttpClient;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;

class SoapApiService
{


    protected $client;

    protected $headers = [
        'Content-Type' => 'text/xml'
    ];


    protected string $apiEndpoint;

    protected string $apiPlatform ;

    ///**
    // * @var Security
    // */
    //private $security;
    /**
     * @var TokenStorageInterface
     */
    protected $tokenStorage;

    /**
     * @var LoggerInterface
     */
    protected $logger;

    protected ParameterBagInterface $parameter;


    public function __construct(/*Security $security,*/ LoggerInterface $logger, TokenStorageInterface $tokenStorage, ParameterBagInterface $parameter)
    {
        //$this->security = $security;
        $this->tokenStorage = $tokenStorage;
        $this->logger = $logger;
        $this->client = HttpClient::create(['verify_peer' => false, 'verify_host' => false]);

        $this->parameter = $parameter;
        //$this->apiEndpoint = $parameter->get('AUTH_SERVER');
    }


    /**
     * @param $method
     * @param $uri
     * @param array $postData
     * @param array $getParams
     * @return ApiResponseType
     */
    public function call(string $method, array $parameters = array())
    {
        return $this->consume(
            $method,
            $parameters
        );

    }

    /**
     * @param string $uri
     * @param array $getParams
     *
     * @return string
     */
    protected function createUri($uri, array $getParams = ['v' => 1])
    {
        return sprintf(
            '%s%s?%s',
            '',
            $uri,
            http_build_query($getParams)
        );
    }


    /**
     * @param string $method
     * @param string $uri
     * @param array $parameters
     * @return ApiResponseType
     * @throws SoapFault
     */
    private function consume(string $method, array $parameters = array())
    {
        $data = $http_status_code = null;
        $wsdl = $this->apiEndpoint;

try {
    $client = new SoapClient($wsdl, [
        'trace' => 1,
        'encoding' => 'UTF-8',  // UTF-8 kodlamasını belirtin
        'stream_context' => stream_context_create(
            array(
                'header' => [
                    'Content-Type' => 'text/xml; charset=utf-8'  // Charset ekleyin
                ],
                'ssl' => array(
                    'verify_peer' => false,
                    'verify_peer_name' => false,
                )
            )
        )
    ]);
    //if ($user = $this->security->getUser()) {
    //    $this->headers['Token'] = $user->getToken();
    //}

    $response = $client->$method($parameters);

    $requestXml = $client->__getLastRequest();
    $responseXml = $client->__getLastResponse();
    $xml = str_replace('xmlns="KorgunWebServis"', '', $responseXml);
    $xml = new SimpleXMLElement($xml);
    $tables = $xml->xpath('//NewDataSet/Table');

    $data = json_decode(json_encode($tables));
} catch (SoapFault $fault) {
//echo "SOAP Fault: (faultcode: {$fault->faultcode}, faultstring: {$fault->faultstring})";
// HTTP başlıklarını ve yanıt kodunu al
    $http_response_header = $client->__getLastResponseHeaders();
    preg_match('/HTTP\/1\.[01] (\d{3})/', $http_response_header, $matches);
    $http_status_code = $matches[1];
} catch (Exception $e) {
    // Genel hataları yakala
    echo "Error: " . $e->getMessage();
}

        $apiResponse = new ApiResponseType();
        $apiResponse->success = $http_status_code ?? 200 < 300;
        $apiResponse->errorCode = $http_status_code;
        $apiResponse->message = (isset($data->message)) ? $data->message : null ;
        $apiResponse->errors = (isset($data->violations)) ? $data->violations : null;
        $apiResponse->data = $data;
        $apiResponse->request = $parameters;

        return $apiResponse;
    }

    public function forceToLogout()
    {
        /* change the user email */
        $this->tokenStorage->setToken();
        /* choose the route to redirect to */

    }

    public function setHeaders(array $headers): SoapApiService
    {
        $this->headers = $headers;
        return $this;
    }

}