<?php

namespace App\Service\Korgun\Soap;
use App\Service\DispatchService;
use App\Service\Order\OrderTransitionService;
use App\Service\ProductPriceService;
use App\Type\Korgun\SetKprFisHarResponseType;
use App\Type\Korgun\SetKprFisOdeResponseType;
use App\Type\Korgun\SetKprFisResponseType;
use App\Type\Korgun\SetMusResponseType;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;
use Wb3\SharedBundle\Entity\Order;
use Wb3\SharedBundle\Entity\User;
use Wb3\SharedBundle\Helper\ObjectHydrator;

/**
 * Korgun entegrasyonu için SOAP tabanlı servis
 * 
 * <PERSON><PERSON> servis, Korgun ERP sistemi ile entegrasyon sağlar ve sipariş verilerinin
 * Korgun sistemine aktarılması, fatura oluşturulması ve müşteri kayıtlarının
 * yönetilmesi gibi işlemleri gerçekleştirir.
 */
class KorgunSoapService
{

    /**
     * KorgunSoapService yapıcı metodu
     * 
     * SOAP istemcisi ve yardımcı servislerin bağımlılıklarını oluşturur.
     * 
     * @param KorgunSoapClient $apiService SOAP API istemcisi
     * @param KorgunSoapMapperService $mapperService Veri dönüştürme servisi
     * @param EntityManagerInterface $entityManager Doctrine entity manager
     * @param KorgunSoapValidateService $validateService Veri doğrulama servisi
     */
    public function __construct(
        private readonly KorgunSoapClient        $apiService,
        private readonly KorgunSoapMapperService $mapperService,
        private readonly EntityManagerInterface  $entityManager,
        private readonly KorgunSoapValidateService $validateService,
        private readonly DispatchService $dispatchService,
        private readonly LoggerInterface $logger,
    )
    {
    }
    
    /**
     * Sipariş bilgilerini Korgun sistemine kaydeder
     * 
     * Sipariş ve müşteri bilgilerini Korgun ERP sistemine göndererek
     * fatura oluşturma işlemini başlatır. Müşteri, fatura, fatura hareketleri
     * ve ödeme bilgilerini sırasıyla kaydeder.
     * 
     * @param Order $order Kaydedilecek sipariş
     * @return array İşlem sonuç bilgileri
     */
    public function savePurchase(Order $order) : array
    {
        $orderItemResults = $orderItemsByStockCode = [];
        $dispatchNo = null;

        $user =  $this->entityManager->getRepository(User::class)->find(3);
        //satin alma yapmadan once verileri validate eder.
        $this->validateService->validateSavePurchase($order,$user);

        //eger export ise irsaliyeyi gönder
        if($order->getExtras()['isExport']) {
            $dispatchNo = $this->dispatchService->sendDispatch($order);
        }

        //Müşteriyi Kaydet müşteriyi ara varsa kendisine yoksa yeni olarak.
        $musteriReq = $this->mapperService->mapOrderToSetMusteriKartRequestType($order);
        $musteriReq = ObjectHydrator::normalize($musteriReq);
        $response = $this->apiService->call("SetMusteriKart", $musteriReq);
        /** @var SetMusResponseType $musRes */
        $musRes = ObjectHydrator::deserialize(json_encode($response->data[0]), SetMusResponseType::class);
        $musKod = $musRes->Column1;
        $this->logger->info('Musteri Kaydedildi.',['musKod' => $musKod]);

        //kaporayı kaydet
        $setKprReq = $this->mapperService->mapOrderToSetKprRequestType($order,$user,$musKod,$dispatchNo);
        $setKprReq = ObjectHydrator::normalize($setKprReq);
        $response = $this->apiService->call("SetKprFis", $setKprReq);
        /** @var SetKprFisResponseType $kprFisRes */
        $kprFisRes = ObjectHydrator::deserialize(json_encode($response->data[0]), SetKprFisResponseType::class);

        if(!$kprFisRes) {
            throw new UnprocessableEntityHttpException('KORGUN_SAVE_PURCHASE_ERROR');
        }
        $korgunSatNo = $kprFisRes->FisNo;
        $invoiceNumber = $kprFisRes->BelgeNo;
        $this->logger->info('Kapora Kaydedildi.',['orderId'=>$order->getId(),'kprFisRes' => $kprFisRes]);

        // stok koduna gore gruplandir.
        foreach($order->getItems() as $orderItem) {
            $orderItemsByStockCode[$orderItem->getProductVariant()->getStockCode()] = ['item'=>null,'quantity'=>0];
            $orderItemsByStockCode[$orderItem->getProductVariant()->getStockCode()]['item'] = $orderItem;
            $orderItemsByStockCode[$orderItem->getProductVariant()->getStockCode()]['quantity'] += 1;
        }

        //fis hareketlerini kaydet
        foreach ($orderItemsByStockCode as $orderItemGroup) {
            $kprFisHarReq = $this->mapperService->mapOrderItemToSetKprHarRequestType($korgunSatNo,$orderItemGroup['item'],$orderItemGroup['quantity']);
            $kprFisHarReq = ObjectHydrator::normalize($kprFisHarReq);
            $response = $this->apiService->call("SetKprFisHar", $kprFisHarReq);
            $kprFisHarRes = ObjectHydrator::deserialize(json_encode($response->data[0]), SetKprFisHarResponseType::class);
            $orderItemResults[] = $kprFisHarRes;
            $this->logger->info('orderItem icin KprFisHar eklendi.',['orderItemId'=>$orderItemGroup['item']->getId(),'kprFisHarRes'=>$kprFisHarRes]);
        }

        //odemeyi kaydet
        $setKprReq = $this->mapperService->mapOrderToSetOdeRequestType($korgunSatNo,$order);
        $setKprReq = ObjectHydrator::normalize($setKprReq);
        $response = $this->apiService->call("SetKprFisOde", $setKprReq);
        $kprFisOdeRes = ObjectHydrator::deserialize(json_encode($response->data[0]), SetKprFisOdeResponseType::class);
        $this->logger->info('Odeme kaydedildi.',['kprFisOdeRes'=>$kprFisOdeRes]);


        $result =  [
            'fisNo' => $korgunSatNo,
            'invoiceNumber' =>$invoiceNumber
        ];
        $this->logger->info("Siparis faturalandirdi.",['orderId'=>$order->getId(),'result'=>$result]);
        return $result;
    }
}