<?php

namespace App\Service\Korgun\Soap;

use App\Repository\OrderAddressRepository;
use App\Service\Order\OrderTransitionService;
use App\Service\ProductPriceService;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;
use Wb3\SharedBundle\Entity\Order;
use Wb3\SharedBundle\Entity\OrderItem;
use Wb3\SharedBundle\Entity\ProductAttribute;
use Wb3\SharedBundle\Entity\ProductPrice;
use Wb3\SharedBundle\Entity\User;

class KorgunSoapValidateService
{
    public function __construct(
        private readonly OrderAddressRepository     $orderAddressRepository,
        private readonly ProductPriceService        $productPriceService,
        private readonly OrderTransitionService     $orderStateService
    )
    {
    }

    public function validateSavePurchase(Order $order, User $user)
    {

        // state
        if(!$this->orderStateService->can($order,Order::TRANSITION_TO_INVOICED))
        {
            throw new UnprocessableEntityHttpException('STATE_NOT_VALID_FOR_TRANSITION : (currentState:' . $order->getState() . ')');
        }


        $customer = $order->getCustomer();
        $invAddr = $this->orderAddressRepository->getInvoiceAddressByOrderId($order->getId());


        //Musteri
        if (strlen($customer->getName())>150) {
            throw new UnprocessableEntityHttpException('CUSTOMER_NAME_TOO_LONG : (customerId:' . $customer->getId() . ')');
        }
        if (strlen($invAddr->getAddress()) > 140) {
            throw new UnprocessableEntityHttpException('CUSTOMER_ADDRESS_TOO_LONG : (customerAddressId:' . $invAddr->getId() . ')');
        }

        //Kapora
        if(!$order->getPlatform()->getKorgunSellerCode()) {
            throw new UnprocessableEntityHttpException('KORGUN_SELLER_CODE_NOT_FOUND');
        }
        if(is_null($user->getKorgunPersonalCode())) {
            throw new UnprocessableEntityHttpException('KORGUN_PERSONAL_CODE_NOT_FOUND');
        }

        //Order Item
        foreach ($order->getItems() as $orderItem) {

            $product = $orderItem->getProductVariant()->getProduct();
            if (!$this->productPriceService->getOneByProductAndType($product, ProductPrice::PRODUCT_PRICE_INTERNET_ID)) {
                throw new UnprocessableEntityHttpException("PRODUCT_PRICE_INTERNET_NOT_FOUND_BY_PRODUCT : (productId:{$product->getId()})");
            }
            if (!$product->getAttributeByAttributeId(ProductAttribute::ATTRIBUTE_BIRIM_ID)) {
                throw new UnprocessableEntityHttpException("PRODUCT_ATTRIBUTE_BIRIM_NOT_FOUND : (productId:{$product->getId()})");
            }

            if(!$orderItem->getProductVariant()->getSizeCode()) {
                throw new UnprocessableEntityHttpException("PRODUCT_VARIANT_SIZE_CODE_NOT_FOUND : (productId:{$product->getId()})");
            }
        }

        //Odeme


    }
}