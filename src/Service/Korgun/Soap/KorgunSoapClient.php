<?php

namespace App\Service\Korgun\Soap;


use App\Service\SoapApiService;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;

class KorgunSoapClient extends SoapApiService
{


    public function __construct(LoggerInterface $logger, TokenStorageInterface $tokenStorage, ParameterBagInterface $parameter)
    {
        parent::__construct( $logger, $tokenStorage, $parameter);
        $this->apiEndpoint = $_ENV['KORGUN_SOAP_API_URL'];
        $this->setHeaders(['Content-Type' => 'text/xml']);
    }

}