<?php

namespace App\Service\Korgun\Soap;

use App\Repository\OrderAddressRepository;
use App\Service\ProductAttributeValueService;
use App\Service\ProductPriceService;
use App\Type\Korgun\SetKprFisHarRequestType;
use App\Type\Korgun\SetKprFisOdeRequestType;
use App\Type\Korgun\SetKprFisRequestType;
use App\Type\Korgun\SetMusteriKartRequestType;
use Symfony\Component\HttpKernel\Exception\UnprocessableEntityHttpException;
use Wb3\SharedBundle\Entity\Location;
use Wb3\SharedBundle\Entity\Order;
use Wb3\SharedBundle\Entity\OrderItem;
use Wb3\SharedBundle\Entity\ProductAttribute;
use Wb3\SharedBundle\Entity\ProductPrice;
use Wb3\SharedBundle\Entity\User;
use Wb3\SharedBundle\Helper\ObjectHydrator;
use Wb3\SharedBundle\Helper\UtilHelper;

/**
 * Korgun SOAP veri dönüştürme servisi
 * 
 * Bu servis, sistem nesnelerini Korgun SOAP API'sinin 
 * anlayacağı request tiplerine dönüştürmek için kullanılır.
 */
class KorgunSoapMapperService
{
    /**
     * KorgunSoapMapperService yapıcı metodu
     * 
     * Repository ve servis bağımlılıklarını oluşturur.
     * 
     * @param OrderAddressRepository $orderAddressRepository Sipariş adresi repository'si
     * @param ProductPriceService $productPriceService Ürün fiyat servisi
     */
    public function __construct(
        private readonly OrderAddressRepository $orderAddressRepository,
        private readonly ProductPriceService $productPriceService,
        private readonly ProductAttributeValueService $productAttributeValueService,
    ){}

    /**
     * Siparişi Korgun fiş request tipine dönüştürür
     * 
     * Sipariş bilgilerini Korgun SOAP API'si için fiş request
     * formatına dönüştürür.
     * 
     * @param Order $order Dönüştürülecek sipariş
     * @param User $user İşlemi yapan kullanıcı
     * @param string $musKod Müşteri kodu
     * @return SetKprFisRequestType Oluşturulan request nesnesi
     */
    public function mapOrderToSetKprRequestType(Order $order,User $user,string $musKod,?string $dispatchNo=null) : SetKprFisRequestType
    {
        $gtipInfos = [];

        $extras = $order->getExtras();

        /**
         * (SatTip,
         * SatTar,
         * PERKOD,
         * Kasa,
         * MusKod,
         * SatKod,
         * BelgeNo,
         * iskontoor,
         * iskontotut,
         * ParaCinsi,
         * Location " .
         * (empty($p['orderNumber']) ? "" : ", OzKod1") .
         * (empty($p['trackingNumber']) ? "" : ", OzKod2") .
         * (empty($p['cargoPrice']) ? "" : ", OzKod3") .
         * (empty($p['platformCommission']) ? "" : ", OzKod6") .
         * (empty($p['platformDiscountAmount']) ? "" : ", OzKod5") .
         * (empty($p['cargoBarcode']) ? "" : ", OzKod14") .
         * (empty($p['cargoCompany']) ? "" : ", OzKod15") .
         * (empty($p['despatchNo']) ? "" : ", OzKod8") .
         * (empty($p['despatchNo']) ? "" : ", OzKod9") .
         * ($p['isMicroExport'] != 'Y' ? "" : ", OzKod11") .
         * ($p['isMicroExport'] != 'Y' ? "" : ", OzKod12") .
         * ($p['isMicroExport'] != 'Y' ? "" : ", Mikroihracat") .
         * ($p['isMicroExport'] != 'Y' ? "" : ", TaxExemptionReasonCode") .
         * ($p['isMicroExport'] != 'Y' ? "" : ", EF_Muafiyet") . "
         * )
         * VALUES      (" . $p['salesType'] . ",
         * GETDATE(), " .
         * $c->quote($p['personnelCode']) . ", " .
         * $c->quote($p['cashierCode']) . ", " .
         * $c->quote($p['customerCode']) . ", " .
         * $c->quote($p['sellerCode']) . ", " .
         * (empty($p['documentNumber']) ? "(SELECT dbo.kg_fn_strinc(Max(BelgeNo), Len(Max(BelgeNo))) FROM Possat_kay WHERE Location = " . $c->quote($p['location']) . ")" : $c->quote($p['documentNumber'])) . "," .
         * (empty($p['discountRate']) ? "0" : $p['discountRate']) . ", " .
         * (empty($p['discountAmount']) ? "0" : $p['discountAmount']) . ", " .
         * $c->quote($p['currency']) . ", " .
         * $c->quote($p['location']) .
         * (empty($p['orderNumber']) ? "" : (", " . $c->quote($p['orderNumber']) )) .
         * (empty($p['trackingNumber']) ? "" : (", " . $c->quote($p['trackingNumber']) )) .
         * (empty($p['cargoPrice']) ? "" : (", " . $c->quote($p['cargoPrice']) )) . //OzKod3
         * (empty($p['platformCommission']) ? "" : (", " . $c->quote($p['platformCommission']) )) .
         * (empty($p['platformDiscountAmount']) ? "" : (", " . $c->quote($p['platformDiscountAmount']) )) .
         * (empty($p['cargoBarcode']) ? "" : (", " . $c->quote($p['cargoBarcode']) )) .
         * (empty($p['cargoCompany']) ? "" : (", " . $c->quote($p['cargoCompany']) )) .
         * (empty($p['despatchNo']) ? "" : (", " . $c->quote($p['despatchNo']) )) .
         * (empty($p['despatchNo']) ? "" : (", " . $c->quote(date("d/m/Y")) )) .
         * ($p['isMicroExport'] != 'Y' ? "" : (", " . $c->quote($p['deliveryTypeForMicroExport']))) .
         * ($p['isMicroExport'] != 'Y' ? "" : (", " . $c->quote($p['gtipInfoForMicroExport']))) .
         * ($p['isMicroExport'] != 'Y' ? "" : (", " . $c->quote('*'))) .
         * ($p['isMicroExport'] != 'Y' ? "" : (", " . $c->quote('301'))) .
         * ($p['isMicroExport'] != 'Y' ? "" : (", " . $c->quote('11/1-a, Mal İhracatı'))) . "
         */
        //Her platformun ayri paymentType i var. platforma yazialacak.

        $type = new SetKprFisRequestType();
        $type->SatNo='';
        $type->SatTip = '2';
        $type->Kasa = 'K003';
        $type->Sattar = date("Y/d/m H:i:s");
        $type->PERKOD = $user->getKorgunPersonalCode();
        $type->MusKod = $musKod;
        $type->SatKod =  $order->getPlatform()->getKorgunSellerCode();
        $type->BelgeNo = '';
        $type->iskontoor = ''; //soapte var. db de yok. bos gerilecek.
        $type->iskontoTut = ''; //soapte var. db de yok. bos gerilecek.
        $type->ParaCinsi = 'TL';
        $type->Location = Location::LOCATION_CODE_INTERNET;
        $type->OzKod1 = $order->getOrderNumber(); //orderNumber
        $type->OzKod2 = $order->getTrack()->getTrackNumber(); //trackingNumber
        $type->OzKod3 = ''; //cargoPrice - navlunBedeli
        $type->OzKod4 = ''; //Fatura No. Tarih
        $type->OzKod5 = $order->getTotalPlatformDiscount(); //platformDiscountAmount
        $type->OzKod6 = ''; //platformCommission
        $type->OzKod7 = ''; //Anlasmali komisyon orani
        $type->OzKod8 = ''; //irsaliyeNo
        $type->OzKod9 = ''; //İrsaliye Tarihi
        $type->OzKod10 = '';
        $type->OzKod11 = ''; //Teslimat Şekli
        $type->OzKod12 = ''; //GTIP
        $type->OzKod13 = '';
        $type->OzKod14 = $order->getTrack()->getTrackNumber(); //cargoBarcode ozkod ile aynıveri yazılacak.
        $type->OzKod15 = $order->getCargo()->getName(); //cargoCompany
        $type->OzKod16 = '';
        $type->OzKod17 = '';
        $type->OzKod18 = '';
        $type->OzKod19 = '';
        $type->OzKod20 = '';
        $type->SevkTar = '';
        $type->Aciklama = $order->getPlatform()->getSalesDescription();
        $type->grf = '';
        $type->SeriNo = '';
        $type->MusMuhKod = '';
        $type->iadeNeden = '';
        $type->mikroihracat=''; //default. aşağıda üzerine basildi.
        $type->TaxExemptionReasonCode='';

        if($extras['isExport']) {

            foreach ($order->getItems() as $i => $orderItem) {
                $gtip = $this->productAttributeValueService->getOneFromAttributeNameByProduct($orderItem->getProductVariant()->getProduct(),ProductAttribute::ID_GTIP);
                $mensei = $this->productAttributeValueService->getOneFromAttributeNameByProduct($orderItem->getProductVariant()->getProduct(),ProductAttribute::ID_MENSEI);
                $gtipInfos[] = "Item" . ($i + 1) . " = GTİP: {$gtip} - MENŞEİ: {$mensei}";
            }

            $type->OzKod8 = $dispatchNo; //despatchNo // HB ve TY de irsaliye gonderildikten sonra eklenecek.
            $type->OzKod9 = date("d/m/Y"); //despatch date
            $type->OzKod11 = $order->getAddresses()[0]->getCountryCode() == 'AZ' ? 'DAP' : 'DDP'; //deliveryTypeForMicroExport
            $type->OzKod12 = implode(" - ",$gtipInfos); //gtipInfoForMicroExport
            $type->mikroihracat = '*'; //@TODO is_export boolean olarak order uzerine konacak ve orda okunacak.true ise * olacak.
            $type->TaxExemptionReasonCode=301; //mkro ihracat ise
            //$type->EF_Muafiyet = '11/1-a, Mal İhracatı'; //@TODO serviste yok.
        }
        return $type;
    }

    public function mapOrderItemToSetKprHarRequestType(int $FisNo, OrderItem $orderItem,int $quantity) : SetKprFisHarRequestType
    {
        //$this->validateMapOrderItemToSetKprHarRequestType($orderItem);

        $product = $orderItem->getProductVariant()->getProduct();
        $netPrice = $this->productPriceService->getOneByProductAndType($orderItem->getProductVariant()->getProduct(),ProductPrice::PRODUCT_PRICE_INTERNET_ID);
        $birimAttribute = $product->getAttributeByAttributeId(ProductAttribute::ATTRIBUTE_BIRIM_ID);

        $listPrice = $netPrice->getPrice();
        $salePrice = $orderItem->getPrice();
        $discount = $listPrice - $salePrice;

        if($discount < 0){
            $discount = 0;
        }

        $type = new SetKprFisHarRequestType();
        $type->SatNo = $FisNo;
        $type->SatHarinx = '';
        $type->SKOD = $orderItem->getProductVariant()->getProduct()->getSku();
        $type->RKOD = $orderItem->getProductVariant()->getColor();
        $type->BedKod = $orderItem->getProductVariant()->getSizeCode(); // TKOD
        $type->Miktar = $quantity;
        $type->Birim = $birimAttribute->getValue()->getName();
        $type->ParaCinsi = 'TL';
        $type->Fiyat = $listPrice;
        $type->iskonto = '0';
        $type->iskontoTut = $discount;
        $type->KDVORAN = $orderItem->getOrder()->getExtras()['isExport'] ? 0 : $orderItem->getVatRate(); //microda kdv orani sifir olarak gidecek.
        $type->iadeSatHarinx = '';
        $type->SatKod = '';
        $type->Barcode= '';
        $type->WMSLocation = '';
        $type->iadeTipi = '';
        /**
         * (SatNo,
         * SKOD,
         * RKOD,
         * BedKod,
         * Miktar,
         * Birim,
         * ParaCinsi,
         * Fiyat,
         * iskonto,
         * iskontoTut,
         * KDVORAN)
         * VALUES      (" .
         * $salesNumber . ", " .
         * $c->quote($itemCode) . ", " .
         * ($itemColourCode ? $itemColourCode : '0') . ", " .
         * ($itemSizeCode ? $itemSizeCode : '0') . ", " .
         * ($itemQuantity ? $itemQuantity : '1') . ", " .
         * $c->quote($itemUnit) . ", " .
         * $c->quote($itemCurrency) . ", " .
         * $itemPrice . ", " .
         * ($itemDiscountRate ? $itemDiscountRate : 0) . ", " .
         * ($itemDiscountAmount ? $itemDiscountAmount : 0) . ", " .
         * ($itemVat ? $itemVat : '0') .
         * ")
         */

        return  $type;
    }

    public function mapOrderToSetOdeRequestType(int $FisNo, Order $order): SetKprFisOdeRequestType
    {
        $type = new SetKprFisOdeRequestType();
        $type->SatNo = $FisNo;
        $type->OdTip = $order->getPlatform()->getKorgunPaymentType();
        $type->ParaCinsi = 'TL';
        $type->Tutar = $order->getTotalPrice();
        $type->OdeTar = date('Y/d/m H:i:s');
        $type->HCKod = '';
        $type->Odinx = '';

        return $type;
        /**
         * (SatNo,
         * OdTip,
         * ParaCinsi,
         * Tutar,
         * OdTar,
         * komisyon)
         * VALUES      (" .
         * $salesNumber . ", " .
         * $c->quote($paymentType) . ", " .
         * $c->quote($currency) . ", " .
         * $paymentAmount . ", " . "
         * GETDATE(),
         * (select komisyon
         * from   perodetiplocation pl
         * where  exists (select top 1 1
         * from   possat_kay pk
         * where  pl.location = pk.location
         * and satno = " . $salesNumber . ")
         * and pl.od_kod = " . $c->quote($paymentType) . ") )
         */
    }

    public function mapOrderToSetMusteriKartRequestType(Order $order)
    {
        $customer = $order->getCustomer();

        if(!$customer) {
            throw new UnprocessableEntityHttpException("ORDER_CUSTOMER_NOT_FOUND");
        }

        $invAddr = $this->orderAddressRepository->getInvoiceAddressByOrderId($order->getId());

        if(!$invAddr)
            throw new UnprocessableEntityHttpException('ORDER_INVOICE_ADDRESS_NOT_FOUND');

        $type = new SetMusteriKartRequestType();

        $type->MKod = '';;
        $type->MName = $customer->getName();
        $type->Adr1 = UtilHelper::ensureValidUtf8(substr($invAddr->getAddress(),0,70));
        $type->Adr2 = UtilHelper::ensureValidUtf8(substr($invAddr->getAddress(),70));
        $type->Sehir = $invAddr->getCity();
        $type->PKod = '';;
        $type->evtel = '';;
        $type->istel = '';;
        $type->ceptel = $invAddr->getPhone() ?? '';
        $type->Fax = '';;
        $type->eMail = $customer->getMail() ?? '';
        $type->WWW = '';;
        $type->Resim = '';;
        $type->DogTar = '';;
        $type->MedDur = '';;
        $type->Cinsiyeti = '';;
        $type->Notu = '';;
        $type->TCKimlikNo = '';;
        $type->WorkName = '';;
        $type->WorkAdr1 = '';;
        $type->WorkAdr2 = '';;
        $type->WorkSehir = '';;
        $type->WorkFax = '';;
        $type->UyeNo = '';;
        $type->KaraListe = '';;
        $type->ilce = $invAddr->getCity();
        $type->Semt = $invAddr->getTown();
        $type->Ulke = $invAddr->getCountry();
        $type->OgrenimDurumu = '';
        $type->Meslek = '';
        $type->OzKod1 = '';
        $type->OzKod2 = '';
        $type->OzKod3 = '';
        $type->OzKod4 = '';
        $type->OzKod5 = '';
        $type->OzKod6 = '';
        $type->OzKod7 = '';
        $type->OzKod8 = '';
        $type->OzKod9 = '';
        $type->OzKod10 = '';
        $type->Location = 'L003';
        $type->vdar = '';
        $type->VNo = '';
        $type->MuhKod = '';


        return $type;

        /**
         *  $this->addEqualParam($p, 'MName', 'customerName', ',') .
         *  $this->addEqualParam($p, 'Adr1', 'address1', ',') .
         *  $this->addEqualParam($p, 'Adr2', 'address2', ',') .
         *  $this->addEqualParam($p, 'Sehir', 'city', ',') .
         *  $this->addEqualParam($p, 'PKod', 'postCode', ',') .
         *  $this->addEqualParam($p, 'evtel', 'homePhone', ',') .
         *  $this->addEqualParam($p, 'istel', 'workPhone', ',') .
         *  $this->addEqualParam($p, 'ceptel', 'mobilePhone', ',') .
         *  $this->addEqualParam($p, 'Fax', 'fax', ',') .
         *  $this->addEqualParam($p, 'eMail', 'email', ',') .
         *  $this->addEqualParam($p, 'WWW', 'website', ',') .
         *  $this->addEqualParam($p, 'Resim', 'photo', ',') .
         *  $this->addEqualParam($p, 'DogTar', 'birthDate', ',', 'ISNULL(NULL,GETDATE())') .
         *  $this->addEqualParam($p, 'MedDur', 'maritalStatus', ',') .
         *  $this->addEqualParam($p, 'Cinsiyeti', 'gender', ',') .
         *  $this->addEqualParam($p, 'TCKimlikNo', 'identityNumber', ',') .
         *  $this->addEqualParam($p, 'WorkName', 'workName', ',') .
         *  $this->addEqualParam($p, 'WorkAdr1', 'workAddress1', ',') .
         *  $this->addEqualParam($p, 'WorkAdr2', 'workAddress2', ',') .
         *  $this->addEqualParam($p, 'WorkSehir', 'workCity', ',') .
         *  $this->addEqualParam($p, 'WorkFax', 'workFax', ',') .
         *  $this->addEqualParam($p, 'KaraListe', 'blacklist', ',') .
         *  $this->addEqualParam($p, 'ilce', 'county', ',') .
         *  $this->addEqualParam($p, 'Semt', 'district', ',') .
         *  $this->addEqualParam($p, 'Ulke', 'country', ',') .
         *  $this->addEqualParam($p, 'OgrenimDurumu', 'educationStatus', ',') .
         *  $this->addEqualParam($p, 'Meslek', 'profession', ',') .
         *  $this->addEqualParam($p, 'vdar', 'taxOffice', ',') .
         *  $this->addEqualParam($p, 'VNo', 'taxNumber');
         * /
         */

    }
}
