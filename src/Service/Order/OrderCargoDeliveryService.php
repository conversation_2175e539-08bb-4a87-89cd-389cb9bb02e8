<?php

namespace App\Service\Order;

use App\Repository\OrderCargoDeliveryRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Security\Core\User\UserInterface;
use Wb3\SharedBundle\Entity\OrderCargo;
use Wb3\SharedBundle\Entity\OrderCargoDelivery;
use Wb3\SharedBundle\Entity\User;

class OrderCargoDeliveryService
{
    
    public function __construct(
        public readonly OrderCargoDeliveryRepository $repository,
        private EntityManagerInterface $entityManager
    ) {}

    public function insert(OrderCargo $orderCargo,User|UserInterface $user,array $jsonData) : OrderCargoDelivery
    {
        $ocr = new OrderCargoDelivery();
        $ocr->setCargo($orderCargo);
        $ocr->setUser($user);
        $ocr->setJson($jsonData);
        $this->entityManager->persist($ocr);
        $this->entityManager->flush();

        return $ocr;
    }
}