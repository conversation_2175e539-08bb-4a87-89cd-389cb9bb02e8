<?php

namespace App\Service\Order;

use App\Repository\OrderItemBufferRepository;
use Doctrine\ORM\EntityManagerInterface;

class OrderItemBufferService
{
    public function __construct(
        private readonly OrderItemBufferRepository  $repository,
        private readonly EntityManagerInterface $em,
    ){}

    public function setCompletedAtByItem(int $orderItemId) : bool
    {
        $buffer = $this->repository->getActiveByItem($orderItemId);
        if($buffer) {
            $buffer->setCompletedAt(new \DateTimeImmutable());
            $this->em->flush();
            return true;
        }
        return false;
    }
}