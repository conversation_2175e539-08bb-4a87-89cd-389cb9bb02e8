<?php

namespace App\Service\Order;

use App\Service\QueService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Workflow\Exception\TransitionException;
use Symfony\Component\Workflow\WorkflowInterface;
use Wb3\SharedBundle\Entity\OrderItem;

/**
 * Sipariş durum geçişlerini yöneten servis
 * 
 * Bu servis, Symfony Workflow bileşenini kullanarak sipariş durumları
 * arasındaki geçişleri yönetir. Siparişlerin durumlarının değiştirilmesi ve
 * belirli bir duruma geçiş yapılıp yapılamayacağının kontrolünü sağlar.
 */
class OrderItemTransitionService
{
    /**
     * OrderTransitionService yapıcı metodu
     *
     * Workflow ve EntityManager bağımlılıklarını oluşturur.
     *
     * @param WorkflowInterface $orderItemStateMachine
     * @param QueService $queService
     */
    public function __construct(
        readonly WorkflowInterface      $orderItemStateMachine,
        private readonly  QueService             $queService,
    ){}
    
    /**
     * Siparişin durumunu değiştirir
     * 
     * Verilen sipariş nesnesine belirtilen geçişi uygular ve değişikliği kaydeder.
     */
    public function changeTransition(OrderItem $orderItem, string $transition): void
    {
        $this->orderItemStateMachine->apply($orderItem, $transition);

        $this->queService->addToIndexOrder($orderItem->getOrder()->getId());
    }
    
    /**
     * Belirtilen geçişin uygulanabilir olup olmadığını kontrol eder
     * 
     * Verilen nesnenin mevcut durumunda belirtilen geçişin 
     * uygulanabilir olup olmadığını döndürür.
     */
    public function can(Object $object,string $transitionName) : bool
    {
        return $this->orderItemStateMachine->can($object,$transitionName);
    }

    public function validateCan(OrderItem $orderItem, string $transitionName) : void
    {
        if (!$this->can($orderItem, OrderItem::TRANSITION_TO_PRICE_ERROR_NOTIFIED)) {
            throw  new TransitionException($orderItem, $transitionName, $this->orderItemStateMachine, 'TRANSITION_NOT_ALLOWED');
        }

    }

}