<?php

namespace App\Service\Order;

use Wb3\SharedBundle\Entity\Order;

/**
 * Sipariş durumlarını Türkçe başlıklara dönüştüren servis
 * 
 * Bu servis, sipariş durumlarının sistem içindeki kodlarını kullanıcı dostu
 * Türkçe metinlere dönüştürmek için kullanılır.
 */
class OrderMapperService
{
    /**
     * Sipariş durumunu Türkçe başlığa dönüştürür
     * 
     * Sistem içindeki sipariş durum kodlarını kullanıcı arayüzünde
     * görüntülenecek Türkçe metinlere dönüştürür.
     * 
     * @param string $state Dönüştürülecek sipariş durum kodu
     * @return string Türkçe durum başlığı
     */
    public function mapOrderStateToTitle(string $state) : string
    {
        $titles = [
            Order::STATE_WAITING_PAYMENT => 'Ödeme Bekleniyor',
            Order::STATE_NEW => 'Yeni',
            Order::STATE_VENDOR_CANCELED => 'Satıcı İptali',
            Order::STATE_CUSTOMER_CANCEL_REQUESTED => 'Müşteri İptal İsteği',
            Order::STATE_CUSTOMER_CANCELED => 'Müşteri İptali',
            Order::STATE_INVOICED => 'Faturalandırıldı',
            Order::STATE_IN_CARGO => 'Kargoda',
            Order::STATE_DELIVERED => 'Teslim Edildi',
            Order::STATE_UNDELIVERED => 'Teslim Edilmedi',
        ];
        return $titles[$state] ?? $state;
    }
}