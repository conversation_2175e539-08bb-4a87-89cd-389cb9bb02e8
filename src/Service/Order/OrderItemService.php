<?php
namespace App\Service\Order;
use App\Repository\OrderItemRepository;
use App\Service\QueService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\SecurityBundle\Security;
use Wb3\SharedBundle\Entity\OrderItem;
use Wb3\SharedBundle\Entity\OrderItemCheck;
use Wb3\SharedBundle\Type\NotificationRequestType;

/**
 * Sipariş kalemlerini yöneten servis
 * 
 * Bu servis, sipariş içindeki ürün kalemlerinin listelenmesi, kaydedilmesi
 * ve durumlarının değiştirilmesi gibi işlemleri yönetir. Özellikle transfer
 * işlemleri için kullanılır.
 */
class OrderItemService
{
    public function __construct(
        readonly  OrderItemRepository            $repository,
        private readonly  EntityManagerInterface $entityManager,
        private readonly  QueService             $queService,
        private readonly  OrderItemTransitionService $transitionService,
        private readonly Security $security,
    )
    {}
    
    /**
     * Sayfalandırılmış sipariş kalemi listesini döndürür
     * 
     * Belirtilen filtrelere göre sipariş kalemlerini sorgular ve sayfalandırır.
     *
     */
    public function allWithPaginate(array $filters = [], int $page = 1, $limit = 10): array
    {
        return $this->repository->allWithPaginate($page, $limit, $filters);
    }
    
    /**
     * Sipariş kalemi kaydını veritabanına ekler
     * 
     * Verilen sipariş kalemi nesnesini persist eder ve flush işlemi uygular.
     * 
     * @param OrderItem $orderItem Kaydedilecek sipariş kalemi nesnesi
     */
    public function insert(OrderItem $orderItem)
    {
        $this->entityManager->persist($orderItem);
        $this->entityManager->flush();
    }
    
    /**
     * Sipariş kalemini transfer edildi durumuna geçirir
ne olacak içinde     *
     * Sipariş kaleminin durumunu "transferred" olarak değiştirir,
     * tarih alanlarını günceller ve kaydeder.
     * 
     * @param OrderItem $orderItem Durumu değiştirilecek sipariş kalemi
     */
    public function transfer(OrderItem $orderItem,string $name): void
    {

        //state id transfered yap
        //$this->transitionService->validateCan($orderItem, OrderItem::TRANSITION_TO_TRANSFERRED);
        //$this->transitionService->changeTransition($orderItem, OrderItem::TRANSITION_TO_TRANSFERRED);
        $orderItem->setState(OrderItem::STATE_TRANSFERRED);
        $orderItem->setUpdatedAt();
        $this->entityManager->flush();
    }

    public function notifyPriceError(OrderItem $orderItem) : void
    {
        /**
         * Sipariş No: {{orderNumber}}
         * Platform: {{platformCode}}
         * SCS: {{stockCode}}
         * Korgun Renk: {{korgunRenk}}
         * Marka: {{brandName}}
         * Barkod: {{barcode}}
         */
        //Fiyat hatası maili gonder
        $notificationRequestType = new NotificationRequestType();
        $notificationRequestType->templateCode = 'price_error';
        $notificationRequestType->toAddr = json_decode($_ENV['PRICE_ERROR_NOTIFY_EMAILS']); //@TODO to ve cc nasil olacak. ayalrlanacak.
        $notificationRequestType->templateData = [ //sipariş ile ilgili bilgiler burdan data olarak girilecek.
            'orderNumber' => $orderItem->getOrder()->getOrderNumber(),
            'platformCode' => $orderItem->getOrder()->getPlatform()->getCode(),
            'stockCode' => $orderItem->getProductVariant()->getStockCode(),
            'korgunRenk' => $orderItem->getColor(),
            'brandName' => $orderItem->getProductVariant()->getProduct()->getBrand()->getName(),
            'barcode' => $orderItem->getProductVariant()->getBarcode(),
        ];
        $this->queService->addQueueForSendNotification($notificationRequestType);
    }

    public function approve(OrderItemCheck $orderItemCheck,string $description)
    {
        $orderItemCheck->setIsApproved(true);
        $orderItemCheck->setApprovedAt(new \DateTimeImmutable());
        $orderItemCheck->setUser($this->security->getUser());
        $orderItemCheck->setUpdatedAt();
        $orderItemCheck->setDescription($description);
        $this->entityManager->flush();
    }

}