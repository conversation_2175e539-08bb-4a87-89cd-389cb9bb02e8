<?php

namespace App\Service\Order;

use App\Repository\OrderAddressRepository;
use Wb3\SharedBundle\Entity\OrderAddress;

class OrderAddressService
{
    public function __construct(
        private readonly OrderAddressRepository $repository
    ){}

    public function getInvoiceAddress(int $orderId) : OrderAddress | null
    {
            return $this->repository->getInvoiceAddressByOrderId($orderId);
    }

    public function getShipmentAddress(int $orderId) : OrderAddress | null
    {
        return $this->repository->getShippingAddressByOrderId($orderId);
    }

}