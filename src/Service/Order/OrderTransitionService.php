<?php

namespace App\Service\Order;

use App\Service\QueService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Workflow\WorkflowInterface;
use Wb3\SharedBundle\Entity\Order;

/**
 * Sipariş durum geçişlerini yöneten servis
 * 
 * Bu servis, Symfony Workflow bileşenini kullanarak sipariş durumları
 * arasındaki geçişleri yönetir. Siparişlerin durumlarının değiştirilmesi ve
 * belirli bir duruma geçiş yapılıp yapılamayacağının kontrolünü sağlar.
 */
class OrderTransitionService
{
    /**
     * OrderTransitionService yapıcı metodu
     * 
     * Workflow ve EntityManager bağımlılıklarını oluşturur.
     * 
     * @param WorkflowInterface $orderStateMachine Sipariş durum makinesi
     * @param EntityManagerInterface $entityManager Doctrine entity manager
     */
    public function __construct(
        private readonly WorkflowInterface $orderStateMachine,
        private readonly EntityManagerInterface $entityManager,
        private readonly QueService $queService
    ){}
    
    /**
     * Siparişin durumunu değiştirir
     * 
     * Verilen sipariş nesnesine belirtilen geçişi uygular ve değişikliği kaydeder.
     * 
     * @param Order $order Durum değişikliği uygulanacak sipariş
     * @param string $transition Uygulanacak geçiş (transition) adı
     */
    public function changeTransition(Order $order, string $transition): void
    {
        $this->orderStateMachine->apply($order, $transition);
    
        $this->entityManager->flush();

        $this->queService->addToIndexOrder($order->getId());
    }
    
    /**
     * Belirtilen geçişin uygulanabilir olup olmadığını kontrol eder
     * 
     * Verilen nesnenin mevcut durumunda belirtilen geçişin 
     * uygulanabilir olup olmadığını döndürür.
     * 
     * @param Object $object Kontrol edilecek nesne
     * @param string $transitionName Kontrol edilecek geçiş adı
     * @return bool Geçiş uygulanabilirse true, aksi halde false
     */
    public function can(Object $object,string $transitionName) : bool
    {
        return $this->orderStateMachine->can($object,$transitionName);
    }

}