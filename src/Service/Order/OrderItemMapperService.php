<?php

namespace App\Service\Order;

use Wb3\SharedBundle\Entity\OrderItem;

class OrderItemMapperService
{

    public function mapOrderItemStateToTitle(string $state) : string
    {
        $titles = [
            OrderItem::STATE_NEW => 'Yeni',
            OrderItem::STATE_OUT_OF_STOCK => 'Stokta Yok',
            OrderItem::STATE_OUT_OF_STOCK_NOTIFIED => 'Stokta Yok Bildirildi',
            OrderItem::STATE_PRICE_ERROR => 'Fiyat Hatası',
            OrderItem::STATE_PRICE_ERROR_NOTIFIED => 'Fiyat Hatası Bildirildi',
            OrderItem::STATE_LOCATION_DEFINED => 'Lokasyon tanımlandı.',
            OrderItem::STATE_READY => 'Hazır. Sevk bekleniyor...',
            OrderItem::STATE_TRANSFERRED => 'Transfer Edildi',
            OrderItem::STATE_VENDOR_CANCELED => 'Satıcı İptal etti',
            OrderItem::STATE_CUSTOMER_CANCELED => 'Müşteri Iptal Etti',
            OrderItem::STATE_CUSTOMER_RETURNED => 'Müşteriden Geri Döndü',
            OrderItem::TRANSITION_TO_TRANSFERRED => 'Sevk Edildi',
        ];
        return $titles[$state] ?? $state;
    }

}