<?php

namespace App\Service\Order;

use Doctrine\ORM\EntityManagerInterface;
use Wb3\SharedBundle\Entity\Order;
use Wb3\SharedBundle\Entity\OrderInvoice;

class OrderInvoiceService
{
    public function __construct(
        private readonly EntityManagerInterface $entityManager
    ){}

    public function create(Order $order, string $invoiceNumber): void
    {
        $oi = new OrderInvoice();
        $oi->setOrder($order);
        $oi->setInvoiceNumber($invoiceNumber);
        $oi->setCreatedAt()->setUpdatedAt();

        $this->entityManager->persist($oi);
        $this->entityManager->flush();
    }
}