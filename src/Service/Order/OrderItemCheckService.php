<?php

namespace App\Service\Order;

use App\Repository\OrderItemCheckRepository;

class OrderItemCheckService
{

    public function __construct(
        public readonly  OrderItemCheckRepository $repository
    ){}

    public function allApprovedsWithPaginate()
    {
        return $this->repository->allApproverWithPaginate();
    }

    public function allWithPaginate(int $page = 1, $limit = 10, array $filters = [])
    {
        return $this->repository->allWithPaginate($page, $limit,$filters);
    }
}