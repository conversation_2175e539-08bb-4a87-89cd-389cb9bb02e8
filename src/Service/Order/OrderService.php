<?php

namespace App\Service\Order;
use App\Repository\OrderRepository;
use App\Service\Korgun\Soap\KorgunSoapService;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\QueryBuilder;
use Psr\Log\LoggerInterface;
use Wb3\SharedBundle\Entity\Order;

/**
 * Sipariş işlemlerini yöneten ana servis
 * 
 * Bu servis, sipariş verilerinin alınması, filtrelenmesi, gruplandırılması ve 
 * kaydedilmesi gibi temel sipariş işlemlerini gerçekleştirir.
 */
class OrderService
{
    /**
     * Sipariş servisi yapıcı metodu
     * 
     * Repository ve registry bağımlılıklarını oluşturur.
     */
    public function __construct(
        readonly OrderRepository        $repository,
        private readonly EntityManagerInterface $entityManager,
        private readonly KorgunSoapService $korgunSoapService,
        private readonly OrderInvoiceService $orderInvoiceService,
        private readonly \App\Service\Wb2\OrderService $wb2OrderService,
        private readonly OrderTransitionService $transitionService,
        private readonly OrderItemBufferService $orderItemBufferService,
        private readonly LoggerInterface $logger,
    ) {}
    /**
     * Sayfalandırılmış sipariş listesini döndürür
     * 
     * Belirtilen filtrelere göre siparişleri sorgular ve sayfalandırır.
     * 
     * @param array $filters Uygulanacak filtreler
     * @param int $page Sayfa numarası
     * @param int $limit Sayfa başına kayıt sayısı
     * @return array Sayfalandırılmış sipariş listesi
     */
    public function allWithPaginate(array $filters = [], int $page = 1, $limit = 10): array
    {
    
        return $this->repository->allWithPaginate($page, $limit, $filters);
    }

    public function personalSaleReport(?string $startDate = null, ?string $endDate = null): array
    {

        return (array) $this->repository->personalSaleReport($startDate, $endDate);
    }
    
    /**
     * Siparişleri durumlarına göre gruplandırır
     * 
     * Belirtilen filtrelere göre siparişleri durumlarına göre gruplandırır.
     * 
     * @param array $filters Uygulanacak filtreler
     * @return array Durumlara göre gruplandırılmış sipariş sayıları
     */
    public function allGroupBySate(array $filters = []): array
    {
        return $this->repository->allGroupByState($filters);
    }
    
    /**
     * Sipariş kaydını veritabanına ekler
     * 
     * Verilen sipariş nesnesini persist eder ve flush işlemi uygular.
     * 
     * @param Order $order Kaydedilecek sipariş nesnesi
     */
    public function insert(Order $order)
    {
        $this->entityManager->persist($order);
        $this->entityManager->flush();
    }

    public function purchase(Order $order): void
    {
        //faturalandır
        $purchaseData = $this->korgunSoapService->savePurchase($order);

        //Taslak faturayı kaydet.
        $this->orderInvoiceService->create($order, $purchaseData['invoiceNumber']);

        //state i atlat
      $order->setState(Order::STATE_INVOICED);
      $this->entityManager->flush();

       //orderItem lerdeki buffer lari düşür.
        foreach($order->getItems() AS $item) {
            $this->orderItemBufferService->setCompletedAtByItem($item->getId());
        }

        //wb2 den state i düşür
        $this->wb2OrderService->updateStatusToInCargo($order);
    }

}