<?php
namespace App\Processor;

use Enqueue\Client\CommandSubscriberInterface;
use Interop\Queue\Context;
use Interop\Queue\Message;
use Interop\Queue\Processor;

class SendNotificationProcessor implements Processor, CommandSubscriberInterface
{
    public function __construct(){}
    public function process(Message $message, Context $context) : string
    {
        return self::ACK;
    }

    public static function getSubscribedCommand(): array
    {
        return [
            'command' => 'SendNotificationProcessor',
            'queue' => 'send_notification',
            'prefix_queue' => true,
            'exclusive' => false
        ];
    }
}