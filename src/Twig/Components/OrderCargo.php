<?php

namespace App\Twig\Components;

use App\Repository\OrderCargoDeliveryRepository;
use App\Repository\OrderCargoRepository;
use App\Repository\OrderRepository;
use App\Service\Order\OrderCargoDeliveryService;
use Doctrine\Common\Collections\Collection;
use Faker\Provider\Base;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\UX\LiveComponent\Attribute\AsLiveComponent;
use Symfony\UX\LiveComponent\Attribute\LiveAction;
use Symfony\UX\LiveComponent\Attribute\LiveProp;
use Symfony\UX\LiveComponent\ComponentToolsTrait;
use Symfony\UX\LiveComponent\DefaultActionTrait;
use Wb3\SharedBundle\Controller\BaseController;
use Wb3\SharedBundle\Entity\Order;
use Wb3\SharedBundle\Entity\OrderCargoDelivery;
use Wb3\SharedBundle\Entity\OrderTrack;
use Wb3\SharedBundle\Entity\Platform;

#[AsLiveComponent]
final class OrderCargo extends BaseController
{
    use DefaultActionTrait;
    use ComponentToolsTrait;
    #[LiveProp]
    public \Wb3\SharedBundle\Entity\OrderCargo $orderCargo;

    #[LiveProp(writable: true)]
    public string $barcode = '';

    #[LiveProp(writable: true)]
    public array $validOrders = [];
    #[LiveProp(writable: true)]
    public array $invalidOrders = [];
    #[LiveProp(writable: true)]
    public ?int $printedCargoId = null;
    #[LiveProp(writable: true)]
    public string $printedDate;
    #[LiveProp]
    public array|null $deliveries = [];
    public array $orderCargoes = [];

    public function __construct(
        private Security                  $security,
        private OrderCargoDeliveryService $deliveryService,
        private OrderRepository           $orderRepository,
        private OrderCargoRepository      $orderCargoRepository, private readonly OrderCargoDeliveryRepository $orderCargoDeliveryRepository,
    )
    {
        $this->printedDate = date("Y-m-d");
        $this->orderCargoes = $this->orderCargoRepository->findBy([],['id'=>'DESC']);
        $this->allDeliveries();
    }
    #[LiveAction]
    public function addOrderByBarcode(): void
    {
        $order = $this->orderRepository->getByBarcode($this->barcode);

        if($order) {
            $decoratedOrder = $this->decorateOrder($order);
            if($this->validateOrder($order)){
            $this->validOrders[] = $decoratedOrder;
                $this->dispatchBrowserEvent('get-sound', ['type' => 'success']);
        } else {
                $this->invalidOrders[] = $decoratedOrder;
                $this->dispatchBrowserEvent('get-sound', ['type' => 'error']);
            }

        } else {
          $order = new Order();
          $order->setPlatform(new Platform());
          $order->setTrack((new OrderTrack())->setTrackNumber($this->barcode));
          $this->invalidOrders[] = $this->decorateOrder($order);

            $this->dispatchBrowserEvent('get-sound', ['type' => 'error']);
        }
        $this->barcode = '';
    }
    #[LiveAction]
    public function clearList(): void
    {
        $this->validOrders = [];
        $this->invalidOrders = [];
    }

    private function decorateOrder(Order $order) : array
    {
        return [
            'customerName'=>$order->getCustomer()?->getName() ?? '?',
            'orderNumber'=>$order->getOrderNumber() ?? '?',
            'trackNumber' => $order->getTrack()->getTrackNumber() ?? '?',
            'orderAt' => $order->getOrderAt()?->format("d-m-Y H:i:s") ?? '?',
            'readAt' => date("d-m-Y H:i:s"),
            'platformTitle' => $order->getPlatform()?->getTitle() ?? '?',
            'cargoName' => $order->getCargo()?->getName() ?? '?',
            'desi'  => $order->getItems()[0]?->getDesi() ?? '?',
        ];
    }

    private function validateOrder(Order $order) :bool
    {
        if($order->getCargo()->getId() !== $this->orderCargo->getId()) {
            return false;
        }

        return true;
    }
    #[LiveAction]
    public function print()
    {
        if(count($this->validOrders) == 0) {
            $this->dispatchBrowserEvent('print-list-empty');
            return false;
         }
        $user = $this->security->getUser();

        $pcr = $this->deliveryService->insert($this->orderCargo,$user,$this->validOrders);

        return $this->redirect($this->generateUrl('app_ordercargo_report',['orderCargoDelivery' =>$pcr->getId()]));
    }

    #[LiveAction]
    public function allDeliveries() : void
    {
        $data = [];

       $result = $this->deliveryService->repository->getByCargoAndDate($this->printedCargoId,$this->printedDate);
       foreach ($result AS $item) {
           $data[] = $this->decorateDelivery($item);
       }
       $this->deliveries = $data;
    }

    private function decorateDelivery(OrderCargoDelivery $orderCargoDelivery) : array
    {
        return [
            'id' => $orderCargoDelivery->getId(),
            'createdAt' => $orderCargoDelivery->getCreatedAt()?->format("d-m-Y H:i:s"),
            'cargoName' => $orderCargoDelivery->getCargo()?->getName(),
        ];
    }


}
