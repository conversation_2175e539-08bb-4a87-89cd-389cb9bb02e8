<?php
namespace App\Twig;

use App\Service\Order\OrderItemMapperService;
use App\Service\Order\OrderMapperService;
use App\Service\ProductMapperService;
use Twig\Extension\AbstractExtension;
use Twig\TwigFilter;

class AppExtension extends AbstractExtension
{
    public function __construct(
        private readonly OrderMapperService $orderMapperService,
        private readonly OrderItemMapperService $orderItemMapperService
    )
    {
    }

    public function getFilters(): array
    {
        return [
            new TwigFilter('mapOrderStateToTitle', [$this, 'mapOrderStateToTitle']),
            new TwigFilter('mapOrderItemStateToTitle', [$this, 'mapOrderItemStateToTitle']),
            new TwigFilter('formatPrice', [$this, 'formatPrice']),
        ];
    }

    public function mapOrderStateToTitle(string $state): string
    {
        return $this->orderMapperService->mapOrderStateToTitle($state);
    }

    public function mapOrderItemStateToTitle(string $state): string
    {
        return $this->orderItemMapperService->mapOrderItemStateToTitle($state);
    }
    public function formatPrice(float $price): string
    {
        return number_format($price,2,'.',',');
    }
}