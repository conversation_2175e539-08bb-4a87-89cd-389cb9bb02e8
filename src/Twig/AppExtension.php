<?php
namespace Wb3\SharedBundle\Twig;

use App\Service\ProductMapperService;
use Twig\Extension\AbstractExtension;
use Twig\TwigFilter;

class AppExtension extends AbstractExtension
{
    public function __construct(
        private readonly ProductMapperService $productMapperService
    )
    {
    }

    public function getFilters(): array
    {
        return [
            new TwigFilter('mapStateToTitle', [$this, 'mapStateToTitle']),
        ];
    }

    public function mapStateToTitle(string $state): string
    {
        return $this->productMapperService->mapStateToTitle($state);
    }
}