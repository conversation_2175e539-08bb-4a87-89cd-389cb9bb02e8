# see https://symfony.com/doc/current/reference/configuration/framework.html
framework:
    secret: '%env(APP_SECRET)%'
    #csrf_protection: true
    annotations: false
    http_method_override: false
    handle_all_throwables: true

    # Enables session support. Note that the session will ONLY be started if you read or write from it.
    # Remove or comment this section to explicitly disable session support.
    session:
        enabled: true
        name: wb3_session
        handler_id: Symfony\Component\HttpFoundation\Session\Storage\Handler\PdoSessionHandler
        cookie_lifetime: 604800    # <PERSON><PERSON> (saniye cinsinden)
        cookie_domain: "%env(COOKIE_DOMAIN)%"  # Tüm subdomainler için geçerli

    #esi: true
    #fragments: true
    php_errors:
        log: true

when@test:
    framework:
        test: true
        session:
            storage_factory_id: session.storage.factory.mock_file