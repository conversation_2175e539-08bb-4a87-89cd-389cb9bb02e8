security:
    enable_authenticator_manager: true
    #    encoders:
    #        # use your user class name here
    #        Wb3\SharedBundle\Entity\User: bcrypt
    #        Symfony\Component\Security\Core\User\InMemoryUser: bcrypt

    password_hashers:
        # auto hasher with default options for the User class (and children)
        Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface: 'auto'

    providers:
        # used to reload user from session & other features (e.g. switch_user)
        app_user_provider:
            entity:
                class: Wb3\SharedBundle\Entity\User
                property: email

    firewalls:
        dev:
            pattern: ^/(_(profiler|wdt)|css|images|js)/
            security: false

        main:
            pattern: ^/
            lazy: true
            stateless: false  # TokenStorage için false olmalı
            provider: app_user_provider
            custom_authenticator: Wb3\SharedBundle\Security\LoginFormAuthenticator
            entry_point: Wb3\SharedBundle\Security\LoginFormAuthenticator
            logout:
                path: app_security_logout
            remember_me:
                secret: "%kernel.secret%"
                lifetime: 604800 # 7 gün
                path: /
                domain: "%env(COOKIE_DOMAIN)%"  # Cookie'nin geçerli o<PERSON>ğı alan

    role_hierarchy:
        ROLE_ADMIN: [ROLE_STORE_CASE,ROLE_ORDER_TEAM_LEAD,ROLE_CATEGORY_TEAM_LEAD,ROLE_ACCOUNTING_TEAM_LEAD,ROLE_SUPERVISOR,ROLE_CARGO_TEAM_LEAD]
        ROLE_ACCOUNTING_TEAM_LEAD: [ROLE_ORDER_TEAM_LEAD,ROLE_CATEGORY_TEAM_LEAD]
        ROLE_STORE_CASE: [ROLE_STORE_USER]
        ROLE_ORDER_TEAM_LEAD: [ROLE_ORDER_USER]
        ROLE_CATEGORY_TEAM_LEAD: [ROLE_CATEGORY_USER]
