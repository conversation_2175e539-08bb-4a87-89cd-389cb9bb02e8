framework:
  workflows:
    order_item:
      type: 'state_machine'
      audit_trail:
        enabled: true
      marking_store:
        type: 'method'
        property: 'state'
      supports:
        - Wb3\SharedBundle\Entity\OrderItem
      initial_marking: new
      places:
        - new
        - out_of_stock
        - out_of_stock_notified
        - price_error #order gecmeyecek
        - price_error_notified #order gecmeyecek order price check te onaylandiginda ready e gececek.
        - location_defined
        - ready #urun hazir. transfer ve satışı yapılacak.
        - transferred
        - vendor_canceled
        - customer_canceled
        - customer_returned
      transitions:
        toLocationDefined:
          from: new
          to: location_defined
        toOutOfStock:
          from: new
          to: out_of_stock
        toOutOfStockNotified:
          from: out_of_stock
          to: out_of_stock_notified
        toPriceError:
          from: [new,location_defined]
          to: price_error
        toTransferred:
          from: [ price_error, price_error_notified,ready ]
          to: transferred
        toPriceErrorNotified:
          from: [ price_error ]
          to: price_error_notified
        toCustomerCanceled:
          from: [ new, location_defined, out_of_stock, transferred]
          to: customer_canceled
        toVendorCanceled:
          from: [ new, location_defined, out_of_stock, transferred]
          to: vendor_canceled
        toReady:
          from: [location_defined, price_error,price_error_notified]
          to: [ready]
# şema için komut: php bin/console workflow:dump order_item | dot -Tpng -o workflow/order_item.png (client ten calıstırılacak.)


# farklı urun gonderilince order_item uzerinde nasıl bir degisiklik olacak.
# item uzerine değişik urun gonderildi diye flag koyonacak. ve aciklama alanı cıkacak. aciklama eklenecek. degisiklik olan itemlara aciklama mecburi olacak.
# item üzerinde cift oldugunda farklı lokasyonlarda olabilecegi için 2 item item tabblosune tek tek yazılacak.

#paket ayırmada paket bazlı fatura kesilecek.