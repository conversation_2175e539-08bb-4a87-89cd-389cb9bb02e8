<?php
namespace Wb3\SharedBundle\Security\Voter;

use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Authorization\Voter\Voter;
use Symfony\Component\Security\Core\User\UserInterface;

class ProductAttributeVoter extends Voter
{
    // Voter'ın desteklediği işlemler
    public const VIEW = 'view';

    protected function supports(string $attribute, mixed $subject): bool
    {
        return in_array($attribute, [
            self::VIEW,
            //self::EDIT, self::STOCK, self::UPDATE, self::REINDEX
        ]);
    }

    protected function voteOnAttribute(string $attribute, mixed $subject, TokenInterface $token): bool
    {
        $user = $token->getUser();
        // Eğer kullanıcı oturum açmamışsa erişimi reddet
        if (!$user instanceof UserInterface) {
            return false;
        }

        // Kullanıcının rollerine göre işlem kontrolü
        return match($attribute) {
            self::VIEW => $this->canView($user),
            default => false,
        };
    }

    private function canView(UserInterface $user): bool
    {
        return true;
        //in_array('ROLE_', $user->getRoles());
            //|| in_array('ROLE_ADMIN', $user->getRoles());
    }
}