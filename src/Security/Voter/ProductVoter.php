<?php
namespace Wb3\SharedBundle\Security\Voter;

use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Authorization\Voter\Voter;
use Symfony\Component\Security\Core\User\UserInterface;
use Wb3\SharedBundle\Entity\User;

class ProductVoter extends Voter
{
    // Voter'ın desteklediği işlemler
    public const VIEW = 'view';
    //public const EDIT = 'edit';
    //public const STOCK = 'stock';
    //public const UPDATE = 'update';
    //public const REINDEX = 'reindex';

    protected function supports(string $attribute, mixed $subject): bool
    {
        return in_array($attribute, [
            self::VIEW,
            //self::EDIT, self::STOCK, self::UPDATE, self::REINDEX
        ]);
    }

    protected function voteOnAttribute(string $attribute, mixed $subject, TokenInterface $token): bool
    {
        $user = $token->getUser();
        // <PERSON><PERSON><PERSON> kullan<PERSON>cı oturum açmamışsa erişimi reddet
        if (!$user instanceof UserInterface) {
            return false;
        }

        // Kullanıcının rollerine göre işlem kontrolü
        return match($attribute) {
            self::VIEW => $this->canView($user),
            //self::EDIT => $this->canEdit($user),
            //self::STOCK => $this->canViewStock($user),
            //self::UPDATE => $this->canUpdate($user),
            //self::REINDEX => $this->canReindex($user),
            default => false,
        };
    }

    private function canView(UserInterface $user): bool
    {
        return true;
        //in_array('ROLE_PRODUCT_USER', $user->getRoles());
            //|| in_array('ROLE_ADMIN', $user->getRoles());
    }

    //private function canEdit(UserInterface $user): bool
    //{
    //    // Sadece ROLE_EDITOR ve ROLE_ADMIN düzenleyebilir
    //    return in_array('ROLE_EDITOR', $user->getRoles())
    //        || in_array('ROLE_ADMIN', $user->getRoles());
    //}
    //
    //private function canViewStock(UserInterface $user): bool
    //{
    //    // Stok bilgisini sadece ROLE_STOCK ve ROLE_ADMIN görebilir
    //    return in_array('ROLE_STOCK', $user->getRoles())
    //        || in_array('ROLE_ADMIN', $user->getRoles());
    //}
    //
    //private function canUpdate(UserInterface $user): bool
    //{
    //    return in_array('ROLE_EDITOR', $user->getRoles())
    //        || in_array('ROLE_ADMIN', $user->getRoles());
    //}
    //
    //private function canReindex(UserInterface $user): bool
    //{
    //    // Reindex yapabilecek roller
    //    return in_array('ROLE_ADMIN', $user->getRoles());
    //}
}