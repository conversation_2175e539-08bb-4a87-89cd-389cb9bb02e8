<?php

namespace App\Type\Cargo\Surat;

use Symfony\Component\Validator\Constraints as Assert;

class GonderiType
{
    #[Assert\NotBlank]
    public string $KisiKurum;
    public string $SahisBirim;
    #[Assert\NotBlank]
    public string $AliciAdresi;
    #[Assert\NotBlank]
    public string $Il;
    #[Assert\NotBlank]
    public string $Ilce;
    public string $TelefonIs;
    public string $TelefonEv;
    public string $TelefonCep;
    #[Assert\NotBlank]
    public string $Email;
    public string $AliciKodu;
    #[Assert\NotBlank]
    public int $KargoTuru;
    #[Assert\NotBlank]
    public int $OdemeTipi;
    #[Assert\NotBlank]
    public string $IrsaliyeSeriNo;
    #[Assert\NotBlank]
    public string $IrsaliyeSiraNo;
    public string $ReferansNo;
    #[Assert\NotBlank]
    public string $OzelKargoTakipNo;
    #[Assert\NotBlank]
    public int $Adet;
    #[Assert\NotBlank]
    public string $BirimDesi;
    #[Assert\NotBlank]
    public string $BirimKg;
    #[Assert\NotBlank]
    public string $KargoIcerigi;
    #[Assert\Choice([0,1])]
    #[Assert\NotBlank]
    public int $KapidanOdemeTahsilatTipi;
    #[Assert\NotBlank]
    public string $KapidanOdemeTutari;
    #[Assert\NotBlank]
    public string $EkHizmetler;
    #[Assert\NotBlank]
    public string $SevkAdresi;
    #[Assert\NotBlank]
    public int $TeslimSekli;
    #[Assert\NotBlank]
    public int $TasimaSekli;
    //public string $BayiNo;
    //public string $EntegrasyonId;
    //public string $EntegrasyonHesaplamaTuru;
    #[Assert\Type("integer")]
    #[Assert\NotBlank]
    public string $GonderiSekli;
    #[Assert\Type("string")]
    #[Assert\NotBlank]
    public string $TeslimSubeKodu;
    #[Assert\Type("integer")]
    #[Assert\Choice([0,1])]
    #[Assert\NotBlank]
    public int $Pazaryerimi;
    #[Assert\Type("string")]
    #[Assert\NotNull]
    public string $EntegrasyonFirmasi;
    #[Assert\Type("integer")]
    #[Assert\Choice([0,1])]
    #[Assert\NotNull]
    public string $Iademi;
}

/**
 *  'Gonderi' => [
 *  'KisiKurum' => ucwords($name),
 *  'SahisBirim' => '',
 *  'AliciAdresi' => $shippingAddress->getAddress(),
 *  'Il' => $city,
 *  'Ilce' => $district,
 *  'TelefonEv' => '',
 *  'TelefonIs' => '',
 *  'TelefonCep' => $shippingAddress->getPhone(),
 *  'Email' => $shippingAddress->getOrder()->getCustomer()->getMail(),
 *  'AliciKodu' => '',-
 *  'KargoTuru' => 3,
 *  'OdemeTipi' => 1,
 *  'IrsaliyeSeriNo' => substr($orderInvoice->getInvoiceNumber(),0,3), //@TODO Fatura Seni NO
 *  'IrsaliyeSiraNo' => substr($orderInvoice->getInvoiceNumber(),3), //@TODO Fatura InvoiceNo
 *  'ReferansNo' => '',
 *  'OzelKargoTakipNo' => $order->getPlatform()->getCode() . $order->getOrderNumber(),
 *  'Adet' => count($order->getItems()),
 *  'BirimDesi' => 1,
 *  'BirimKg' => 1,
 *  'KargoIcerigi' => '',-
 *  'KapidanOdemeTahsilatTipi' => ((double)str_replace(',', '.', $cashOnDeliveryCost)) > 0 ? 1 : 0,
 *  'KapidanOdemeTutari' => ((double)str_replace(',', '.', $cashOnDeliveryCost)) > 0 ? ((double)str_replace(',', '.', $cashOnDeliveryCost)) : 0,
 *  'EkHizmetler' => '',
 *  'SevkAdresi' => '',
 *  'TeslimSekli' => 1,
 *  'TasimaSekli' => 1,
 *  //'BayiNo' => '',
 *  //'EntegrasyonId' => '',
 *  //'EntegrasyonHesaplamaTuru' => '',
 *  'GonderiSekli' => 0, //0 Standart gönderi, 5 Bukoli, 8 Pudo-
 *  'TeslimSubeKodu' => '', //Sürat Kargo Teslim Noktasını (Şubeleri) ifade etmektedir.
 *  'Pazaryerimi' => 0, //1 Pazaryeri ise, 0 Pazaryeri değil ise
 *  'EntegrasyonFirmasi' => '',
 *  'Iademi' => 0, //0 Standart gönderi, 1 İade
 *  ]
 */