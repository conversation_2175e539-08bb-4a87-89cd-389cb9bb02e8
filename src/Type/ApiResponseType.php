<?php

namespace App\Type;

use Symfony\Component\Validator\Constraints as Assert;

final class ApiResponseType
{
    public function __get($property) {
        if (property_exists($this, $property)) {
            return $this->$property;
        }
    }

    public function __set($property, $value) {
        if (property_exists($this, $property)) {
            $this->$property = $value;
        }
        return $this;
    }

    /**
     * @var bool
     */
    public $success;

    /**
     * @var int
     */
    public $errorCode;

    /**
     * @var string
     */
    public $message;

    /**
     * @var array
     */
    public $data;

    /**
     * @var array
     */
    public $errors;

    /**
     * @var string
     */
    public $request;

}

