<?php
namespace Wb3\SharedBundle\Type\Task\Upi;

class ItemType {
    public string $barcode;
    public int $quantity;
    public ?float $listPrice = null;
    public float $salePrice;


    public function toArray(): array
    {
        return [
            'barcode' => $this->barcode,
            'quantity' => $this->quantity,
            'listPrice' => $this->listPrice,
            'salePrice' => $this->salePrice
        ];

    }
}
?>