<?php

namespace App\Repository;

use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use Wb3\SharedBundle\Entity\ProductAttributeValue;
use Wb3\SharedBundle\Helper\PaginatorUtil;

class ProductAttributeValueRepository extends ServiceEntityRepository
{

    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, ProductAttributeValue::class);
    }

    public function allWithPaginate(int $page = 1, int $limit = 10, array $filters = []): array
    {
        $qb = $this->createQueryBuilder("pa")
            ->select('pa');

        $qb->orderBy('pa.updatedAt', 'DESC');
        $qb->setMaxResults($limit)->setFirstResult(($page - 1) * $limit);
        $data = PaginatorUtil::paginate($qb->getQuery(), $page, $limit);
        return $data;
    }

}
