<?php

namespace App\Repository;

use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use Wb3\SharedBundle\Entity\OrderInvoice;
use Wb3\SharedBundle\Entity\OrderItemBuffer;
use Wb3\SharedBundle\Entity\ProductBrand;

/**
 * @extends ServiceEntityRepository<ProductBrand>
 *
 * @method OrderItemBuffer|null find($id, $lockMode = null, $lockVersion = null)
 * @method OrderItemBuffer|null findOneBy(array $criteria, array $orderBy = null)
 * @method OrderItemBuffer[]    findAll()
 * @method OrderItemBuffer[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class OrderItemBufferRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, OrderItemBuffer::class);
    }

    public function getActiveByItem(int $orderItemId) : ?OrderItemBuffer
    {
        $qb = $this->createQueryBuilder('oib')
            ->where('oib.id = :orderItemId')
            ->andWhere('oib.completedAt is null')
             ->setParameter('orderItemId', $orderItemId)
             ->setMaxResults(1);

        return $qb->getQuery()->getOneOrNullResult();
    }

}
