<?php

namespace App\Repository;

use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use Wb3\SharedBundle\Entity\OrderCargoDelivery;

/**
 * @extends ServiceEntityRepository<\Wb3\SharedBundle\Entity\OrderCargoDelivery>
 */
class OrderCargoDeliveryRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, OrderCargoDelivery::class);
    }

    public function getByCargoAndDate(?int $cargoId, string $date)
    {
        $start = $date . ' 00:00:00';
        $end = $date . ' 23:59:59';

        $qb = $this->createQueryBuilder('ocd')
            ->andWhere('ocd.createdAt BETWEEN :start AND :end')
            ->setParameter('start', $start)
            ->setParameter('end', $end)
            ->setMaxResults(10)
            ->orderBy('ocd.id', 'desc');
        if($cargoId) {
            $qb->andWhere('ocd.cargo = :cargoId')
                ->setParameter('cargoId', $cargoId);
        }
        return $qb->getQuery()->getResult();
    }
}
