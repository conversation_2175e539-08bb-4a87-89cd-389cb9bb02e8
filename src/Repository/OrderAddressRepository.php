<?php

namespace App\Repository;

use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\DBAL\ArrayParameterType;
use Doctrine\DBAL\ParameterType;
use Doctrine\Persistence\ManagerRegistry;
use Wb3\SharedBundle\Entity\OrderAddress;
use Wb3\SharedBundle\Helper\PaginatorUtil;
use Doctrine\ORM\EntityManagerInterface;


/**
 * @extends ServiceEntityRepository<\Wb3\SharedBundle\Entity\OrderAddress>
 */
class OrderAddressRepository extends ServiceEntityRepository
{
    public EntityManagerInterface $em;

    public function __construct(ManagerRegistry $registry, EntityManagerInterface $em)
    {
        $this->em = $em;

        parent::__construct($registry, OrderAddress::class);
    }
    public function allWithPaginate(int $page, mixed $limit, array $filters): array
    {
        $qb = $this->createQueryBuilder("oca")
            ->select('oca')
            ->orderBy('oca.id', 'DESC')
            ->setMaxResults($limit)
            ->setFirstResult(($page - 1) * $limit);

        // PaginatorUtil'den veriyi alıyoruz
        $paginationData = PaginatorUtil::paginate($qb->getQuery(), $page, $limit);

        return $paginationData;
    }
    public function insert(OrderAddress $orderCustomerAddress): OrderAddress
    {
        $this->em->persist($orderCustomerAddress);
        $this->em->flush();
        return $orderCustomerAddress;
    }

    public function getShippingAddressByOrderId(int $orderId) : ?OrderAddress
    {
        $addressType = OrderAddress::ID_SHIPPING_TYPE;
        $qb = $this->createQueryBuilder("oa")
            ->select('oa')
            ->where('oa.order= :orderId')->setParameter('orderId',$orderId)
            ->andWhere('oa.type = :addressType')->setParameter('addressType', $addressType)
            ->setMaxResults(1);

        return $qb->getQuery()->getOneOrNullResult();
    }

    public function getInvoiceAddressByOrderId(int $orderId) : ?OrderAddress
    {
        $addressTypes = [OrderAddress::ID_INVOICE_TYPE,OrderAddress::ID_INVOICE_TYPE_MODIFIED];
        $qb = $this->createQueryBuilder("oa")
            ->select('oa')
            ->where('oa.order= :orderId')->setParameter('orderId',$orderId)
            ->andWhere('oa.type IN (:addressTypes)')->setParameter('addressTypes',$addressTypes,ArrayParameterType::INTEGER)
            ->orderBy('oa.id', 'DESC')
            ->setMaxResults(1);

        return $qb->getQuery()->getOneOrNullResult();
    }



    //    /**
    //     * @return OrderAddress[] Returns an array of OrderAddress objects
    //     */
    //    public function findByExampleField($value): array
    //    {
    //        return $this->createQueryBuilder('o')
    //            ->andWhere('o.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->orderBy('o.id', 'ASC')
    //            ->setMaxResults(10)
    //            ->getQuery()
    //            ->getResult()
    //        ;
    //    }

    //    public function findOneBySomeField($value): ?OrderAddress
    //    {
    //        return $this->createQueryBuilder('o')
    //            ->andWhere('o.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->getQuery()
    //            ->getOneOrNullResult()
    //        ;
    //    }
}
