<?php

namespace App\Repository\Wb2;

use Doctrine\Persistence\ManagerRegistry;
use Wb3\SharedBundle\Service\Wb2\MapperService;

class OrderRepository
{
    CONST SATUS_CARGO_AND_FINISHED = 22;

    public function __construct(
        public ManagerRegistry $registry,
    ){}

    public function updateStatusToInCargo(string $platformCode, string $orderNumber) : void
    {
        $platformCode = MapperService::mapToPlatformCode($platformCode);
        $sql = "UPDATE Orders SET status=".self::SATUS_CARGO_AND_FINISHED.",KorgunOrderId=0 WHERE Platform='$platformCode' AND OrderNumber='$orderNumber'";
        $connection = $this->registry->getConnection("wb2");
        $connection->executeQuery($sql);
    }
}
