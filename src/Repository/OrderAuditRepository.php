<?php

namespace App\Repository;

use Doctrine\ORM\EntityManagerInterface;
use Doctrine\Persistence\ManagerRegistry;
use Wb3\SharedBundle\Entity\OrderAudit;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Wb3\SharedBundle\Helper\PaginatorUtil;

/**
 * @extends ServiceEntityRepository<OrderAudit>
 *
 *
 * @method OrderAudit|null find($id, $lockMode = null, $lockVersion = null)
 * @method OrderAudit|null findOneBy(array $criteria, array $orderBy = null)
 * @method OrderAudit[]    findAll()
 * @method OrderAudit[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class OrderAuditRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry,
                                private readonly  EntityManagerInterface $entityManager)
    {
        parent::__construct($registry, OrderAudit::class);
    }

    public function getByLikeDiff(?int $objectId, string $diffs = null): array
    {

        $conn = $this->entityManager->getConnection();
        $sql = "SELECT * FROM orders_audit WHERE object_id = :objectId AND diffs::TEXT LIKE :diffs ORDER BY id DESC";
        $stmt = $conn->prepare($sql);

        $stmt->bindValue('diffs', '%' . $diffs . '%');
        $stmt->bindValue('objectId',$objectId);

        $data = $stmt->executeQuery()->fetchAllAssociative();

        return $data;
    }
}