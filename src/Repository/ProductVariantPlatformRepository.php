<?php

namespace App\Repository;

use Wb3\SharedBundle\Entity\Platform;
use Wb3\SharedBundle\Entity\ProductVariantPlatform;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<ProductVariantPlatform>
 *
 * @method ProductVariantPlatform|null find($id, $lockMode = null, $lockVersion = null)
 * @method ProductVariantPlatform|null findOneBy(array $criteria, array $orderBy = null)
 * @method ProductVariantPlatform[]    findAll()
 * @method ProductVariantPlatform[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class ProductVariantPlatformRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, ProductVariantPlatform::class);
    }

    //    /**
    //     * @return Platform[] Returns an array of Platform objects
    //     */
    //    public function findByExampleField($value): array
    //    {
    //        return $this->createQueryBuilder('p')
    //            ->andWhere('p.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->orderBy('p.id', 'ASC')
    //            ->setMaxResults(10)
    //            ->getQuery()
    //            ->getResult()
    //        ;
    //    }

    //    public function findOneBySomeField($value): ?Platform
    //    {
    //        return $this->createQueryBuilder('p')
    //            ->andWhere('p.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->getQuery()
    //            ->getOneOrNullResult()
    //        ;
    //    }
    public function getByVendorStockCodes(Platform $platform, array $vendorStockCodes)
    {
        $qb = $this->createQueryBuilder('pvp');
        $qb->andWhere('pvp.platform = :platform')->setParameter('platform', $platform);
        $qb->andWhere('pvp.vendorStockCode IN (:vendorStockCodes)')->setParameter('vendorStockCodes', $vendorStockCodes);
        return $qb->getQuery()->getResult();
    }

    public function getByBarcodes(Platform $platform, array $barcodes,$states = [])
    {
        $qb = $this->createQueryBuilder('pvp');
        $qb->andWhere('pvp.platform = :platform')->setParameter('platform', $platform);
        $qb->andWhere('pvp.barcode IN (:barcodes)')->setParameter('barcodes', $barcodes);

        if(count($states)>0)
            $qb->andWhere('pvp.state IN(:states)')->setParameter('states', $states);

        $qb->andWhere('pvp.state IN(:states)')->setParameter('states', $states);
        return $qb->getQuery()->getResult();
    }
}
