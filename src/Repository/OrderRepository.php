<?php

namespace App\Repository;

use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\DBAL\ParameterType;
use Doctrine\ORM\QueryBuilder;
use Doctrine\Persistence\ManagerRegistry;
use PgSql\Connection;
use Wb3\SharedBundle\Entity\Order;
use Wb3\SharedBundle\Helper\PaginatorUtil;

/**
 *
 * @method Order|null find($id, $lockMode = null, $lockVersion = null)
 * @method Order|null findOneBy(array $criteria, array $orderBy = null)
 * @method Order[]    findAll()
 * @method Order[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class OrderRepository extends ServiceEntityRepository
{

    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Order::class);
    }
    public function allWithPaginate(int $page, mixed $limit, array $filters): array
    {
        $qb = $this->createQueryBuilder("o")
            ->select('o');

        $qb->innerJoin('o.customer', 'oc')
            ->leftJoin('o.platform','op')
            ->leftJoin('o.items', 'oi')
            ->leftJoin('oi.productVariant', 'pv')
            ->leftJoin('oi.location','oil')
            ->leftJoin('pv.product', 'p')
            ->leftJoin('p.brand','pb');
        $qb = $this->setFilters($filters, $qb);
        $this->setOrdering($filters['orderBy'] ?? null, $qb);

            $qb->setMaxResults($limit)
            ->setFirstResult(($page - 1) * $limit);

        // PaginatorUtil'den veriyi alıyoruz
        $paginationData = PaginatorUtil::paginate($qb->getQuery(), $page, $limit);

        return $paginationData;
    }

    public function personalSaleReport(): array
    {
        $connection = $this->getEntityManager()->getConnection();
        $sql = "
        SELECT 
            o.order_number, 
            CONCAT(u.first_name, ' ', u.last_name) AS name,
            o.created_at AS order_created_at,
            oi.created_at AS invoice_created_at
        FROM orders_audit oa
        JOIN orders o ON o.id = oa.object_id::int
        JOIN order_invoice oi ON oi.order_id = o.id
        LEFT JOIN users u ON u.email = oa.blame_user
        WHERE oa.diffs::text LIKE '%\"new\":\"invoiced\"%' ";
        $stmt = $connection->prepare($sql);
        $result = $stmt->executeQuery()->fetchAllAssociative();
        return $result;

    }


    private function setFilters(array $filters, QueryBuilder $qb): QueryBuilder
    {
        // Filter by 'q' for customer name, stock code, order number, or product name
        if (!empty($filters['q'])) {
            $qb->andWhere('oc.name LIKE :q OR p.skuColor LIKE :q OR o.orderNumber LIKE :q OR pv.barcode LIKE :q')
                ->setParameter('q', '%' . $filters['q'] . '%');
        }

        // Filter by 'locationId'
        if (!empty($filters['locationCode'])) {
            // Join with OrderItem to access location
                $qb->andWhere('oil.code = :locationCode')
                ->setParameter('locationCode', $filters['locationCode']);
        }

        // Filter by 'platformId'
        if (!empty($filters['platformCode'])) {
            $qb->andWhere('op.code = :platformCode')
                ->setParameter('platformCode', $filters['platformCode']);
        }
        //
        // Filter by 'brandId'
        if (!empty($filters['brandName'])) {
                $qb->andWhere('pb.name = :brandName')
                ->setParameter('brandName', $filters['brandName']);
        }

        // Filter by 'barcode'
        if (!empty($filters['state'])) {
            $qb->andWhere('o.state IN(:state)');
            $qb->setParameter('state', $filters['state']);
        }
        //dd($qb->getQuery()->getSQL());

        return $qb;
    }

    //private function setOrdering(?string $orderBy, QueryBuilder $qb): void
    //{
    //    switch ($orderBy) {
    //        case 'dueDate':
    //            $qb->orderBy('o.lastShippingAt', 'ASC');
    //            break;
    //        case 'stockCode':
    //            $qb->orderBy('o.stockCode', 'ASC');
    //            break;
    //        case 'platform':
    //            $qb->orderBy('o.platform', 'ASC');
    //            break;
    //        case 'oldOrders':
    //            $qb->orderBy('o.orderAt', 'ASC');
    //            break;
    //        case 'newOrders':
    //            $qb->orderBy('o.orderAt', 'DESC');
    //            break;
    //        default:
    //            $qb->orderBy('o.id', 'DESC'); // Default ordering
    //    }
    //}

    //    /**
    //     * @return Order[] Returns an array of Order objects
    //     */
    //    public function findByExampleField($value): array
    //    {
    //        return $this->createQueryBuilder('o')
    //            ->andWhere('o.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->orderBy('o.id', 'ASC')
    //            ->setMaxResults(10)
    //            ->getQuery()
    //            ->getResult()
    //        ;
    //    }

    //    public function findOneBySomeField($value): ?Order
    //    {
    //        return $this->createQueryBuilder('o')
    //            ->andWhere('o.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->getQuery()
    //            ->getOneOrNullResult()
    //        ;
    //    }
    public function allGroupByState(array $states)
    {

        $qb = $this->createQueryBuilder("o")
            ->select('o.state,COUNT(o.id) as count')
            ->where('o.state IN (:states)')
            ->setParameter('states',$states)
            ->groupBy('o.state')
            ->orderBy('COUNT(o.id)', 'DESC');

        //$qb = $this->setFilters($filters, $qb);

        return $qb->getQuery()->getResult();
    }

    public function getByIds(array $orderIds,array $locationCodes = [],array $orderItemStates = [])
    {
        $qb = $this->createQueryBuilder("o")
            ->addSelect('o,oc,oi,ot,oil,op,ocargo,pv,product,brand,images')
            ->leftJoin('o.customer','oc')
            ->leftJoin('o.track','ot')
            ->leftJoin('o.cargo','ocargo')
            ->leftJoin('o.platform','op')
            ->leftJoin('o.items','oi')
            ->leftJoin('oi.location','oil')
            ->leftJoin('oi.productVariant','pv')
            ->leftJoin('pv.product','product')
            ->leftJoin('product.brand','brand')
            ->leftJoin('product.images','images')
            ->where('o.id IN (:ids)')->setParameter('ids',$orderIds)
            ->andWhere('o.deletedAt is null');
        if(count($locationCodes)) {
            $qb->andWhere('oil.code IN (:locationCodes)')->setParameter('locationCodes',$locationCodes);
        }

        if(count($orderItemStates)) {
            $qb->andWhere('oi.state IN (:orderItemStates)')->setParameter('orderItemStates',$orderItemStates);
        }

        return $qb->getQuery()->getResult();
    }

    public function getByBarcode(string $barcode) : Order | null
    {
        $qb = $this->createQueryBuilder("o")
            ->addSelect('o,ot,oc')
            ->innerJoin('o.track','ot')
            ->innerJoin('o.customer','oc')
            ->where('ot.trackNumber = :barcode')->setParameter('barcode',$barcode)
            ->andWhere('o.state = :state')->setParameter('state', Order::STATE_IN_CARGO)
            ->setMaxResults(1);

        return $qb->getQuery()->getOneOrNullResult();
    }
}
