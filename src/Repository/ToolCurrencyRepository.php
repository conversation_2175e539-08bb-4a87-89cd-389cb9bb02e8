<?php

namespace App\Repository;

use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use Wb3\SharedBundle\Entity\ToolCurrency;


class ToolCurrencyRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, ToolCurrency::class);
    }

    public function allWithPaginate(int $page, mixed $limit, array $filters): array
    {
        return $this->createQueryBuilder('c')
            ->andWhere('c.currencyCode IN (:codes)')
            ->setParameter('codes', ['USD', 'EUR', 'GBP'])
            ->getQuery()
            ->getResult();
    }


}
