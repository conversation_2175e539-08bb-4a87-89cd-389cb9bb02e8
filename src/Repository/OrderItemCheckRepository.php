<?php
namespace App\Repository;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\QueryBuilder;
use Doctrine\Persistence\ManagerRegistry;
use Wb3\SharedBundle\Entity\OrderItemCheck;
use Wb3\SharedBundle\Helper\PaginatorUtil;


/**
     * @extends ServiceEntityRepository<\Wb3\SharedBundle\Entity\OrderItemCheck>
     */
class OrderItemCheckRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, OrderItemCheck::class);
    }

    public function allWithPaginate(int $page, mixed $limit, array $filters): array
    {
        $qb = $this->createQueryBuilder("t")
            ->select('t','oi')
            ->leftJoin('t.item','oi')
            ->leftJoin('oi.order','o');

        $qb = $this->setFilters($filters, $qb);

        $qb->setMaxResults($limit)
            ->setFirstResult(($page - 1) * $limit);

        // PaginatorUtil'den veriyi alıyoruz
        $paginationData = PaginatorUtil::paginate($qb->getQuery(), $page, $limit);

        return $paginationData;
    }

    private function setFilters(array $filters, QueryBuilder $qb)
    {
        if (isset($filters['isApproved'])) {
            $qb->andWhere('t.isApproved = :isApproved')
            ->setParameter('isApproved', $filters['isApproved']);
        }

        if (!empty($filters['platformId'])) {
            $qb->andWhere('o.platform = :platformId')
                ->setParameter('platformId', $filters['platformId']);;
        }

        if (!empty($filters['q'])) {
            $qb->andWhere('o.orderNumber LIKE :q')
                ->setParameter('q', '%' . trim($filters['q']) . '%');
        }

        return $qb;
    }
}