<?php

namespace App\Repository;

use Doctrine\ORM\EntityManagerInterface;
use Doctrine\Persistence\ManagerRegistry;
use Wb3\SharedBundle\Entity\OrderItemAudit;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;

/**
 * @extends ServiceEntityRepository<OrderItemAudit>
 *
 *
 * @method OrderItemAudit|null find($id, $lockMode = null, $lockVersion = null)
 * @method OrderItemAudit|null findOneBy(array $criteria, array $orderBy = null)
 * @method OrderItemAudit[]    findAll()
 * @method OrderItemAudit[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class OrderItemAuditRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry,
                                private readonly  EntityManagerInterface $entityManager)
    {
        parent::__construct($registry, OrderItemAudit::class);
    }

    public function getByLikeDiff(?int $objectId, string $diffs = null): array
    {

        $conn = $this->entityManager->getConnection();
        $sql = "SELECT * FROM order_item_audit WHERE object_id = :objectId AND diffs::TEXT LIKE :diffs ORDER BY id DESC";
        $stmt = $conn->prepare($sql);

        $stmt->bindValue('diffs', '%' . $diffs . '%');
        $stmt->bindValue('objectId',$objectId);

        $data = $stmt->executeQuery()->fetchAllAssociative();

        return $data;
    }
}