<?php

namespace App\Repository;

use Doctrine\ORM\QueryBuilder;
use Doctrine\Persistence\ManagerRegistry;
use Wb3\SharedBundle\Entity\OrderItem;
use Wb3\SharedBundle\Helper\PaginatorUtil;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;


/**
 * @extends ServiceEntityRepository<OrderItem>
 */
class OrderItemRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, OrderItem::class);
    }

    public function allWithPaginate(int $page, mixed $limit, array $filters): array
    {
        $qb = $this->createQueryBuilder("oi")
            ->select('oi')
            ->join('oi.order','o')
            ->join('o.customer', 'oc')
            ->join('o.platform','op')
            ->join('oi.productVariant', 'pv')
            ->leftJoin('oi.location','l')
            ->leftJoin('pv.product', 'p')
            ->leftJoin('p.brand','pb');
        $qb = $this->setFilters($filters, $qb);
        $this->setOrdering($filters['orderBy'] ?? null, $qb);

        $qb->setMaxResults($limit)
            ->setFirstResult(($page - 1) * $limit);

        // PaginatorUtil'den veriyi alıyoruz
        $paginationData = PaginatorUtil::paginate($qb->getQuery(), $page, $limit);

        return $paginationData;
    }
    public function paginateWithLocation(int $page, mixed $limit,int $locationId, array $filters): array
    {
        $qb = $this->createQueryBuilder("oi")
            ->select('oi');

        // Varsayılan location ID kontrolü
        $qb->andWhere('oi.location = :locationId')
            ->setParameter('locationId', $locationId);

        // Apply filters
        $qb = $this->setFilters($filters, $qb);

        // Set ordering if needed
    //    $this->setOrdering($filters['orderBy'] ?? null, $qb);

        // Set pagination limits
        $qb->setMaxResults($limit)
            ->setFirstResult(($page - 1) * $limit);

        // Use PaginatorUtil to get paginated results
        $paginationData = PaginatorUtil::paginate($qb->getQuery(), $page, $limit);

        return $paginationData;
    }


    private function setFilters(array $filters, QueryBuilder $qb): QueryBuilder
    {
       //  Filter by 'q' for customer name, stock code, order number, or product name
        if (!empty($filters['q'])) {
            $qb->andWhere('oc.name LIKE :q OR p.skuColor LIKE :q OR o.orderNumber LIKE :q OR pv.barcode LIKE :q')
                ->setParameter('q', '%' . $filters['q'] . '%');
        }

        // Filter by 'locationId'
        if (!empty($filters['locationCode'])) {
            // Join with OrderItem to access location
            $qb->andWhere('oil.code = :locationCode')
                ->setParameter('locationCode', $filters['locationCode']);
        }

        // Filter by 'platformId'
        if (!empty($filters['platformCode'])) {
            $qb->andWhere('op.code = :platformCode')
                ->setParameter('platformCode', $filters['platformCode']);
        }
        //
        // Filter by 'brandId'
        if (!empty($filters['brandName'])) {
            $qb->andWhere('pb.name = :brandName')
                ->setParameter('brandName', $filters['brandName']);
        }

        // Filter by 'barcode'
        if (!empty($filters['state'])) {
            $qb->andWhere('o.state IN(:state)');
            $qb->setParameter('state', $filters['state']);
        }
        //dd($qb->getQuery()->getSQL());

        return $qb;
    }
    //private function setOrdering(?string $orderBy, QueryBuilder $qb): void
    //{
    //    switch ($orderBy) {
    //        case 'dueDate':
    //            $qb->orderBy('o.lastShippingAt', 'ASC');
    //            break;
    //        case 'stockCode':
    //            $qb->orderBy('oi.stockCode', 'ASC');
    //            break;
    //        case 'platform':
    //            $qb->innerJoin('oi.order', 'o')
    //                ->orderBy('o.platform', 'ASC');
    //            break;
    //        case 'oldOrders':
    //            $qb->innerJoin('oi.order', 'o')
    //                ->orderBy('o.orderedAt', 'ASC');
    //            break;
    //        case 'newOrders':
    //            $qb->innerJoin('oi.order', 'o')
    //                ->orderBy('o.orderedAt', 'DESC');
    //            break;
    //        default:
    //            $qb->orderBy('oi.id', 'desc'); // Default ordering
    //    }
    //}


    public function findAllOrderItemsWithOrders()
    {
        return $this->createQueryBuilder('oi')
            ->innerJoin('oi.order', 'o') // Order ile join
            ->addSelect('o') // Order'ı da seç
                ->leftJoin('oi.productVariant','pv')
            ->addSelect('pv')
            ->getQuery()
            ->getResult();
    }

    //    /**
    //     * @return OrderItem[] Returns an array of OrderItem objects
    //     */
    //    public function findByExampleField($value): array
    //    {
    //        return $this->createQueryBuilder('o')
    //            ->andWhere('o.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->orderBy('o.id', 'ASC')
    //            ->setMaxResults(10)
    //            ->getQuery()
    //            ->getResult()
    //        ;
    //    }

    //    public function findOneBySomeField($value): ?OrderItem
    //    {
    //        return $this->createQueryBuilder('o')
    //            ->andWhere('o.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->getQuery()
    //            ->getOneOrNullResult()
    //        ;
    //    }
}
