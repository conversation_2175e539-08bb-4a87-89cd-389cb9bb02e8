<?php

namespace App\Command;
use App\Repository\OrderRepository;
use App\Scheduler\Platform\IdefixAliSolmaz\SyncProductScheduler;
use App\Service\DispatchService;
use App\Service\Order\OrderItemBufferService;
use App\Service\Order\OrderService;
use App\Service\Platform\AliSolmaz\AliSolmazPlatformService;
use App\Service\Platform\DistributeService;
use App\Service\Platform\Idefix\IdefixPlatformService;
use App\Service\Platform\IdefixAliSolmaz\IdefixAliSolmazPlatformService;
use App\Service\Platform\Trendyol\TrendyolPlatformService;
use App\Service\Platform\TrendyolAliSolmaz\TrendyolAliSolmazPlatformService;
use App\Service\PlatformMatchCategoryService;
use App\Service\PlatformService;
use App\Service\QueService;
use App\Service\Search\SearchService;
use App\Service\TaskService;
use Doctrine\Common\Collections\Order;
use Doctrine\ORM\EntityManagerInterface;
use Enqueue\Client\ProducerInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Wb3\SharedBundle\Entity\Task;

class TestCommand extends Command
{
    protected static $defaultName = 'app:test';
    private DistributeService $platformDistributeService;
    private IdefixPlatformService $idefixMarketplaceService;
    private EntityManagerInterface $em;
    private PlatformService $platformService;
    private AliSolmazPlatformService $aliSolmazMarketplaceService;
    private PlatformMatchCategoryService $platformMathCategoryService;

    public function __construct(
        private readonly QueService $queService,
        private readonly DispatchService $dispatchService,
        private readonly OrderService $orderService,
        private readonly OrderItemBufferService $orderItemBufferService,
        string                                                                  $name = null)
    {
        parent::__construct($name);
    }
    /**
     * @throws \Exception
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {

        ////Kapora
        //$order = $this->orderService->repository->find(31);
        //$this->orderService->purchase($order);


        //Dispatch
        $order = $this->orderService->repository->find(12);
        $this->dispatchService->sendDispatch($order);

        //buffer
        //$this->orderItemBufferService->setCompletedAtByItem(1);
    }

}
