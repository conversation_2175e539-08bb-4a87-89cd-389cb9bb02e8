<?php

namespace Wb3\SharedBundle\Entity;

use App\Repository\ProductVariantRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Doctrine\ORM\Mapping\Cache;
use Doctrine\ORM\Mapping\UniqueConstraint;
use Wb3\SharedBundle\Entity\Trait\TimestampTrait;

#[UniqueConstraint(name: 'product_variant_stock_code_unique', columns: ['stock_code'])]
#[ORM\Index(name: 'product_variant_product_id_idx', columns: ['product_id'])]
#[ORM\Entity(repositoryClass: ProductVariantRepository::class)]
class ProductVariant
{

    use TimestampTrait;
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(length: 50)]
    private string $stockCode;

    #[ORM\Column(length: 50)]
    private string $state = 'draft';

    #[ORM\Column(length: 50, nullable: true)]
    private ?string $transition = null;

    #[ORM\ManyToOne(targetEntity: Product::class, cascade: ['persist'], inversedBy: 'variants')]
    #[ORM\JoinColumn(nullable: false)]
    private Product $product;

    #[ORM\Column(length: 50)]
    private string $barcode;

    #[ORM\Column(length: 64, nullable: true)]
    private ?string $hash;

    #[ORM\OneToMany(targetEntity: ProductVariantLocation::class, mappedBy: 'variant',cascade: ['persist','remove'])]
    #[Cache(usage: 'NONSTRICT_READ_WRITE', region: 'write_rare')]
    protected Collection $locations;

    #[ORM\OneToMany(targetEntity: PlatformProductVariant::class, mappedBy: 'variant',cascade: ['persist','remove'])]
    protected Collection $platformProductVariants;

    /**
     * @var Collection<int, PlatformBuffer>
     */
    #[ORM\OneToMany(targetEntity: PlatformBuffer::class, mappedBy: 'ProductVariant')]
    private Collection $platformBuffers;

    /**
     * @var Collection<int, PlatformMatchVariant>
     */
    #[ORM\OneToMany(targetEntity: PlatformMatchVariant::class, mappedBy: 'productVariant')]
    private Collection $platformMatchProductVariants;
    #[ORM\Column(type: 'decimal', precision: 10,scale: 2 ,nullable: true)]
    private ?float $desi;

    #[ORM\Column(type: 'integer', options: ['default' => 0])]
    private ?int $quantity = null;

    #[ORM\Column(nullable: true)]
    private ?int $sizeCode = null;
    public function __construct()
    {
        $this->platformBuffers = new ArrayCollection();
        $this->platformMatchProductVariants = new ArrayCollection();
        $this->locations = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(?int $id): void
    {
        $this->id = $id;
    }

    public function getStockCode(): ?string
    {
        return $this->stockCode;
    }

    public function setStockCode(string $stockCode): static
    {
        $this->stockCode = $stockCode;

        return $this;
    }

    public function getState(): ?string
    {
        return $this->state;
    }

    public function setState(string $state): static
    {
        $this->state = $state;

        return $this;
    }

    public function getTransition(): ?string
    {
        return $this->transition;
    }

    public function setTransition(?string $transition): static
    {
        $this->transition = $transition;

        return $this;
    }

    public function getProduct(): ?Product
    {
        return $this->product;
    }

    public function setProduct(?Product $product): static
    {
        $this->product = $product;

        return $this;
    }

    public function getBarcode(): ?string
    {
        return $this->barcode;
    }

    public function setBarcode(string $barcode): static
    {
        $this->barcode = $barcode;

        return $this;
    }

    public function getHash(): ?string
    {
        return $this->hash;
    }

    public function setHash(string $hash) : self
    {
        $this->hash =  $hash;

        return $this;
    }

    /**
     * @return Collection<int, ProductVariantLocation>
     */
    public function getLocations(): Collection
    {
        return $this->locations;
    }

    public function addLocation(ProductVariantLocation $location): static
    {
        if (!$this->locations->contains($location)) {
            $this->locations->add($location);
            $location->setVariant($this);
        }

        return $this;
    }

    public function removeLocation(ProductVariantLocation $location): static
    {
        if ($this->locations->removeElement($location)) {
            // set the owning side to null (unless already changed)
            if ($location->getLocation() === $this) {
                $location->setVariant(null);
            }
        }

        return $this;
    }

    public function addPlatform(PlatformProductVariant $variantPlatform): static
    {
        if (!$this->locations->contains($variantPlatform)) {
            $this->locations->add($variantPlatform);
            $variantPlatform->setVariant($this);
        }

        return $this;
    }

    public function removePlatform(PlatformProductVariant $variantPlatform): static
    {
        if ($this->locations->removeElement($variantPlatform)) {
            // set the owning side to null (unless already changed)
            if ($variantPlatform->getVariant() === $this) {
                $variantPlatform->setVariant(null);
            }
        }

        return $this;
    }

    public function getPlatformProductVariants(): Collection
    {
        return $this->platformProductVariants;
    }

    public function setPlatformProductVariants(Collection $platformProductVariants): ProductVariant
    {
        $this->platformProductVariants = $platformProductVariants;
        return $this;
    }

    /**
     * @return Collection<int, PlatformBuffer>
     */
    public function getPlatformBuffers(): Collection
    {
        return $this->platformBuffers;
    }

    public function addPlatformBuffer(PlatformBuffer $platformBuffer): static
    {
        if (!$this->platformBuffers->contains($platformBuffer)) {
            $this->platformBuffers->add($platformBuffer);
            $platformBuffer->setProductVariant($this);
        }

        return $this;
    }

    public function removePlatformBuffer(PlatformBuffer $platformBuffer): static
    {
        if ($this->platformBuffers->removeElement($platformBuffer)) {
            // set the owning side to null (unless already changed)
            if ($platformBuffer->getProductVariant() === $this) {
                $platformBuffer->setProductVariant(null);
            }
        }

        return $this;
    }
    public function __toString(): string
    {
       return $this->getStockCode();
    }

    /**
     * @return Collection<int, PlatformMatchVariant>
     */
    public function getPlatformMatchProductVariants(): Collection
    {
        return $this->platformMatchProductVariants;
    }

    public function addPlatformMatchProductVariant(PlatformMatchVariant $platformMatchProductVariant): static
    {
        if (!$this->platformMatchProductVariants->contains($platformMatchProductVariant)) {
            $this->platformMatchProductVariants->add($platformMatchProductVariant);
            $platformMatchProductVariant->setVariant($this);
        }

        return $this;
    }

    public function removePlatformMatchProductVariant(PlatformMatchVariant $platformMatchProductVariant): static
    {
        if ($this->platformMatchProductVariants->removeElement($platformMatchProductVariant)) {
            // set the owning side to null (unless already changed)
            if ($platformMatchProductVariant->getVariant() === $this) {
                $platformMatchProductVariant->setVariant(null);
            }
        }

        return $this;
    }

    public function getDesi(): ?float
    {
        return $this->desi;
    }

    public function setDesi(?float $desi): self
    {
        $this->desi = $desi;

        return $this;
    }

    public function getSize()
    {
        return  explode(" - ",$this->stockCode)[2];
    }

    public function getColor()
    {
        return  explode(" - ",$this->stockCode)[1];
    }

    public function calculateQuantity()
    {
        $qty=0;
        foreach($this->getLocations() AS $location) {
            $qty += $location->getQuantity();
        }
        return $qty;
    }

    public function getQuantity(): ?int
    {
        return $this->quantity;
    }

    public function setQuantity(int $quantity): static
    {
        $this->quantity = $quantity;

        return $this;
    }

    public function getSizeCode(): ?int
    {
        return $this->sizeCode;
    }

    public function setSizeCode(?int $sizeCode): static
    {
        $this->sizeCode = $sizeCode;

        return $this;
    }

}
