<?php

namespace Wb3\SharedBundle\Entity;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use App\Repository\ProductPriceTypeRepository;
use Doctrine\ORM\Mapping\Cache;
use Wb3\SharedBundle\Entity\Trait\TimestampTrait;

#[ORM\Entity(repositoryClass: ProductPriceTypeRepository::class)]
#[Cache(usage: 'NONSTRICT_READ_WRITE', region: 'write_rare')]
class ProductPriceType
{
    use TimestampTrait;
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(length: 50)]
    private ?string $title;

    #[ORM\OneToMany(targetEntity: ProductPrice::class, mappedBy: 'ProductPriceType')]
    private Collection $productPrices;

    #[ORM\Column(length: 50)]
    private ?string $korgunCode = null;

    #[ORM\Column(nullable: true)]
    private ?int $sortBy = null;

    #[ORM\Column(length: 10)]
    private ?string $type = null;

    /**
     * @var Collection<int, PlatformPriceRule>
     */
    #[ORM\OneToMany(targetEntity: PlatformPriceRule::class, mappedBy: 'type')]
    private Collection $platformPriceRules;

    public function __construct()
    {
        $this->productPrices = new ArrayCollection();
        $this->platformPriceRules = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getTitle(): ?string
    {
        return $this->title;
    }

    public function setTitle(string $title): static
    {
        $this->title = $title;

        return $this;
    }

    /**
     * @return Collection<int, ProductPrice>
     */
    public function getProductPrices(): Collection
    {
        return $this->productPrices;
    }

    public function addProductPrice(ProductPrice $productPrice): static
    {
        if (!$this->productPrices->contains($productPrice)) {
            $this->productPrices->add($productPrice);
            $productPrice->setType($this);
        }

        return $this;
    }

    public function removeProductPrice(ProductPrice $productPrice): static
    {
        if ($this->productPrices->removeElement($productPrice)) {
            // set the owning side to null (unless already changed)
            if ($productPrice->getType() === $this) {
                $productPrice->setType(null);
            }
        }

        return $this;
    }

    public function getKorgunCode(): ?string
    {
        return $this->korgunCode;
    }

    public function setKorgunCode(string $korgunCode): static
    {
        $this->korgunCode = $korgunCode;

        return $this;
    }

    public function getSortBy(): ?int
    {
        return $this->sortBy;
    }

    public function setSortBy(?int $sortBy): static
    {
        $this->sortBy = $sortBy;

        return $this;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(string $type): static
    {
        $this->type = $type;

        return $this;
    }

    /**
     * @return Collection<int, PlatformPriceRule>
     */
    public function getPlatformPriceRules(): Collection
    {
        return $this->platformPriceRules;
    }

    public function addPlatformPriceRule(PlatformPriceRule $platformPriceRule): static
    {
        if (!$this->platformPriceRules->contains($platformPriceRule)) {
            $this->platformPriceRules->add($platformPriceRule);
            $platformPriceRule->setType($this);
        }

        return $this;
    }

    public function removePlatformPriceRule(PlatformPriceRule $platformPriceRule): static
    {
        if ($this->platformPriceRules->removeElement($platformPriceRule)) {
            // set the owning side to null (unless already changed)
            if ($platformPriceRule->getType() === $this) {
                $platformPriceRule->setType(null);
            }
        }

        return $this;
    }
}
