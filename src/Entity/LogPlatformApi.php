<?php

namespace Wb3\SharedBundle\Entity;

use Doctrine\ORM\Mapping as ORM;
use Wb3\SharedBundle\Entity\Trait\TimestampTrait;
use App\Repository\LogPlatformApiRepository;
#[ORM\HasLifecycleCallbacks]
#[ORM\Entity(repositoryClass: LogPlatformApiRepository::class)]
//#[ORM\Cache(usage: 'READ_ONLY',region: 'my_entity_region')]
class LogPlatformApi
{
    use TimestampTrait;
    CONST MAX_RESPONSE_LENGTH = 100000;

    CONST SEARCH_EXCLUDE_FIELDS = 'request,response';
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(length: 10)]
    private ?string $channel = null;

    #[ORM\Column(length: 50)]
    private ?string $level = null;

    #[ORM\Column(length: 255)]
    private ?string $message = null;

    #[ORM\Column]
    private ?int $responseCode = null;

    #[ORM\Column(length: 255)]
    private ?string $url = null;

    #[ORM\Column]
    private array $request = [];

    #[ORM\Column]
    private array $response = [];

    #[ORM\Column(nullable: true)]
    private ?int $executeTime = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getChannel(): ?string
    {
        return $this->channel;
    }

    public function setChannel(string $channel): static
    {
        $this->channel = $channel;

        return $this;
    }

    public function getLevel(): ?string
    {
        return $this->level;
    }

    public function setLevel(string $level): static
    {
        $this->level = $level;

        return $this;
    }

    public function getMessage(): ?string
    {
        return $this->message;
    }

    public function setMessage(string $message): static
    {
        $this->message = $message;

        return $this;
    }

    public function getResponseCode(): ?int
    {
        return $this->responseCode;
    }

    public function setResponseCode(?int $responseCode): static
    {
        $this->responseCode = $responseCode;

        return $this;
    }

    public function getUrl(): ?string
    {
        return $this->url;
    }

    public function setUrl(string $url): static
    {
        $this->url = $url;

        return $this;
    }

    public function getRequest(): array
    {
        return $this->request;
    }

    public function setRequest(array $request): static
    {
        $this->request = $request;

        return $this;
    }

    public function getResponse(): array
    {
        return $this->response;
    }

    public function setResponse(array $response): static
    {
        $this->response = $response;

        if(strlen(json_encode($this->response)) > self::MAX_RESPONSE_LENGTH) {
            $this->response = [];
        }

        return $this;
    }

    public function getExecuteTime(): ?int
    {
        return $this->executeTime;
    }

    public function setExecuteTime(?int $executeTime): static
    {
        $this->executeTime = $executeTime;

        return $this;
    }
}
