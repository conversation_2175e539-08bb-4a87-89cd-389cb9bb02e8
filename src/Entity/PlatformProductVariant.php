<?php

namespace Wb3\SharedBundle\Entity;

use Doctrine\ORM\Mapping as ORM;
use Wb3\SharedBundle\Entity\Trait\TimestampTrait;
use App\Repository\PlatformProductVariantRepository;

#[ORM\Entity(repositoryClass: PlatformProductVariantRepository::class)]
#[ORM\HasLifecycleCallbacks]
#[ORM\UniqueConstraint(name: 'platform_id_barcode_unique', columns: ['platform_id', 'barcode'])]
#[ORM\UniqueConstraint(name: 'platform_id_stock_code_unique', columns: ['platform_id', 'stock_code'])]
#[ORM\UniqueConstraint(name: 'platform_id_hash_unique', columns: ['platform_id', 'hash'])]
class PlatformProductVariant
{
    use TimestampTrait;

    const STATE_DRAFT = 'draft';
    const STATE_CREATED = 'created';
    const STATE_APPROVED = 'approved';
    const STATE_UPLOADED = 'uploaded';
    const STATE_PLATFORM_WILL_REVIEW = 'platform_will_review';
    const STATE_WAITING_VENDOR_APPROVE = 'waiting_vendor_approve';
    const STATE_WAITING_MISSING_INFO = 'waiting_missing_info';
    const STATE_VENDOR_DECLINED = 'vendor_declined';
    const STATE_PLATFORM_DECLINED = 'platform_declined';
    const STATE_PUBLISHED = 'published';
    const STATE_CLOSED = 'closed';
    const STATE_OUT_OF_STOCK = 'out_of_stock';

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\ManyToOne(targetEntity: ProductVariant::class)]
    private ProductVariant $variant;

    #[ORM\ManyToOne(targetEntity: Platform::class)]
    private Platform $platform;
    #[ORM\Column(type:'string',length: 50, options: ['default' => 'N/A'])]
    private string $stockCode = 'N/A';
    #[ORM\Column(type:'string',length: 255)]
    private string $barcode;
    #[ORM\Column(type: 'integer',options: ['default' => 0])]
    private int $platformQuantity = 0;
    #[ORM\Column(length: 50)]
    private string $state = 'draft';
    #[ORM\Column(length: 64)]
    private string $hash;

    #[ORM\Column(type: 'float', options: ['default' => 0.0])]
    private float $platformPrice = 0.0;

    #[ORM\Column(nullable: true)]
    private ?array $extra = null;

    #[ORM\ManyToOne(inversedBy: 'productVariantPlatforms')]
    private ?PlatformCategory $platformCategory = null;

    #[ORM\ManyToOne(inversedBy: 'productVariantPlatforms')]
    private ?PlatformBrand $platformBrand = null;

    #[ORM\Column(nullable: true)]
    private ?array $attributes = null;

    #[ORM\Column(nullable: true)]
    private ?float $listPrice = null;

    #[ORM\Column(nullable: true)]
    private ?int $vendorQuantity = null;

    #[ORM\Column(nullable: true)]
    private ?float $vendorPrice = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getVariant() : ProductVariant
    {
        return $this->variant;
    }

    public function setVariant($variant) : self
    {
        $this->variant = $variant;
        return $this;
    }
    public function getPlatform() : Platform
    {
        return $this->platform;
    }

    public function setPlatform($platform) : self
    {
        $this->platform = $platform;
        return $this;
    }

    public function getBarcode(): string
    {
        return $this->barcode;
    }

    public function setBarcode(string $barcode): self
    {
        $this->barcode = $barcode;
        return $this;
    }

    public function getPlatformQuantity(): int
    {
        return $this->platformQuantity;
    }

    public function setPlatformQuantity(int $platformQuantity): self
    {
        $this->platformQuantity = $platformQuantity;
        return $this;
    }

    public function getState(): string
    {
        return $this->state;
    }

    public function setState(string $state): self
    {
        $this->state = $state;
        return $this;
    }

    public function getHash(): string
    {
        return $this->hash;
    }

    public function getPlatformPrice(): float
    {
        return $this->platformPrice;
    }

    public function setPlatformPrice(float $platformPrice): PlatformProductVariant
    {
        $this->platformPrice = $platformPrice;
        return $this;
    }

    public function setHash(string $hash) : self
    {
        $this->hash = $hash;
        return $this;
    }

    public function getStockCode(): string
    {
        return $this->stockCode;
    }

    public function setStockCode(string $stockCode): PlatformProductVariant
    {
        $this->stockCode = $stockCode;
        return $this;
    }

    public function getExtra(): ?array
    {
        return $this->extra;
    }

    public function setExtra(?array $extra): static
    {
        $this->extra = $extra;

        return $this;
    }

    public function getPlatformCategory(): ?PlatformCategory
    {
        return $this->platformCategory;
    }

    public function setPlatformCategory(?PlatformCategory $platformCategory): static
    {
        $this->platformCategory = $platformCategory;

        return $this;
    }

    public function getPlatformBrand(): ?PlatformBrand
    {
        return $this->platformBrand;
    }

    public function setPlatformBrand(?PlatformBrand $platformBrand): static
    {
        $this->platformBrand = $platformBrand;

        return $this;
    }

    public function getAttributes(): ?array
    {
        return $this->attributes;
    }

    public function setAttributes(?array $attributes): static
    {
        $this->attributes = $attributes;

        return $this;
    }

    public function getListPrice(): ?float
    {
        return $this->listPrice;
    }

    public function setListPrice(?float $listPrice): static
    {
        $this->listPrice = $listPrice;

        return $this;
    }

    public function getVendorQuantity(): ?int
    {
        return $this->vendorQuantity;
    }

    public function setVendorQuantity(?int $vendorQuantity): static
    {
        $this->vendorQuantity = $vendorQuantity;

        return $this;
    }

    public function getVendorPrice(): ?float
    {
        return $this->vendorPrice;
    }

    public function setVendorPrice(?float $vendorPrice): static
    {
        $this->vendorPrice = $vendorPrice;

        return $this;
    }

}
