<?php

namespace Wb3\SharedBundle\Entity;

use App\Repository\OrderRepository;
use DateInterval;
use DateTime;
use DateTimeInterface;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Wb3\SharedBundle\Entity\Trait\TimestampTrait;

#[ORM\Entity(repositoryClass: OrderRepository::class)]
#[ORM\UniqueConstraint(name: 'platform_order_number_unique', columns: ['platform_id','order_number'])]
#[ORM\Table(name: 'orders')]
#[ORM\HasLifecycleCallbacks]
class Order
{

    use TimestampTrait;
    const STATE_WAITING_PAYMENT = 'waiting_payment';
    const STATE_NEW = 'new';
    const  STATE_VENDOR_CANCELED = 'vendor_canceled';
    const STATE_CUSTOMER_CANCEL_REQUESTED = 'customer_cancel_requested';
    const STATE_CUSTOMER_CANCELED = 'customer_canceled';
    const STATE_INVOICED = 'invoiced';
    const STATE_IN_CARGO = 'in_cargo';
    const STATE_DELIVERED = 'delivered';
    const STATE_UNDELIVERED = 'undelivered';

    const TRANSITION_TO_INVOICED = 'to_invoiced';
    const TRANSITION_TO_IN_CARGO = 'to_in_cargo';
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(length: 255)]
    private ?string $orderNumber = null;
    #[ORM\OneToMany(targetEntity: OrderItem::class, mappedBy: 'order',cascade: ['persist'])]
    private Collection $items;


    #[ORM\Column(options: ['default' => false])]
    private ?bool $isGift = false;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $giftMessage = null;

    #[ORM\OneToOne(cascade: ['persist', 'remove'])]
    #[ORM\JoinColumn(nullable: false)]
    private ?OrderCustomer $customer = null;

    #[ORM\OneToOne(cascade: ['persist', 'remove'])]
    #[ORM\JoinColumn(nullable: true)]
    private ?OrderInvoice $invoice = null;


    #[ORM\ManyToOne(targetEntity: Platform::class)]
    #[ORM\JoinColumn(nullable: false)]
    private ?Platform $platform;

    #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?DateTimeInterface $expireAt = null;

    #[ORM\Column(type: Types::JSON, nullable: true)]
    private ?array $extras = null;
    #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?DateTimeInterface $orderAt = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $paymentType = null;

    #[ORM\Column(type: "string",length: 255)]
    private string $state = 'new';
    #[ORM\ManyToOne(cascade: ['persist'])]
    #[ORM\JoinColumn(nullable: false)]
    private ?OrderCargo $cargo = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    private ?DateTimeInterface $lastShippingAt = null;

    #[ORM\Column]
    private ?string $packageNumber = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $hash = null;
    #[ORM\OneToMany(targetEntity: OrderAddress::class, mappedBy: 'order',cascade: ['persist'])]
    private Collection $addresses;

    #[ORM\OneToOne(mappedBy: 'order',cascade: ['persist', 'remove'])]
    private ?OrderTrack $track = null;

    #[ORM\Column(options: ['default' => 0])]
    private ?float $totalPrice = 0;

    #[ORM\Column(options: ['default' => '0.00'])]
    private ?float $totalDiscount = null;

    #[ORM\Column(options: ['default' => '0.00'])]
    private ?float $totalVendorDiscount = null;

    #[ORM\Column(options: ['default' => '0.00'])]
    private ?float $totalPlatformDiscount = null;

    #[ORM\Column(options: ['default' => '0.00'])]
    private ?float $discountedTotalPrice = null;

    #[ORM\Column(options: ['default' => '0.00'])]
    private ?float $totalCommissionAmount = null;


    public function __construct()
    {
        $this->items = new ArrayCollection();
        $this->addresses = new ArrayCollection();
    }


    /**
     * @return Collection<int, OrderItem>
     */
    public function getItems(): Collection
    {
        return $this->items;
    }

    public function addItem(OrderItem $orderItem): static
    {
        if (!$this->items->contains($orderItem)) {
            $this->items->add($orderItem);
            $orderItem->setOrder($this);
        }

        return $this;
    }
    public function getId(): ?int
    {
        return $this->id;
    }

    public function getOrderNumber(): ?string
    {
        return $this->orderNumber;
    }

    public function setOrderNumber(string $orderNumber): static
    {
        $this->orderNumber = $orderNumber;

        return $this;
    }

    public function isGift(): ?bool
    {
        return $this->isGift;
    }

    public function setGift(bool $isGift): static
    {
        $this->isGift = $isGift;

        return $this;
    }

    public function getGiftMessage(): ?string
    {
        return $this->giftMessage;
    }

    public function setGiftMessage(?string $giftMessage): static
    {
        $this->giftMessage = $giftMessage;

        return $this;
    }

    public function getCustomer(): ?OrderCustomer
    {
        return $this->customer;
    }

    public function setCustomer(OrderCustomer $customerId): static
    {
        $this->customer = $customerId;

        return $this;
    }


    public function getPlatform(): ?Platform
    {
        return $this->platform;
    }

    public function setPlatform(?Platform $platform): static
    {
        $this->platform = $platform;

        return $this;
    }

    public function getExpireAt(): ?DateTimeInterface
    {
        return $this->expireAt;
    }

    public function setExpireAt(?DateTimeInterface $expireAt): static
    {
        $this->expireAt = $expireAt;

        return $this;
    }

    public function getExtras(): ?array
    {
        return $this->extras;
    }

    public function setExtras(?array $extras): static
    {
        $this->extras = $extras;

        return $this;
    }


    public function getOrderAt(): ?DateTimeInterface
    {
        return $this->orderAt;
    }

    public function setOrderAt(?DateTimeInterface $orderAt): static
    {
        $this->orderAt = $orderAt;

        return $this;
    }

    public function getPaymentType(): ?string
    {
        return $this->paymentType;
    }

    public function setPaymentType(?string $paymentType): static
    {
        $this->paymentType = $paymentType;

        return $this;
    }

    public function getState(): ?string
    {
        return $this->state;
    }

    public function setState(string $state): static
    {
        $this->state = $state;

        return $this;
    }
    public function getPackageNumber(): ?string
    {
        return $this->packageNumber;
    }

    public function setPackageNumber(?string $packageNumber): static
    {
        $this->packageNumber = $packageNumber;

        return $this;
    }
    public function getCargo(): ?OrderCargo
    {
        return $this->cargo;
    }

    public function setCargo(?OrderCargo $cargo): static
    {
        $this->cargo = $cargo;

        return $this;
    }
    public function getLastShippingAt(): ?DateTimeInterface
    {
        return $this->lastShippingAt;
    }

    public function setLastShippingAt(DateTimeInterface $lastShippingAt): static
    {
        $this->lastShippingAt = $lastShippingAt;

        return $this;
    }
    public function getRemainingTime(): ?DateInterval
    {
        // Şu anki zaman
        $now = new DateTime();

        // ExpireAt hesapla
        $remainAt = clone $this->getOrderAt();
        $remainAt->modify('+24 hours');


        // Eğer expireAt dolmuşsa null döndür
        if ($now >= $remainAt) {
            return null;
        }

        // Kalan süreyi hesapla
        return $now->diff($remainAt);
    }

    public function getHash(): ?string
    {
        return $this->hash;
    }

    public function setHash(?string $hash): static
    {
        $this->hash = $hash;

        return $this;
    }


    public function getIsGift(): ?bool
    {
        return $this->isGift;
    }

    public function setIsGift(?bool $isGift): Order
    {
        $this->isGift = $isGift;
        return $this;
    }

    /**
     * @return Collection<int, OrderItem>
     */
    public function getAddresses(): Collection
    {
        return $this->addresses;
    }

    public function addAddress(OrderAddress $address): static
    {
        if (!$this->addresses->contains($address)) {
            $this->addresses->add($address);
            $address->setOrder($this);
        }

        return $this;
    }

    public function getInvoice(): ?OrderInvoice
    {
        return $this->invoice;
    }

    public function setInvoice(?OrderInvoice $invoice): Order
    {
        $this->invoice = $invoice;
        return $this;
    }

    public function getTrack(): ?OrderTrack
    {
        return $this->track;
    }

    public function setTrack(?OrderTrack $track): static
    {
        $this->track = $track;

        return $this;
    }

    public function getTotalPrice(): ?float
    {
        return $this->totalPrice;
    }

    public function setTotalPrice(float $totalPrice): static
    {
        $this->totalPrice = $totalPrice;

        return $this;
    }

    public function getTotalDiscount(): ?float
    {
        return $this->totalDiscount;
    }

    public function setTotalDiscount(float $totalDiscount): static
    {
        $this->totalDiscount = $totalDiscount;

        return $this;
    }

    public function getTotalVendorDiscount(): ?float
    {
        return $this->totalVendorDiscount;
    }

    public function setTotalVendorDiscount(float $totalVendorDiscount): static
    {
        $this->totalVendorDiscount = $totalVendorDiscount;

        return $this;
    }

    public function getTotalPlatformDiscount(): ?float
    {
        return $this->totalPlatformDiscount;
    }

    public function setTotalPlatformDiscount(float $totalPlatformDiscount): static
    {
        $this->totalPlatformDiscount = $totalPlatformDiscount;

        return $this;
    }

    public function getDiscountedTotalPrice(): ?float
    {
        return $this->discountedTotalPrice;
    }

    public function setDiscountedTotalPrice(float $discountedTotalPrice): static
    {
        $this->discountedTotalPrice = $discountedTotalPrice;

        return $this;
    }

    public function getTotalCommissionAmount(): ?float
    {
        return $this->totalCommissionAmount;
    }

    public function setTotalCommissionAmount(float $totalCommissionAmount): static
    {
        $this->totalCommissionAmount = $totalCommissionAmount;

        return $this;
    }
    public function __toString(): string
    {
       return "Order Entity: ".$this->id;
    }

}
