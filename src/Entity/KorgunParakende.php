<?php

namespace Wb3\SharedBundle\Entity;

use App\Repository\KorgunParakendeRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: KorgunParakendeRepository::class)]
class KorgunParakende
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\ManyToOne(cascade: ['persist'])]
    #[ORM\JoinColumn(nullable: false)]
    private ?ProductVariant $productVariant = null;

    #[ORM\ManyToOne(cascade: ['persist'])]
    #[ORM\JoinColumn(nullable: false)]
    private ?Product $product = null;

    #[ORM\Column(length: 255)]
    private ?string $scs = null;

    #[ORM\Column(length: 255)]
    private ?string $skod = null;

    #[ORM\Column]
    private ?int $rkod = null;

    #[ORM\Column]
    private ?int $bedKod = null;

    #[ORM\Column]
    private ?int $satNo = null;

    #[ORM\Column(length: 255)]
    private ?string $satTip = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    private ?\DateTimeInterface $satTar = null;

    #[ORM\Column(length: 255)]
    private ?string $location = null;

    #[ORM\Column(type: Types::DECIMAL, precision: 10, scale: 2)]
    private ?string $miktar = null;

    #[ORM\Column(type: Types::DECIMAL, precision: 7, scale: 2)]
    private ?string $fiyat = null;

    #[ORM\Column(type: Types::DECIMAL, precision: 7, scale: 2)]
    private ?string $alisFiyati = null;

    #[ORM\Column(type: Types::DECIMAL, precision: 5, scale: 2)]
    private ?string $kdvOran = null;

    #[ORM\Column(type: Types::DECIMAL, precision: 7, scale: 2)]
    private ?string $kdvTutar = null;

    #[ORM\Column(type: Types::DECIMAL, precision: 7, scale: 2)]
    private ?string $iskTutar = null;

    #[ORM\Column(length: 255)]
    private ?string $insUn = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    private ?\DateTimeInterface $insDt = null;

    #[ORM\Column(length: 255)]
    private ?string $upoUn = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    private ?\DateTimeInterface $upDt = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getProductVariant(): ?ProductVariant
    {
        return $this->productVariant;
    }

    public function setProductVariant(ProductVariant $productVariant): static
    {
        $this->productVariant = $productVariant;

        return $this;
    }

    public function getProduct(): ?Product
    {
        return $this->product;
    }

    public function setProduct(Product $product): static
    {
        $this->product = $product;

        return $this;
    }

    public function getScs(): ?string
    {
        return $this->scs;
    }

    public function setScs(string $scs): static
    {
        $this->scs = $scs;

        return $this;
    }

    public function getSkod(): ?string
    {
        return $this->skod;
    }

    public function setSkod(string $skod): static
    {
        $this->skod = $skod;

        return $this;
    }

    public function getRkod(): ?int
    {
        return $this->rkod;
    }

    public function setRkod(int $rkod): static
    {
        $this->rkod = $rkod;

        return $this;
    }

    public function getBedKod(): ?int
    {
        return $this->bedKod;
    }

    public function setBedKod(int $bedKod): static
    {
        $this->bedKod = $bedKod;

        return $this;
    }

    public function getSatNo(): ?int
    {
        return $this->satNo;
    }

    public function setSatNo(int $satNo): static
    {
        $this->satNo = $satNo;

        return $this;
    }

    public function getSatTip(): ?string
    {
        return $this->satTip;
    }

    public function setSatTip(string $satTip): static
    {
        $this->satTip = $satTip;

        return $this;
    }

    public function getSatTar(): ?\DateTimeInterface
    {
        return $this->satTar;
    }

    public function setSatTar(\DateTimeInterface $satTar): static
    {
        $this->satTar = $satTar;

        return $this;
    }

    public function getLocation(): ?string
    {
        return $this->location;
    }

    public function setLocation(string $location): static
    {
        $this->location = $location;

        return $this;
    }

    public function getMiktar(): ?string
    {
        return $this->miktar;
    }

    public function setMiktar(string $miktar): static
    {
        $this->miktar = $miktar;

        return $this;
    }

    public function getFiyat(): ?string
    {
        return $this->fiyat;
    }

    public function setFiyat(string $fiyat): static
    {
        $this->fiyat = $fiyat;

        return $this;
    }

    public function getAlisFiyati(): ?string
    {
        return $this->alisFiyati;
    }

    public function setAlisFiyati(string $alisFiyati): static
    {
        $this->alisFiyati = $alisFiyati;

        return $this;
    }

    public function getKdvOran(): ?string
    {
        return $this->kdvOran;
    }

    public function setKdvOran(string $kdvOran): static
    {
        $this->kdvOran = $kdvOran;

        return $this;
    }

    public function getKdvTutar(): ?string
    {
        return $this->kdvTutar;
    }

    public function setKdvTutar(string $kdvTutar): static
    {
        $this->kdvTutar = $kdvTutar;

        return $this;
    }

    public function getIskTutar(): ?string
    {
        return $this->iskTutar;
    }

    public function setIskTutar(string $iskTutar): static
    {
        $this->iskTutar = $iskTutar;

        return $this;
    }

    public function getInsUn(): ?string
    {
        return $this->insUn;
    }

    public function setInsUn(string $insUn): static
    {
        $this->insUn = $insUn;

        return $this;
    }

    public function getInsDt(): ?\DateTimeInterface
    {
        return $this->insDt;
    }

    public function setInsDt(\DateTimeInterface $insDt): static
    {
        $this->insDt = $insDt;

        return $this;
    }

    public function getUpoUn(): ?string
    {
        return $this->upoUn;
    }

    public function setUpoUn(string $upoUn): static
    {
        $this->upoUn = $upoUn;

        return $this;
    }

    public function getUpDt(): ?\DateTimeInterface
    {
        return $this->upDt;
    }

    public function setUpDt(\DateTimeInterface $upDt): static
    {
        $this->upDt = $upDt;

        return $this;
    }
}
