<?php

namespace Wb3\SharedBundle\Entity;

use App\Repository\OrderItemCheckRepository;
use Doctrine\ORM\Mapping as ORM;
use Wb3\SharedBundle\Entity\Trait\TimestampTrait;

#[ORM\Entity(repositoryClass: OrderItemCheckRepository::class)]
class OrderItemCheck
{
    use TimestampTrait;
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\ManyToOne(inversedBy: 'orderItemChecks')]
    private ?OrderItem $item = null;

    #[ORM\Column(options: ['default' => false])]
    private ?bool $isApproved = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $description = null;

    #[ORM\ManyToOne(inversedBy: 'orderItemChecks')]
    private ?User $user = null;

    #[ORM\Column(nullable: true)]
    private ?\DateTimeImmutable $approvedAt = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getItem(): ?OrderItem
    {
        return $this->item;
    }

    public function setItem(?OrderItem $item): static
    {
        $this->item = $item;

        return $this;
    }

    public function isApproved(): ?bool
    {
        return $this->isApproved;
    }

    public function setIsApproved(bool $isApproved): static
    {
        $this->isApproved = $isApproved;

        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): static
    {
        $this->description = $description;

        return $this;
    }

    public function getUser(): ?User
    {
        return $this->user;
    }

    public function setUser(?User $user): static
    {
        $this->user = $user;

        return $this;
    }

    public function getApprovedAt(): ?\DateTimeImmutable
    {
        return $this->approvedAt;
    }

    public function setApprovedAt(?\DateTimeImmutable $approvedAt): static
    {
        $this->approvedAt = $approvedAt;

        return $this;
    }
}
