<?php

namespace Wb3\SharedBundle\Entity;

use App\Repository\ProductAttributeValueRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Doctrine\ORM\Mapping\Cache;
use Wb3\SharedBundle\Entity\Trait\TimestampTrait;

#[ORM\Entity(repositoryClass: ProductAttributeValueRepository::class)]
#[ORM\Index(fields: ['attribute'])]
#[Cache(usage: 'NONSTRICT_READ_WRITE', region: 'write_rare')]
class ProductAttributeValue
{
    use TimestampTrait;

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\ManyToOne(inversedBy: 'productAttributeValues')]
    private ?ProductAttribute $attribute = null;

    #[ORM\Column(length: 255)]
    private ?string $name = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $friendlyName = null;

    /**
     * @var Collection<int, PlatformPriceRule>
     */
    #[ORM\ManyToMany(targetEntity: PlatformPriceRule::class, mappedBy: 'brandAttributeValue')]
    private Collection $platformPriceRules;

    /**
     * @var Collection<int, PlatformForbiddenProduct>
     */
    #[ORM\OneToMany(targetEntity: PlatformForbiddenProduct::class, mappedBy: 'brand_attribute_value')]
    private Collection $platformForbiddenProducts;

    public function __construct()
    {
        $this->platformPriceRules = new ArrayCollection();
        $this->platformForbiddenProducts = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getAttribute(): ?ProductAttribute
    {
        return $this->attribute;
    }

    public function setAttribute(?ProductAttribute $attribute): static
    {
        $this->attribute = $attribute;

        return $this;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): static
    {
        $this->name = $name;

        return $this;
    }

    public function getFriendlyName(): ?string
    {
        return $this->friendlyName;
    }

    public function setFriendlyName(?string $friendlyName): ProductAttributeValue
    {
        $this->friendlyName = $friendlyName;
        return $this;
    }

    /**
     * @return Collection<int, PlatformPriceRule>
     */
    public function getPlatformPriceRules(): Collection
    {
        return $this->platformPriceRules;
    }

    public function addPlatformPriceRule(PlatformPriceRule $platformPriceRule): static
    {
        if (!$this->platformPriceRules->contains($platformPriceRule)) {
            $this->platformPriceRules->add($platformPriceRule);
            $platformPriceRule->addBrandAttributeValue($this);
        }

        return $this;
    }

    public function removePlatformPriceRule(PlatformPriceRule $platformPriceRule): static
    {
        if ($this->platformPriceRules->removeElement($platformPriceRule)) {
            $platformPriceRule->removeBrandAttributeValue($this);
        }

        return $this;
    }
    public function __toString(): string
    {return $this->getFriendlyName() ?? $this->getName();
    }

    /**
     * @return Collection<int, PlatformForbiddenProduct>
     */
    public function getPlatformForbiddenProducts(): Collection
    {
        return $this->platformForbiddenProducts;
    }

    public function addPlatformForbiddenProduct(PlatformForbiddenProduct $platformForbiddenProduct): static
    {
        if (!$this->platformForbiddenProducts->contains($platformForbiddenProduct)) {
            $this->platformForbiddenProducts->add($platformForbiddenProduct);
            $platformForbiddenProduct->setBrandAttributeValue($this);
        }

        return $this;
    }

    public function removePlatformForbiddenProduct(PlatformForbiddenProduct $platformForbiddenProduct): static
    {
        if ($this->platformForbiddenProducts->removeElement($platformForbiddenProduct)) {
            // set the owning side to null (unless already changed)
            if ($platformForbiddenProduct->getBrandAttributeValue() === $this) {
                $platformForbiddenProduct->setBrandAttributeValue(null);
            }
        }

        return $this;
    }

}
