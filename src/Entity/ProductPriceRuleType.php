<?php

namespace Wb3\SharedBundle\Entity;

use App\Repository\ProductPriceRuleTypeRepository;
use Doctrine\ORM\Mapping as ORM;
use Wb3\SharedBundle\Entity\Trait\TimestampTrait;

#[ORM\Entity(repositoryClass: ProductPriceRuleTypeRepository::class)]
class ProductPriceRuleType
{
    use TimestampTrait;
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(length: 255)]
    private ?string $name = null;

    #[ORM\Column(length: 10)]
    private ?string $sign = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): static
    {
        $this->name = $name;

        return $this;
    }

    public function getSign(): ?string
    {
        return $this->sign;
    }

    public function setSign(string $sign): static
    {
        $this->sign = $sign;

        return $this;
    }
  public function __toString(): string
    {
        return $this->getName()." (".$this->getSign().")";    }
}
