<?php

namespace Wb3\SharedBundle\Entity;

use App\Repository\OrderItemRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Wb3\SharedBundle\Entity\Trait\TimestampTrait;

#[ORM\Entity(repositoryClass: OrderItemRepository::class)]
class OrderItem
{
    use TimestampTrait;

    const STATE_NEW =  'new';
    const STATE_OUT_OF_STOCK =  'out_of_stock';
    const STATE_OUT_OF_STOCK_NOTIFIED =  'out_of_stock_notified';
    const STATE_PRICE_ERROR =  'price_error';
    const STATE_PRICE_ERROR_NOTIFIED =  'price_error_notified';
    const STATE_LOCATION_DEFINED =  'location_defined';
    const STATE_TRANSFERRED =  'transferred';
    const STATE_READY = 'ready';
    const STATE_VENDOR_CANCELED =  'vendor_canceled';
    const STATE_CUSTOMER_CANCELED =  'customer_canceled';
    const STATE_CUSTOMER_RETURNED =  'customer_returned';

    const TRANSITION_TO_PRICE_ERROR_NOTIFIED = 'toPriceErrorNotified';
    const TRANSITION_TO_PRICE_ERROR = 'toPriceError';
    const TRANSITION_TO_LOCATION_DEFINED = 'toLocationDefined';
    const TRANSITION_TO_TRANSFERRED = 'toTransferred';

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\ManyToOne(cascade: ['persist'], inversedBy: 'orderItems')]
    #[ORM\JoinColumn(nullable: false)]
    private Order $order;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(nullable: true)]
    private ?Location $location = null;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(nullable: true)]
    private ?ProductVariant $productVariant;

    #[ORM\Column(nullable: true)]
    private ?int $salesCampaignId = null;

    #[ORM\Column(type: Types::DECIMAL, precision: 10, scale: 2)]
    private ?float $price = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $currencyCode = null;

    #[ORM\Column(length: 255)]
    private ?string $state = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $userTransferred= null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $season = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $color = null;

    #[ORM\Column(options: ['default' => '0.00'])]
    private ?float $platformDiscount = null;

    #[ORM\Column(options: ['default' => '0.00'])]
    private ?float $vendorDiscount = null;

    #[ORM\Column(options: ['default' => '0.00'])]
    private ?float $totalDiscount = null;

    #[ORM\Column(options: ['default' => '0.00'])]
    private ?float $discountedTotalPrice = null;

    #[ORM\Column(options: ['default' => '0'])]
    private ?int $vatRate = null;

    #[ORM\Column(options: ['default' => '0.00'])]
    private ?float $commissionAmount = null;

    #[ORM\ManyToOne(inversedBy: 'orderItems')]
    private ?CampaignItem $campaignItem = null;

    /**
     * @var Collection<int, OrderItemCheck>
     */
    #[ORM\OneToMany(targetEntity: OrderItemCheck::class, mappedBy: 'item')]
    private Collection $orderItemChecks;

    #[ORM\Column(nullable: true)]
    private ?float $desi = null;

    #[ORM\Column(length: 50, options: ['default' => '0'])]
    private ?string $platformItemQuantityId = null;

    #[ORM\Column(options: ['default' => '0.00'])]
    private ?float $vendorPrice = 0;

    public function __construct()
    {
        $this->orderItemChecks = new ArrayCollection();
    }
    public function getId(): ?int
    {
        return $this->id;
    }

    public function getOrder(): ?Order
    {
        return $this->order;
    }

    public function setOrder(?Order $orderId): static
    {
        $this->order = $orderId;

        return $this;
    }


    public function getSalesCampaignId(): ?int
    {
        return $this->salesCampaignId;
    }

    public function setSalesCampaignId(?int $salesCampaignId): static
    {
        $this->salesCampaignId = $salesCampaignId;

        return $this;
    }

    public function getPrice(): ?string
    {
        return $this->price;
    }

    public function setPrice(string $price): static
    {
        $this->price = sprintf("%.2f", $price);

        return $this;
    }

    public function getCurrencyCode(): ?string
    {
        return $this->currencyCode;
    }

    public function setCurrencyCode(?string $currencyCode): static
    {
        $this->currencyCode = $currencyCode;

        return $this;
    }

    public function getState(): ?string
    {
        return $this->state;
    }

    public function setState(string $state): static
    {
        $this->state = $state;

        return $this;
    }
    public function getUserTransferred(): ?string
    {
        return $this->userTransferred;
    }

    public function setUserTransferred(string $userTransferred): static
    {
        $this->userTransferred = $userTransferred;

        return $this;
    }

    public function __toString()
    {
        return $this->getProductVariant()->getProduct()->getTitle(); // ya da uygun olan string özelliği
    }

    public function getLocation(): ?Location
    {
        return $this->location;
    }

    public function setLocation(?Location $location): static
    {
        $this->location = $location;

        return $this;
    }

    public function getProductVariant(): ?ProductVariant
    {
        return $this->productVariant;
    }

    public function setProductVariant(?ProductVariant $productVariant): static
    {
        $this->productVariant = $productVariant;
        return $this;
    }

    public function getSeason(): ?string
    {
        return $this->season;
    }

    public function setSeason(?string $season): static
    {
        $this->season = $season;

        return $this;
    }

    public function getColor(): ?string
    {
        return $this->color;
    }

    public function setColor(?string $color): static
    {
        $this->color = $color;

        return $this;
    }

    public function getPlatformDiscount(): ?float
    {
        return $this->platformDiscount;
    }

    public function setPlatformDiscount(float $platformDiscount): static
    {
        $this->platformDiscount = $platformDiscount;

        return $this;
    }

    public function getVendorDiscount(): ?float
    {
        return $this->vendorDiscount;
    }

    public function setVendorDiscount(float $vendorDiscount): static
    {
        $this->vendorDiscount = $vendorDiscount;

        return $this;
    }

    public function getTotalDiscount(): ?float
    {
        return $this->totalDiscount;
    }

    public function setTotalDiscount(float $totalDiscount): static
    {
        $this->totalDiscount = $totalDiscount;

        return $this;
    }

    public function getDiscountedTotalPrice(): ?float
    {
        return $this->discountedTotalPrice;
    }

    public function setDiscountedTotalPrice(float $discountedTotalPrice): static
    {
        $this->discountedTotalPrice = $discountedTotalPrice;

        return $this;
    }

    public function getVatRate(): ?int
    {
        return $this->vatRate;
    }

    public function setVatRate(int $vatRate): static
    {
        $this->vatRate = $vatRate;

        return $this;
    }

    public function getCommissionAmount(): ?float
    {
        return $this->commissionAmount;
    }

    public function setCommissionAmount(float $commissionAmount): static
    {
        $this->commissionAmount = $commissionAmount;

        return $this;
    }

    public function getCampaignItem(): ?CampaignItem
    {
        return $this->campaignItem;
    }

    public function setCampaignItem(?CampaignItem $campaignItem): static
    {
        $this->campaignItem = $campaignItem;

        return $this;
    }

    /**
     * @return Collection<int, OrderItemCheck>
     */
    public function getOrderItemChecks(): Collection
    {
        return $this->orderItemChecks;
    }

    public function addOrderItemCheck(OrderItemCheck $orderItemCheck): static
    {
        if (!$this->orderItemChecks->contains($orderItemCheck)) {
            $this->orderItemChecks->add($orderItemCheck);
            $orderItemCheck->setItem($this);
        }

        return $this;
    }

    public function removeOrderItemCheck(OrderItemCheck $orderItemCheck): static
    {
        if ($this->orderItemChecks->removeElement($orderItemCheck)) {
            // set the owning side to null (unless already changed)
            if ($orderItemCheck->getItem() === $this) {
                $orderItemCheck->setItem(null);
            }
        }

        return $this;
    }

    public function getDesi(): ?float
    {
        return $this->desi;
    }

    public function setDesi(?float $desi): static
    {
        $this->desi = $desi;

        return $this;
    }

    public function getPlatformItemQuantityId(): ?string
    {
        return $this->platformItemQuantityId;
    }

    public function setPlatformItemQuantityId(string $platformItemQuantityId): static
    {
        $this->platformItemQuantityId = $platformItemQuantityId;

        return $this;
    }

    public function getVendorPrice(): ?float
    {
        return $this->vendorPrice;
    }

    public function setVendorPrice(float $vendorPrice): static
    {
        $this->vendorPrice = $vendorPrice;

        return $this;
    }

}
