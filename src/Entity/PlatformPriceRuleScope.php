<?php

namespace Wb3\SharedBundle\Entity;

use App\Repository\PlatformPriceRuleScopeRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: PlatformPriceRuleScopeRepository::class)]
class PlatformPriceRuleScope
{

    const ID_GENERAL=1;
    const ID_PLATFORM=2;
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(length: 255)]
    private ?string $name = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): static
    {
        $this->name = $name;

        return $this;
    }
    public function __toString(): string
    {
        return $this->getName();
    }
}
