<?php
// src/Entity/User.php
namespace Wb3\SharedBundle\Entity;

use App\Repository\UserRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Doctrine\ORM\Mapping\Cache;
use Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface;
use Symfony\Component\Security\Core\User\UserInterface;
use Wb3\SharedBundle\Entity\Trait\TimestampTrait;

#[ORM\Entity(repositoryClass: UserRepository::class)]
#[ORM\Table(name: 'users')]
class User implements UserInterface, PasswordAuthenticatedUserInterface
{
    use TimestampTrait;
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private int $id;

    #[ORM\Column(type: 'string', length: 180, unique: true)]
    private ?string $email;

    #[ORM\Column(type: 'json')]
    private array $roles = [];

    #[ORM\Column(type: 'string')]
    private string $password;

    #[ORM\Column(length: 255)]
    private ?string $firstName = null;

    #[ORM\Column(length: 255)]
    private ?string $lastName = null;

    #[ORM\Column(length: 10, nullable: true)]
    private ?string $korgunPersonalCode = null;

    /**
     * @var Collection<int, OrderItemCheck>
     */
    #[ORM\OneToMany(targetEntity: OrderItemCheck::class, mappedBy: 'user')]
    private Collection $orderItemChecks;

    #[ORM\Column(nullable: true)]
    private ?array $locations = null;

    #[ORM\Column(options: ['default'=>true])]
    private ?bool $isActive = null;

    public function __construct()
    {
        $this->orderItemChecks = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function setEmail(string $email): self
    {
        $this->email = $email;

        return $this;
    }

    /**
     * The public representation of the user (e.g. a username, an email address, etc.)
     *
     * @see UserInterface
     */
    public function getUserIdentifier(): string
    {
        return (string) $this->email;
    }

    /**
     * @see UserInterface
     */
    public function getRoles(): array
    {
        $roles = $this->roles;
        // guarantee every user at least has ROLE_USER

        return array_unique($roles);
    }

    public function setRoles(array $roles): self
    {
        $this->roles = $roles;

        return $this;
    }

    /**
     * @see PasswordAuthenticatedUserInterface
     */
    public function getPassword(): string
    {
        return $this->password;
    }

    public function setPassword(string $password): self
    {
        $this->password = $password;

        return $this;
    }

    /**
     * @see UserInterface
     */
    public function eraseCredentials(): void
    {
        // If you store any temporary, sensitive data on the user, clear it here
        // $this->plainPassword = null;
    }

    public function getFirstName(): ?string
    {
        return $this->firstName;
    }

    public function setFirstName(string $firstName): static
    {
        $this->firstName = $firstName;

        return $this;
    }

    public function getLastName(): ?string
    {
        return $this->lastName;
    }

    public function setLastName(string $lastName): static
    {
        $this->lastName = $lastName;

        return $this;
    }

    public function getKorgunPersonalCode(): ?string
    {
        return $this->korgunPersonalCode;
    }

    public function setKorgunPersonalCode(?string $korgunPersonalCode): static
    {
        $this->korgunPersonalCode = $korgunPersonalCode;

        return $this;
    }
    public function __toString(): string
    {
        return $this->firstName . ' ' . $this->lastName;
    }

    /**
     * @return Collection<int, OrderItemCheck>
     */
    public function getOrderItemChecks(): Collection
    {
        return $this->orderItemChecks;
    }

    public function addOrderItemCheck(OrderItemCheck $orderItemCheck): static
    {
        if (!$this->orderItemChecks->contains($orderItemCheck)) {
            $this->orderItemChecks->add($orderItemCheck);
            $orderItemCheck->setUser($this);
        }

        return $this;
    }

    public function removeOrderItemCheck(OrderItemCheck $orderItemCheck): static
    {
        if ($this->orderItemChecks->removeElement($orderItemCheck)) {
            // set the owning side to null (unless already changed)
            if ($orderItemCheck->getUser() === $this) {
                $orderItemCheck->setUser(null);
            }
        }

        return $this;
    }

    public function getLocations(): ?array
    {
        return $this->locations;
    }

    public function setLocations(?array $locations): static
    {
        $this->locations = $locations;

        return $this;
    }

    public function isActive(): ?bool
    {
        return $this->isActive;
    }

    public function setIsActive(bool $isActive): static
    {
        $this->isActive = $isActive;

        return $this;
    }
}