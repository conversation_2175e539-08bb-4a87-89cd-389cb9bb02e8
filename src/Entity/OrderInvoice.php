<?php

namespace Wb3\SharedBundle\Entity;

use App\Repository\OrderInvoiceRepository;
use Doctrine\ORM\Mapping as ORM;
use Wb3\SharedBundle\Entity\Trait\TimestampTrait;

#[ORM\Entity(repositoryClass: OrderInvoiceRepository::class)]
class OrderInvoice
{
    use TimestampTrait;
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\OneToOne(cascade: ['persist', 'remove'])]
    #[ORM\JoinColumn(nullable: false)]
    private ?order $order = null;

    #[ORM\OneToOne(cascade: ['persist', 'remove'])]
    #[ORM\JoinColumn(nullable: true)]
    private ?OrderKorgunInvoice $korgunInvoice = null;

    #[ORM\Column(length: 50)]
    private ?string $invoiceNumber = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $dispatchNo = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getOrder(): ?order
    {
        return $this->order;
    }

    public function setOrder(order $orderId): static
    {
        $this->order = $orderId;

        return $this;
    }

    public function getKorgunInvoice(): ?OrderKorgunInvoice
    {
        return $this->korgunInvoice;
    }

    public function setKorgunInvoice(?OrderKorgunInvoice $korgunInvoice): static
    {
        $this->korgunInvoice = $korgunInvoice;

        return $this;
    }

    public function getInvoiceNumber(): ?string
    {
        return $this->invoiceNumber;
    }

    public function setInvoiceNumber(string $invoiceNumber): static
    {
        $this->invoiceNumber = $invoiceNumber;

        return $this;
    }

    public function getDispatchNo(): ?string
    {
        return $this->dispatchNo;
    }

    public function setDispatchNo(?string $dispatchNo): static
    {
        $this->dispatchNo = $dispatchNo;

        return $this;
    }
}
