<?php

namespace Wb3\SharedBundle\Entity;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use App\Repository\PlatformBrandRepository;
use Wb3\SharedBundle\Entity\Trait\TimestampTrait;

#[ORM\Entity(repositoryClass: PlatformBrandRepository::class)]
class PlatformBrand
{
    use TimestampTrait;
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    /**
     * @var Collection<int, Platform>
     */
    #[ORM\OneToMany(targetEntity: Platform::class, mappedBy: 'brand')]
    private Collection $platform;

    #[ORM\Column(length: 255)]
    private ?string $remoteId = null;

    #[ORM\Column(length: 255)]
    private ?string $name = null;

    /**
     * @var Collection<int, PlatformProductVariant>
     */
    #[ORM\OneToMany(targetEntity: PlatformProductVariant::class, mappedBy: 'platformBrand')]
    private Collection $productVariantPlatforms;

    public function __construct()
    {
        $this->platform = new ArrayCollection();
        $this->productVariantPlatforms = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    /**
     * @return Collection<int, Platform>
     */
    public function getPlatform(): Collection
    {
        return $this->platform;
    }

    public function addPlatform(Platform $platform): static
    {
        if (!$this->platform->contains($platform)) {
            $this->platform->add($platform);
            $platform->setBrand($this);
        }

        return $this;
    }

    public function removePlatform(Platform $platform): static
    {
        if ($this->platform->removeElement($platform)) {
            // set the owning side to null (unless already changed)
            if ($platform->getBrand() === $this) {
                $platform->setBrand(null);
            }
        }

        return $this;
    }

    public function getRemoteId(): ?string
    {
        return $this->remoteId;
    }

    public function setRemoteId(string $remoteId): static
    {
        $this->remoteId = $remoteId;

        return $this;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): static
    {
        $this->name = $name;

        return $this;
    }

    /**
     * @return Collection<int, PlatformProductVariant>
     */
    public function getProductVariantPlatforms(): Collection
    {
        return $this->productVariantPlatforms;
    }

    public function addProductVariantPlatform(PlatformProductVariant $productVariantPlatform): static
    {
        if (!$this->productVariantPlatforms->contains($productVariantPlatform)) {
            $this->productVariantPlatforms->add($productVariantPlatform);
            $productVariantPlatform->setPlatformBrand($this);
        }

        return $this;
    }

    public function removeProductVariantPlatform(PlatformProductVariant $productVariantPlatform): static
    {
        if ($this->productVariantPlatforms->removeElement($productVariantPlatform)) {
            // set the owning side to null (unless already changed)
            if ($productVariantPlatform->getPlatformBrand() === $this) {
                $productVariantPlatform->setPlatformBrand(null);
            }
        }

        return $this;
    }
}
