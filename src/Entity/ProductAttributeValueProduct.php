<?php

namespace Wb3\SharedBundle\Entity;

use Doctrine\ORM\Mapping as ORM;
use Doctrine\ORM\Mapping\Cache;
use phpseclib3\File\ASN1\Maps\AttributeValue;
use Wb3\SharedBundle\Entity\Trait\TimestampTrait;

#[ORM\Entity(repositoryClass: ProductAttributeValueProduct::class)]
#[ORM\Index(fields:['product','attribute'])]
#[ORM\Index(fields:['product'])]
#[Cache(usage: 'NONSTRICT_READ_WRITE', region: 'write_rare')]
class ProductAttributeValueProduct
{
    use TimestampTrait;

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\ManyToOne(targetEntity: Product::class)]
    private Product $product;

    #[ORM\ManyToOne(targetEntity: ProductAttribute::class)]
    private ProductAttribute $attribute;

    #[ORM\ManyToOne(targetEntity: ProductAttributeValue::class)]
    private ProductAttributeValue $value;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getProduct()
    {
        return $this->product;
    }

    public function setProduct($product)
    {
        $this->product = $product;
        return $this;
    }

    public function getAttribute()
    {
        return $this->attribute;
    }

    public function setAttribute($attribute)
    {
        $this->attribute = $attribute;
        return $this;
    }

    public function getValue()
    {
        return $this->value;
    }

    public function setValue($value)
    {
        $this->value = $value;
        return $this;
    }
}