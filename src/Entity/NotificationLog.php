<?php

namespace Wb3\SharedBundle\Entity;

use App\Repository\NotificationLogRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Wb3\SharedBundle\Entity\Trait\TimestampTrait;
#[ORM\Table(name: "tool_notification_log")]
#[ORM\Entity(repositoryClass: NotificationLogRepository::class)]
class NotificationLog
{
    use TimestampTrait;

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(type: Types::TEXT)]
    private ?string $recipient = null;

    #[ORM\Column(length: 255)]
    private ?string $subject = null;

    #[ORM\Column(length: 255)]
    private ?string $triggerType = null;

    #[ORM\Column(length: 255)]
    private ?string $triggerCode = null;

    #[ORM\Column]
    private ?bool $result = null;

    #[ORM\Column(type: Types::TEXT)]
    private ?string $content = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getRecipient(): ?string
    {
        return $this->recipient;
    }

    public function setRecipient(string $recipient): static
    {
        $this->recipient = $recipient;

        return $this;
    }

    public function getSubject(): ?string
    {
        return $this->subject;
    }

    public function setSubject(string $subject): static
    {
        $this->subject = $subject;

        return $this;
    }

    public function getTriggerType(): ?string
    {
        return $this->triggerType;
    }

    public function setTriggerType(string $triggerType): static
    {
        $this->triggerType = $triggerType;

        return $this;
    }

    public function getTriggerCode(): ?string
    {
        return $this->triggerCode;
    }

    public function setTriggerCode(string $triggerCode): static
    {
        $this->triggerCode = $triggerCode;

        return $this;
    }

    public function isResult(): ?bool
    {
        return $this->result;
    }

    public function setResult(bool $result): static
    {
        $this->result = $result;

        return $this;
    }

    public function getContent(): ?string
    {
        return $this->content;
    }

    public function setContent(string $content): static
    {
        $this->content = $content;

        return $this;
    }
}
