<?php

namespace Wb3\SharedBundle\Entity;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use App\Repository\PlatformRepository;
use Doctrine\ORM\Mapping\UniqueConstraint;
use Wb3\SharedBundle\Entity\Trait\TimestampTrait;

#[UniqueConstraint(columns: ['code'])]
#[ORM\Entity(repositoryClass: PlatformRepository::class)]
class Platform
{
    use TimestampTrait;

    const CODE_TRENDYOL = 'ty';
    const CODE_TRENDYOL_ALI_SOLMAZ = 'tyas';
    const CODE_HEPSIBURADA = 'hb';

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(length: 5)]
    private ?string $code = null;

    #[ORM\Column(length: 255)]
    private ?string $title = null;

    #[ORM\OneToMany(targetEntity: PlatformProductVariant::class, mappedBy: 'platform')]
    private Collection $productVariantPlatforms;

    /**
     * @var Collection<int, PlatformForbiddenProduct>
     */
    #[ORM\ManyToMany(targetEntity: PlatformForbiddenProduct::class, mappedBy: 'Platform')]
    private Collection $forbiddenProducts;

    /**
     * @var Collection<int, PlatformBuffer>
     */
    #[ORM\ManyToMany(targetEntity: PlatformBuffer::class, mappedBy: 'platforms')]
    private Collection $buffers;

    /**
     * @var Collection<int, PlatformPriceRule>
     */
    #[ORM\ManyToMany(targetEntity: PlatformPriceRule::class, mappedBy: 'platform')]
    private Collection $pPriceRules;

    /**
     * @var Collection<int, PlatformCategory>
     */
    #[ORM\OneToMany(targetEntity: PlatformCategory::class, mappedBy: 'platform')]
    private Collection $categories;

    #[ORM\ManyToOne(inversedBy: 'platform')]
    private ?PlatformBrand $brand = null;

    /**
     * @var Collection<int, PlatformProduct>
     */
    #[ORM\OneToMany(targetEntity: PlatformProduct::class, mappedBy: 'platform')]
    private Collection $products;

    /**
     * @var Collection<int, PlatformMatchBrand>
     */
    #[ORM\OneToMany(targetEntity: PlatformMatchBrand::class, mappedBy: 'platform')]
    private Collection $matchBrands;

    /**
     * @var Collection<int, PlatformMatchCategory>
     */
    #[ORM\OneToMany(targetEntity: PlatformMatchCategory::class, mappedBy: 'platform')]
    private Collection $matchCategories;

    /**
     * @var Collection<int, PlatformMatchVariant>
     */
    #[ORM\OneToMany(targetEntity: PlatformMatchVariant::class, mappedBy: 'platform')]
    private Collection $matchProductVariants;

    /**
     * @var Collection<int, PlatformLog>
     */
    #[ORM\OneToMany(targetEntity: PlatformLog::class, mappedBy: 'platform')]
    private Collection $logs;

    /**
     * @var Collection<int, Task>
     */
    #[ORM\OneToMany(targetEntity: Task::class, mappedBy: 'platform')]
    private Collection $tasks;

    #[ORM\Column(nullable: false,options:['default'=>true])]
    private ?bool $isPlatformCargo = null;

    #[ORM\Column(length: 10, nullable: true)]
    private ?string $korgunSellerCode = null;

    #[ORM\Column(length: 10,options: ['default' => 'N/A'])]
    private ?string $korgunPaymentType = null;

    /**
     * @var Collection<int, CampaignItem>
     */
    #[ORM\OneToMany(targetEntity: CampaignItem::class, mappedBy: 'platform')]
    private Collection $promoCampaignItems;

    /**
     * @var Collection<int, Campaign>
     */
    #[ORM\ManyToMany(targetEntity: Campaign::class, mappedBy: 'platforms')]
    private Collection $campaigns;

    #[ORM\Column(type: Types::TEXT,options: ['default'=>'İŞBU FATURA TUTARI ... HESABINA DEVREDİLMİŞTİR.'])]
    private ?string $salesDescription = null;

    public function __construct()
    {
        $this->productVariantPlatforms = new ArrayCollection();
        $this->forbiddenProducts = new ArrayCollection();
        $this->buffers = new ArrayCollection();
        $this->pPriceRules = new ArrayCollection();
        $this->categories = new ArrayCollection();
        $this->products = new ArrayCollection();
        $this->matchBrands = new ArrayCollection();
        $this->matchCategories = new ArrayCollection();
        $this->matchProductVariants = new ArrayCollection();
        $this->logs = new ArrayCollection();
        $this->tasks = new ArrayCollection();
        $this->promoCampaignItems = new ArrayCollection();
        $this->campaigns = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getCode(): ?string
    {
        return $this->code;
    }

    public function setCode(string $code): static
    {
        $this->code = $code;

        return $this;
    }

    public function getTitle(): ?string
    {
        return $this->title;
    }

    public function setTitle(string $title): static
    {
        $this->title = $title;

        return $this;
    }

    /**
     * @return Collection<int, PlatformProductVariant>
     */
    public function getProductVariantPlatforms(): Collection
    {
        return $this->productVariantPlatforms;
    }

    public function addProductVariantPlatform(PlatformProductVariant $productVariantPlatform): static
    {
        if (!$this->productVariantPlatforms->contains($productVariantPlatform)) {
            $this->productVariantPlatforms->add($productVariantPlatform);
            $productVariantPlatform->setPlatform($this);
        }

        return $this;
    }

    public function removeProductVariantPlatform(PlatformProductVariant $productVariantPlatform): static
    {
        if ($this->productVariantPlatforms->removeElement($productVariantPlatform)) {
            // set the owning side to null (unless already changed)
            if ($productVariantPlatform->getPlatform() === $this) {
                $productVariantPlatform->setPlatform(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, PlatformForbiddenProduct>
     */
    public function getForbiddenProducts(): Collection
    {
        return $this->forbiddenProducts;
    }

    public function addType(PlatformForbiddenProduct $type): static
    {
        if (!$this->forbiddenProducts->contains($type)) {
            $this->forbiddenProducts->add($type);
            $type->addPlatform($this);
        }

        return $this;
    }

    public function removeType(PlatformForbiddenProduct $type): static
    {
        if ($this->forbiddenProducts->removeElement($type)) {
            $type->removePlatform($this);
        }

        return $this;
    }

    /**
     * @return Collection<int, PlatformBuffer>
     */
    public function getBuffers(): Collection
    {
        return $this->buffers;
    }

    public function addPlatformBuffer(PlatformBuffer $platformBuffer): static
    {
        if (!$this->buffers->contains($platformBuffer)) {
            $this->buffers->add($platformBuffer);
            $platformBuffer->addPlatform($this);
        }

        return $this;
    }

    public function removePlatformBuffer(PlatformBuffer $platformBuffer): static
    {
        if ($this->buffers->removeElement($platformBuffer)) {
            $platformBuffer->removePlatform($this);
        }

        return $this;
    }

    public function __toString(): string
    {
       return $this->getTitle();
    }

    /**
     * @return Collection<int, PlatformPriceRule>
     */
    public function getPPriceRules(): Collection
    {
        return $this->pPriceRules;
    }

    public function addPlatformPriceRule(PlatformPriceRule $platformPriceRule): static
    {
        if (!$this->pPriceRules->contains($platformPriceRule)) {
            $this->pPriceRules->add($platformPriceRule);
            $platformPriceRule->addPlatform($this);
        }

        return $this;
    }

    public function removePlatformPriceRule(PlatformPriceRule $platformPriceRule): static
    {
        if ($this->pPriceRules->removeElement($platformPriceRule)) {
            $platformPriceRule->removePlatform($this);
        }

        return $this;
    }

    /**
     * @return Collection<int, PlatformCategory>
     */
    public function getCategories(): Collection
    {
        return $this->categories;
    }

    public function addCategory(PlatformCategory $category): static
    {
        if (!$this->categories->contains($category)) {
            $this->categories->add($category);
            $category->setPlatform($this);
        }

        return $this;
    }

    public function removeCategory(PlatformCategory $category): static
    {
        if ($this->categories->removeElement($category)) {
            // set the owning side to null (unless already changed)
            if ($category->getPlatform() === $this) {
                $category->setPlatform(null);
            }
        }

        return $this;
    }

    public function getBrand(): ?PlatformBrand
    {
        return $this->brand;
    }

    public function setBrand(?PlatformBrand $brand): static
    {
        $this->brand = $brand;

        return $this;
    }

    /**
     * @return Collection<int, PlatformProduct>
     */
    public function getProducts(): Collection
    {
        return $this->products;
    }

    public function addPlatformProduct(PlatformProduct $platformProduct): static
    {
        if (!$this->products->contains($platformProduct)) {
            $this->products->add($platformProduct);
            $platformProduct->setPlatform($this);
        }

        return $this;
    }

    public function removePlatformProduct(PlatformProduct $platformProduct): static
    {
        if ($this->products->removeElement($platformProduct)) {
            // set the owning side to null (unless already changed)
            if ($platformProduct->getPlatform() === $this) {
                $platformProduct->setPlatform(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, PlatformMatchBrand>
     */
    public function getMatchBrands(): Collection
    {
        return $this->matchBrands;
    }

    public function addPlatformMatchBrand(PlatformMatchBrand $platformMatchBrand): static
    {
        if (!$this->matchBrands->contains($platformMatchBrand)) {
            $this->matchBrands->add($platformMatchBrand);
            $platformMatchBrand->setPlatform($this);
        }

        return $this;
    }

    public function removePlatformMatchBrand(PlatformMatchBrand $platformMatchBrand): static
    {
        if ($this->matchBrands->removeElement($platformMatchBrand)) {
            // set the owning side to null (unless already changed)
            if ($platformMatchBrand->getPlatform() === $this) {
                $platformMatchBrand->setPlatform(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, PlatformMatchCategory>
     */
    public function getMatchCategories(): Collection
    {
        return $this->matchCategories;
    }

    public function addPlatformMatchCategory(PlatformMatchCategory $platformMatchCategory): static
    {
        if (!$this->matchCategories->contains($platformMatchCategory)) {
            $this->matchCategories->add($platformMatchCategory);
            $platformMatchCategory->setPlatform($this);
        }

        return $this;
    }

    public function removePlatformMatchCategory(PlatformMatchCategory $platformMatchCategory): static
    {
        if ($this->matchCategories->removeElement($platformMatchCategory)) {
            // set the owning side to null (unless already changed)
            if ($platformMatchCategory->getPlatform() === $this) {
                $platformMatchCategory->setPlatform(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, PlatformMatchVariant>
     */
    public function getMatchProductVariants(): Collection
    {
        return $this->matchProductVariants;
    }

    public function addPlatformMatchProductVariant(PlatformMatchVariant $platformMatchProductVariant): static
    {
        if (!$this->matchProductVariants->contains($platformMatchProductVariant)) {
            $this->matchProductVariants->add($platformMatchProductVariant);
            $platformMatchProductVariant->setPlatform($this);
        }

        return $this;
    }

    public function removePlatformMatchProductVariant(PlatformMatchVariant $platformMatchProductVariant): static
    {
        if ($this->matchProductVariants->removeElement($platformMatchProductVariant)) {
            // set the owning side to null (unless already changed)
            if ($platformMatchProductVariant->getPlatform() === $this) {
                $platformMatchProductVariant->setPlatform(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, PlatformLog>
     */
    public function getLogs(): Collection
    {
        return $this->logs;
    }

    public function addPlatformLog(PlatformLog $platformLog): static
    {
        if (!$this->logs->contains($platformLog)) {
            $this->logs->add($platformLog);
            $platformLog->setPlatform($this);
        }

        return $this;
    }

    public function removePlatformLog(PlatformLog $platformLog): static
    {
        if ($this->logs->removeElement($platformLog)) {
            // set the owning side to null (unless already changed)
            if ($platformLog->getPlatform() === $this) {
                $platformLog->setPlatform(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, Task>
     */
    public function getTasks(): Collection
    {
        return $this->tasks;
    }

    public function addTask(Task $task): static
    {
        if (!$this->tasks->contains($task)) {
            $this->tasks->add($task);
            $task->setPlatform($this);
        }

        return $this;
    }

    public function removeTask(Task $task): static
    {
        if ($this->tasks->removeElement($task)) {
            // set the owning side to null (unless already changed)
            if ($task->getPlatform() === $this) {
                $task->setPlatform(null);
            }
        }

        return $this;
    }

    public function isPlatformCargo(): ?bool
    {
        return $this->isPlatformCargo;
    }

    public function setPlatformCargo(bool $isPlatformCargo): static
    {
        $this->isPlatformCargo = $isPlatformCargo;

        return $this;
    }

    public function getKorgunSellerCode(): ?string
    {
        return $this->korgunSellerCode;
    }

    public function setKorgunSellerCode(?string $korgunSellerCode): static
    {
        $this->korgunSellerCode = $korgunSellerCode;

        return $this;
    }

    public function getKorgunPaymentType(): ?string
    {
        return $this->korgunPaymentType;
    }

    public function setKorgunPaymentType(string $korgunPaymentType): static
    {
        $this->korgunPaymentType = $korgunPaymentType;

        return $this;
    }

    /**
     * @return Collection<int, CampaignItem>
     */
    public function getPromoCampaignItems(): Collection
    {
        return $this->promoCampaignItems;
    }

    public function addPromoCampaignItem(CampaignItem $promoCampaignItem): static
    {
        if (!$this->promoCampaignItems->contains($promoCampaignItem)) {
            $this->promoCampaignItems->add($promoCampaignItem);
            $promoCampaignItem->setPlatform($this);
        }

        return $this;
    }

    public function removePromoCampaignItem(CampaignItem $promoCampaignItem): static
    {
        if ($this->promoCampaignItems->removeElement($promoCampaignItem)) {
            // set the owning side to null (unless already changed)
            if ($promoCampaignItem->getPlatform() === $this) {
                $promoCampaignItem->setPlatform(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, Campaign>
     */
    public function getCampaigns(): Collection
    {
        return $this->campaigns;
    }

    public function addCampaign(Campaign $promoCampaign): static
    {
        if (!$this->campaigns->contains($promoCampaign)) {
            $this->campaigns->add($promoCampaign);
            $promoCampaign->addPlatform($this);
        }

        return $this;
    }

    public function removeCampaign(Campaign $promoCampaign): static
    {
        if ($this->campaigns->removeElement($promoCampaign)) {
            $promoCampaign->removePlatform($this);
        }

        return $this;
    }

    public function getSalesDescription(): ?string
    {
        return $this->salesDescription;
    }

    public function setSalesDescription(string $salesDescription): static
    {
        $this->salesDescription = $salesDescription;

        return $this;
    }
}
