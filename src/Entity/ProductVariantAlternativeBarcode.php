<?php

namespace Wb3\SharedBundle\Entity;

use App\Repository\ProductVariantAlternativeBarcodeRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: ProductVariantAlternativeBarcodeRepository::class)]
class ProductVariantAlternativeBarcode
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(length: 20)]
    private ?string $barcode = null;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(nullable: false)]
    private ?ProductVariant $variant = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getBarcode(): ?string
    {
        return $this->barcode;
    }

    public function setBarcode(string $barcode): static
    {
        $this->barcode = $barcode;

        return $this;
    }

    public function getProductVariant(): ?ProductVariant
    {
        return $this->variant;
    }

    public function setProductVariant(?ProductVariant $variant): static
    {
        $this->variant = $variant;

        return $this;
    }
}
