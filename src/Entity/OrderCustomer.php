<?php

namespace Wb3\SharedBundle\Entity;

use App\Repository\OrderCustomerRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Wb3\SharedBundle\Entity\Trait\TimestampTrait;

#[ORM\Entity(repositoryClass: OrderCustomerRepository::class)]
class OrderCustomer
{
    use TimestampTrait;
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $korgunCustomerId  = null;

    #[ORM\Column(length: 255)]
    private ?string $name = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $mail = null;

    #[ORM\Column(length: 20, nullable: true)]
    private ?string $gsm = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $extraName = null;

    #[ORM\Column(length: 20, nullable: true)]
    private ?string $extraGsm = null;

    #[ORM\Column(length: 11, nullable: true)]
    private ?string $tckn = null;

    #[ORM\OneToMany(targetEntity: OrderAddress::class, mappedBy: 'customer',cascade: ['persist'])]
    private Collection $addresses;

    public function __construct()
    {
        $this->addresses = new ArrayCollection();
    }


    public function getId(): ?int
    {
        return $this->id;
    }

    public function getkorgunCustomerId (): ?string
    {
        return $this->korgunCustomerId ;
    }

    public function setkorgunCustomerId (?string $korgunCustomerId ): static
    {
        $this->korgunCustomerId  = $korgunCustomerId ;

        return $this;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): static
    {
        $this->name = $name;

        return $this;
    }

    public function getMail(): ?string
    {
        return $this->mail;
    }

    public function setMail(?string $mail): static
    {
        $this->mail = $mail;

        return $this;
    }

    public function getGsm(): ?string
    {
        return $this->gsm;
    }

    public function setGsm(?string $gsm): static
    {
        $this->gsm = $gsm;

        return $this;
    }

    public function getExtraName(): ?string
    {
        return $this->extraName;
    }

    public function setExtraName(?string $extraName): static
    {
        $this->extraName = $extraName;

        return $this;
    }

    public function getExtraGsm(): ?string
    {
        return $this->extraGsm;
    }

    public function setExtraGsm(?string $extraGsm): static
    {
        $this->extraGsm = $extraGsm;

        return $this;
    }

    public function getTckn(): ?string
    {
        return $this->tckn;
    }

    public function setTckn(?string $tckn): static
    {
        $this->tckn = $tckn;

        return $this;
    }
}
