<?php

namespace Wb3\SharedBundle\Entity;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use App\Repository\PlatformMatchBrandRepository;
use Wb3\SharedBundle\Entity\Trait\TimestampTrait;

#[ORM\UniqueConstraint(name: 'platform_remote_id_name_unique', columns: ['platform_id','remote_id','name'])]
#[ORM\Entity(repositoryClass: PlatformMatchBrandRepository::class)]
class PlatformMatchBrand
{
    use TimestampTrait;
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\ManyToOne(inversedBy: 'platformMatchBrands')]
    private ?Platform $platform = null;

    #[ORM\Column(length: 255)]
    private ?string $name = null;

    #[ORM\Column]
    private ?int $remoteId = null;

    /**
     * @var Collection<int, PlatformMatchVariant>
     */
    #[ORM\OneToMany(targetEntity: PlatformMatchVariant::class, mappedBy: 'platformMatchBrand')]
    private Collection $platformMatchVariants;

    public function __construct()
    {
        $this->platformMatchVariants = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getPlatform(): ?Platform
    {
        return $this->platform;
    }

    public function setPlatform(?Platform $platform): static
    {
        $this->platform = $platform;

        return $this;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): static
    {
        $this->name = $name;

        return $this;
    }

    public function getRemoteId(): ?int
    {
        return $this->remoteId;
    }

    public function setRemoteId(int $remoteId): static
    {
        $this->remoteId = $remoteId;

        return $this;
    }

    /**
     * @return Collection<int, PlatformMatchVariant>
     */
    public function getPlatformMatchVariants(): Collection
    {
        return $this->platformMatchVariants;
    }

    public function addPlatformMatchProductVariant(PlatformMatchVariant $platformMatchProductVariant): static
    {
        if (!$this->platformMatchVariants->contains($platformMatchProductVariant)) {
            $this->platformMatchVariants->add($platformMatchProductVariant);
            $platformMatchProductVariant->setBrand($this);
        }

        return $this;
    }

    public function removePlatformMatchProductVariant(PlatformMatchVariant $platformMatchProductVariant): static
    {
        if ($this->platformMatchVariants->removeElement($platformMatchProductVariant)) {
            // set the owning side to null (unless already changed)
            if ($platformMatchProductVariant->getBrand() === $this) {
                $platformMatchProductVariant->setBrand(null);
            }
        }

        return $this;
    }

    public function __toString(): string
    {
       return $this->getId()." - ".$this->getPlatform()->getTitle()." - ".$this->getName();
    }
}
