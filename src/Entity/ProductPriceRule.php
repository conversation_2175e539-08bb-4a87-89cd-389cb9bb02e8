<?php

namespace Wb3\SharedBundle\Entity;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Wb3\SharedBundle\Entity\Trait\TimestampTrait;
use App\Repository\ProductPriceRuleRepository;

#[ORM\Entity(repositoryClass: ProductPriceRuleRepository::class)]
class ProductPriceRule
{
    use TimestampTrait;

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;


    /**
     * @var Collection<int, ProductBrand>
     */
    #[ORM\ManyToMany(targetEntity: ProductBrand::class, inversedBy: 'productPriceRules')]
    private Collection $brand;

    #[ORM\ManyToOne(inversedBy: 'productPriceRules')]
    #[ORM\JoinColumn(nullable: false)]
    private ?ProductPriceRuleType $type = null;

    #[ORM\Column]
    private float $value;

    #[ORM\Column(options: ['default' => true])]
    private bool $isActive;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $description = null;

    public function __construct()
    {
        $this->brand = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    /**
     * @return Collection<int, ProductAttributeValue>
     */
    public function getBrand(): Collection
    {
        return $this->brand;
    }
    /**
     * @return Collection<int, ProductAttributeValue>
     */
    public function setBrand(Collection $brand): self
    {
        $this->brand = $brand;
        return $this;
    }
    public function addBrand(ProductBrand $productBrand): self
    {
        if (!$this->brand->contains($productBrand)) {
            $this->brand[] = $productBrand;
        }

        return $this;
    }

    public function removeBrand(ProductBrand $productBrand): self
    {
        $this->brand->removeElement($productBrand);

        return $this;
    }


        public function getType(): ?ProductPriceRuleType
    {
        return $this->type;
    }

    public function setType(ProductPriceRuleType $type): static
    {
        $this->type = $type;

        return $this;
    }

    public function getValue(): float
    {
        return $this->value;
    }
    private function getExcludedProductAttributeValueIds(ProductPriceRule $productPriceRule): array
    {
        // Önceden seçilen markaların ID'lerini debug edelim
        if ($productPriceRule->getBrandAttributeValues()->count() > 0) {
            $ids = $productPriceRule->getBrandAttributeValues()->map(function ($pav) {
                return $pav->getId();
            })->toArray();

            // Debug için ID'leri yazdırın
            dump($ids);

            return $ids;
        }

        return [];
    }
    public function setValue(float $value): static
    {
        $this->value = $value;

        return $this;
    }

    public function isActive(): bool
    {
        return $this->isActive;
    }

    public function setActive(bool $isActive): static
    {
        $this->isActive = $isActive;

        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): static
    {
        $this->description = $description;

        return $this;
    }
    public function __toString(): string
    {
        $values = [];

        foreach ($this->brandAttributeValues as $attributeValue) {
            $values[] = (string) $attributeValue; // Assuming ProductAttributeValue has its own __toString() method
        }

        return implode(', ', $values); // Join all values with a comma or another separator
    }
}