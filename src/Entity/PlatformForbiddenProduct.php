<?php

namespace Wb3\SharedBundle\Entity;

use App\Repository\PlatformForbiddenProductRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Wb3\SharedBundle\Entity\Trait\TimestampTrait;

#[ORM\Entity(repositoryClass: PlatformForbiddenProductRepository::class)]
class PlatformForbiddenProduct
{
    use TimestampTrait;
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    /**
     * @var Collection<int, ProductCategory>
     */
    #[ORM\ManyToMany(targetEntity: ProductCategory::class, inversedBy: 'platformForbiddenProducts')]
    private Collection $categories;

    /**
     * @var Collection<int, Platform>
     */
    #[ORM\ManyToMany(targetEntity: Platform::class, inversedBy: 'ForbiddenProducts')]
    private Collection $platforms;


    #[ORM\Column]
    private bool $isActive = true;

    #[ORM\ManyToOne(targetEntity: ProductBrand::class, inversedBy: null)]
    private ?ProductBrand $productBrand = null;
    #[ORM\ManyToOne(inversedBy: 'platformForbiddenProducts')]
    private ?ProductAttributeValue $brandAttributeValue = null;

    #[ORM\ManyToOne(inversedBy: 'platformForbiddenProducts')]
    private ?PlatformForbiddenProductType $type = null;

    /**
     * @var Collection<int, ProductAttributeValue>
     */
    #[ORM\ManyToMany(targetEntity: ProductAttributeValue::class)]
    private Collection $sizeGroups;

    public function __construct()
    {
        $this->categories = new ArrayCollection();
        $this->platforms = new ArrayCollection();
        $this->sizeGroups = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }
    public function getProductBrand(): ?ProductBrand
    {
        return $this->productBrand;
    }

    public function setProductBrand(?ProductBrand $productBrand): static
    {
        $this->productBrand = $productBrand;

        return $this;
    }

    /**
     * @return Collection<int, ProductCategory>
     */
    public function getCategories(): Collection
    {
        return $this->categories;
    }

    public function addCategory(ProductCategory $productCategory): static
    {
        if (!$this->categories->contains($productCategory)) {
            $this->categories->add($productCategory);
        }

        return $this;
    }

    public function removeCategory(ProductCategory $productCategory): static
    {
        $this->categories->removeElement($productCategory);

        return $this;
    }

    /**
     * @return Collection<int, Platform>
     */
    public function getPlatforms(): Collection
    {
        return $this->platforms;
    }

    public function addPlatform(Platform $platform): static
    {
        if (!$this->platforms->contains($platform)) {
            $this->platforms->add($platform);
        }

        return $this;
    }

    public function removePlatform(Platform $platform): static
    {
        $this->platforms->removeElement($platform);

        return $this;
    }

    public function isActive(): ?bool
    {
        return $this->isActive;
    }

    public function setIsActive(bool $state): static
    {
        $this->isActive = $state;

        return $this;
    }

    public function getBrandAttributeValue(): ?ProductAttributeValue
    {
        return $this->brandAttributeValue;
    }

    public function setBrandAttributeValue(?ProductAttributeValue $brandAttributeValue): static
    {
        $this->brandAttributeValue = $brandAttributeValue;

        return $this;
    }

    public function getType(): ?PlatformForbiddenProductType
    {
        return $this->type;
    }

    public function setType(?PlatformForbiddenProductType $type): static
    {
        $this->type = $type;

        return $this;
    }

    /**
     * @return Collection<int, ProductAttributeValue>
     */
    public function getSizeGroups(): Collection
    {
        return $this->sizeGroups;
    }

    public function addSizeGroup(ProductAttributeValue $sizeGroup): static
    {
        if (!$this->sizeGroups->contains($sizeGroup)) {
            $this->sizeGroups->add($sizeGroup);
        }

        return $this;
    }

    public function removeSizeGroup(ProductAttributeValue $sizeGroup): static
    {
        $this->sizeGroups->removeElement($sizeGroup);

        return $this;
    }
}
