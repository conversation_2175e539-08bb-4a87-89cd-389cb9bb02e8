<?php

namespace Wb3\SharedBundle\Entity;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Wb3\SharedBundle\Entity\Trait\TimestampTrait;
use App\Repository\PlatformPriceRuleRepository;

#[ORM\Entity(repositoryClass: PlatformPriceRuleRepository::class)]
class PlatformPriceRule
{
    use TimestampTrait;


    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    /**
     * @var Collection<int, ProductBrand>
     */
    #[ORM\ManyToMany(targetEntity: ProductBrand::class, inversedBy: 'brands')]
    #[ORM\JoinColumn(name: "platform_price_rule_id", referencedColumnName: "id")]
    private Collection $brands;

    #[ORM\ManyToOne(targetEntity: Platform::class, inversedBy: 'platformPriceRules')]
    #[ORM\JoinColumn(nullable: true)]
    private ?Platform $platform = null;

    #[ORM\ManyToOne(inversedBy: 'platformPriceRules')]
    #[ORM\JoinColumn(nullable: false)]
    private ?PlatformPriceRuleType $type = null;

    #[ORM\Column]
    private float $value;

    #[ORM\Column(options: ['default' => true])]
    private bool $isActive;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $description = null;

    #[ORM\ManyToOne(targetEntity: PlatformPriceRuleScope::class, inversedBy: 'platformPriceRules')]
    #[ORM\JoinColumn(nullable: false)]
    private ?PlatformPriceRuleScope $scope = null;

    #[ORM\Column]
    private ?float $minPrice = null;

    #[ORM\Column]
    private ?float $maxPrice = null;

    public function __construct()
    {
        $this->brands = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    /**
     * @return Collection<int, ProductAttributeValue>
     */
    public function getBrands(): Collection
    {
        return $this->brands;
    }

    public function setBrands(Collection $brands): self
    {
        $this->brands = $brands;

        return $this;
    }

    public function addProductAttributeValue(ProductAttributeValue $productAttributeValue): static
    {
        if (!$this->brands->contains($productAttributeValue)) {
            $this->brands->add($productAttributeValue);
        }

        return $this;
    }

    public function removeProductAttributeValue(ProductAttributeValue $productAttributeValue): static
    {
        $this->brands->removeElement($productAttributeValue);

        return $this;
    }

    public function getPlatform(): ?Platform
    {
        return $this->platform;
    }

    public function setPlatform(?Platform $platform): static
    {
        $this->platform = $platform;

        return $this;
    }

    public function getType(): ?PlatformPriceRuleType
    {
        return $this->type;
    }

    public function setType(PlatformPriceRuleType $type): static
    {
        $this->type = $type;

        return $this;
    }

    public function getValue(): float
    {
        return $this->value;
    }

    public function setValue(float $value): static
    {
        $this->value = $value;

        return $this;
    }

    public function isActive(): bool
    {
        return $this->isActive;
    }

    public function setActive(bool $isActive): static
    {
        $this->isActive = $isActive;

        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): static
    {
        $this->description = $description;

        return $this;
    }

    public function getScope(): ?PlatformPriceRuleScope
    {
        return $this->scope;
    }

    public function setScope(PlatformPriceRuleScope $scope): static
    {
        $this->scope = $scope;

        return $this;
    }

    public function getMinPrice(): ?float
    {
        return $this->minPrice;
    }

    public function setMinPrice(float $minPrice): static
    {
        $this->minPrice = $minPrice;

        return $this;
    }

    public function getMaxPrice(): ?float
    {
        return $this->maxPrice;
    }

    public function setMaxPrice(float $maxPrice): static
    {
        $this->maxPrice = $maxPrice;

        return $this;
    }
}