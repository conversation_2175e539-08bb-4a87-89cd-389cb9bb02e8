<?php

namespace Wb3\SharedBundle\Entity;

use Doctrine\ORM\Mapping as ORM;
use Wb3\SharedBundle\Entity\Trait\TimestampTrait;
use Wb3\SharedBundle\Repository\PlatformMatchProductVariantRepository;

#[ORM\Entity(repositoryClass: PlatformMatchVariantRepository::class)]
class PlatformMatchVariant
{
    use TimestampTrait;
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\ManyToOne(inversedBy: 'platformMatchProductVariants')]
    #[ORM\JoinColumn(nullable: false)]
    private ?Platform $platform = null;

    #[ORM\ManyToOne(inversedBy: 'platformMatchProductVariants')]
    #[ORM\JoinColumn(nullable: false)]
    private ?ProductVariant $variant = null;

    #[ORM\ManyToOne(inversedBy: 'platformMatchProductVariants')]
    #[ORM\JoinColumn(nullable: false)]
    private ?PlatformMatchCategory $category = null;

    #[ORM\ManyToOne(inversedBy: 'platformMatchProductVariants')]
    #[ORM\JoinColumn(nullable: false)]
    private ?PlatformMatchBrand $brand = null;

    #[ORM\Column]
    private array $attributes = [];


    public function getId(): ?int
    {
        return $this->id;
    }

    public function getPlatform(): ?Platform
    {
        return $this->platform;
    }

    public function setPlatform(?Platform $platform): static
    {
        $this->platform = $platform;

        return $this;
    }

    public function getVariant(): ?variant
    {
        return $this->variant;
    }

    public function setVariant(?ProductVariant $variant): static
    {
        $this->variant = $variant;

        return $this;
    }

    public function getCategory(): ?PlatformMatchCategory
    {
        return $this->category;
    }

    public function setCategory(?PlatformMatchCategory $category): static
    {
        $this->category = $category;

        return $this;
    }

    public function getBrand(): ?PlatformMatchBrand
    {
        return $this->brand;
    }

    public function setBrand(?PlatformMatchBrand $brand): static
    {
        $this->brand = $brand;

        return $this;
    }
    public function getAttributes(): array
    {
        return $this->attributes;
    }

    public function setAttributes(array $attributes): static
    {
        $this->attributes = $attributes;

        return $this;
    }

}
