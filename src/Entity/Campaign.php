<?php

namespace Wb3\SharedBundle\Entity;

use App\Repository\CampaignItemRepository;
use App\Repository\CampaignRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Wb3\SharedBundle\Entity\Trait\TimestampTrait;

#[ORM\Entity(repositoryClass: CampaignRepository::class)]
class Campaign
{
    use TimestampTrait;
    const ID_TYPE_PLATFORM = 1;
    const ID_TYPE_VENDOR = 2;
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $description = null;

    #[ORM\ManyToOne(cascade: ['persist', 'remove'])]
    #[ORM\JoinColumn(nullable: false)]
    private ?CampaignScope $scope = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    private ?\DateTimeInterface $startDate = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    private ?\DateTimeInterface $endDate = null;

    #[ORM\Column(length: 255)]
    private ?string $name = null;

    #[ORM\ManyToOne(cascade: ['persist', 'remove'])]
    #[ORM\JoinColumn(nullable: false)]
    private ?CampaignType $type = null;

    #[ORM\ManyToOne(cascade: ['persist', 'remove'])]
    #[ORM\JoinColumn(nullable: false)]
    private ?CampaignSpecial $special = null;

    /**
     * @var Collection<int, CampaignItem>
     */
    #[ORM\OneToMany(targetEntity: CampaignItem::class, mappedBy: 'campaign')]
    private Collection $items;

    /**
     * @var Collection<int, Platform>
     */
    #[ORM\ManyToMany(targetEntity: Platform::class, inversedBy: 'campaigns')]
    #[ORM\JoinTable(name: 'campaign_platform')]
    private Collection $platforms;

    public function __construct()
    {
        $this->platforms = new ArrayCollection();
        $this->items = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): static
    {
        $this->description = $description;

        return $this;
    }

    public function getScope(): ?CampaignScope
    {
        return $this->scope;
    }

    public function setScope(CampaignScope $scope): static
    {
        $this->scope = $scope;

        return $this;
    }

    public function getStartDate(): ?\DateTimeInterface
    {
        return $this->startDate;
    }

    public function setStartDate(\DateTimeInterface $startDate): static
    {
        $this->startDate = $startDate;

        return $this;
    }

    public function getEndDate(): ?\DateTimeInterface
    {
        return $this->endDate;
    }

    public function setEndDate(\DateTimeInterface $endDate): static
    {
        $this->endDate = $endDate;

        return $this;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): static
    {
        $this->name = $name;

        return $this;
    }

    public function getType(): ?CampaignType
    {
        return $this->type;
    }

    public function setType(CampaignType $type): static
    {
        $this->type = $type;

        return $this;
    }

    public function getSpecial(): ?CampaignSpecial
    {
        return $this->special;
    }

    public function setSpecial(CampaignSpecial $special): static
    {
        $this->special = $special;

        return $this;
    }

    /**
     * @return Collection<int, CampaignItem>
     */
    public function getItems(): Collection
    {
        return $this->items;
    }

    public function addItem(CampaignItem $item): static
    {
        if (!$this->items->contains($item)) {
            $this->items->add($item);
            $item->setCampaign($this);
        }

        return $this;
    }

    public function removeItem(CampaignItem $item): static
    {
        if ($this->items->removeElement($item)) {
            // set the owning side to null (unless already changed)
            if ($item->getCampaign() === $this) {
                $item->setCampaign(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, Platform>
     */
    public function getPlatforms(): Collection
    {
        return $this->platforms;
    }

    public function addPlatform(Platform $platform): static
    {
        if (!$this->platforms->contains($platform)) {
            $this->platforms->add($platform);
        }

        return $this;
    }

    public function removePlatform(Platform $platform): static
    {
        $this->platforms->removeElement($platform);

        return $this;
    }
}
