<?php

namespace Wb3\SharedBundle\Entity;

use App\Repository\ProductAttributeRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Doctrine\ORM\Mapping\Cache;
use Wb3\SharedBundle\Entity\Trait\TimestampTrait;

#[ORM\Entity(repositoryClass: ProductAttributeRepository::class)]
#[Cache(usage: 'NONSTRICT_READ_WRITE', region: 'write_rare')]
class ProductAttribute
{
    CONST ATTRIBUTE_SIZE_GROUP_ID = 11;
    CONST ATTRIBUTE_BRAND_ID = 1;
    CONST ATTRIBUTE_SEASON_ID = 8;
    CONST ATTRIBUTE_COLOR_ID = 35;

    CONST ATTRIBUTE_KDV_ID = 9;
    CONST ATTRIBUTE_BIRIM_ID = 13;

    const ID_GTIP =17;
    const ID_MENSEI=33;
    CONST ATTRIBUTE_COLOR_NAME = 'Renk';
    use TimestampTrait;

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(length: 255)]
    private ?string $name = null;

    #[ORM\OneToMany(targetEntity: ProductAttributeValue::class, mappedBy: 'productAttribute')]
    private Collection $values;

    #[ORM\Column(length: 255,nullable: true)]
    private ?string $korgunCode = null;

    #[ORM\Column(type: 'integer',nullable: true)]
    private ?int $sort = null;
    public function __construct()
    {
        $this->values = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getKorgunCode(): ?string
    {
        return $this->korgunCode;
    }

    public function setKorgunCode(?string $korgunCode): ProductAttribute
    {
        $this->korgunCode = $korgunCode;
        return $this;
    }


    /**
     * @return Collection<int, ProductAttributeValue>
     */
    public function getValues(): Collection
    {
        return $this->values;
    }

    public function addProductAttributeValue(ProductAttributeValue $productAttributeValue): self
    {
        if (!$this->values->contains($productAttributeValue)) {
            $this->values->add($productAttributeValue);
            $productAttributeValue->setAttribute($this);
        }

        return $this;
    }

    public function removeProductAttributeValue(ProductAttributeValue $productAttributeValue): self
    {
        if ($this->values->removeElement($productAttributeValue)) {
            // set the owning side to null (unless already changed)
            if ($productAttributeValue->getAttribute() === $this) {
                $productAttributeValue->setAttribute(null);
            }
        }

        return $this;
    }

    public function getSort(): ?int
    {
        return $this->sort;
    }

    public function setSort(int $sort): self
    {
        $this->sort = $sort;

        return $this;
    }
    public function __toString(): string
    {
        return $this->getName();
    }
}
