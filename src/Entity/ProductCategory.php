<?php

namespace Wb3\SharedBundle\Entity;

use App\Repository\ProductCategoryRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Wb3\SharedBundle\Entity\Trait\TimestampTrait;

#[ORM\Entity(repositoryClass: ProductCategoryRepository::class)]
#[ORM\Index(fields: ['parent'])]
class ProductCategory
{
    CONST ID_DEFAULT = 161;

    use TimestampTrait;

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\ManyToOne(targetEntity: self::class, cascade: ['persist', 'remove'], inversedBy: 'parent')]
    private ?self $parent = null;

    #[ORM\Column(length: 10)]
    private ?string $ayonCode = null;

    #[ORM\Column(length: 255)]
    private ?string $name = null;

    #[ORM\Column]
    private ?int $level = null;

    #[ORM\Column]
    private ?bool $isLeaf = null;

    #[ORM\Column]
    private ?int $sort = null;
    #[ORM\Column(nullable: true)]
    private ?string $path = null;

    /**
     * @var Collection<int, PlatformForbiddenProduct>
     */
    #[ORM\ManyToMany(targetEntity: PlatformForbiddenProduct::class, mappedBy: 'categories')]
    private Collection $platformForbiddenProducts;

    public function __construct()
    {
        $this->platformForbiddenProducts = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getParent(): ?self
    {
        return $this->parent;
    }

    public function setParent(?self $parent): static
    {
        $this->parent = $parent;

        return $this;
    }

    public function getAyonCode(): ?string
    {
        return $this->ayonCode;
    }

    public function setAyonCode(string $ayonCode): static
    {
        $this->ayonCode = $ayonCode;

        return $this;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): static
    {
        $this->name = $name;

        return $this;
    }

    public function getLevel(): ?int
    {
        return $this->level;
    }

    public function setLevel(int $level): static
    {
        $this->level = $level;

        return $this;
    }

    public function isIsLeaf(): ?bool
    {
        return $this->isLeaf;
    }

    public function setIsLeaf(bool $isLeaf): static
    {
        $this->isLeaf = $isLeaf;

        return $this;
    }

    public function getSort(): ?int
    {
        return $this->sort;
    }

    public function setSort(int $sort): static
    {
        $this->sort = $sort;

        return $this;
    }

    public function getPath(): ?string
    {
        return $this->path;
    }

    public function setPath(?string $path): ProductCategory
    {
        $this->path = $path;
        return $this;
    }

    /**
     * @return Collection<int, PlatformForbiddenProduct>
     */
    public function getPlatformForbiddenProducts(): Collection
    {
        return $this->platformForbiddenProducts;
    }

    public function addPlatformForbiddenProduct(PlatformForbiddenProduct $platformForbiddenProduct): static
    {
        if (!$this->platformForbiddenProducts->contains($platformForbiddenProduct)) {
            $this->platformForbiddenProducts->add($platformForbiddenProduct);
            $platformForbiddenProduct->addCategory($this);
        }

        return $this;
    }

    public function removePlatformForbiddenProduct(PlatformForbiddenProduct $platformForbiddenProduct): static
    {
        if ($this->platformForbiddenProducts->removeElement($platformForbiddenProduct)) {
            $platformForbiddenProduct->removeCategory($this);
        }

        return $this;
    }

    public function __toString(): string
    {
        return $this->getPath();
    }

}
