<?php

namespace Wb3\SharedBundle\Entity;

use DateTimeImmutable;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Wb3\SharedBundle\Entity\Trait\TimestampTrait;
use App\Repository\TaskRepository;
#[ORM\HasLifecycleCallbacks]
#[ORM\Entity(repositoryClass: TaskRepository::class)]
class Task
{

    const STATE_DRAFT = 'DRAFT';
    const STATE_IN_PROGRESS = 'IN_PROGRESS';
    const STATE_COMPLETED = 'COMPLETED';
    const STATE_FAILED = 'FAILED';

    const ALL_STATES = [self::STATE_DRAFT,self::STATE_IN_PROGRESS,self::STATE_COMPLETED,self::STATE_FAILED];

    CONST SEARCH_EXCLUDE_FIELDS = 'request,response,object';

    use TimestampTrait;
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\ManyToOne(inversedBy: 'tasks')]
    private ?TaskType $type = null;

    #[ORM\Column]
    private ?int $userId = null;

    #[ORM\Column(type: Types::TEXT)]
    private ?string $request = null;

    #[ORM\Column(options: ['default'=>0] )]
    private ?int $percent = null;

    #[ORM\ManyToOne(inversedBy: 'tasks')]
    private ?Platform $platform = null;

    #[ORM\Column(type: Types::TEXT,nullable: true)]
    private ?string $response = null;

    #[ORM\Column(nullable: true)]
    private ?int $itemCount = null;

    #[ORM\Column(nullable: true)]
    private ?array $extra = null;

    #[ORM\Column(nullable: true)]
    private ?DateTimeImmutable $completedAt = null;

    #[ORM\Column(nullable: true)]
    private ?float $executeTime = null;

    #[ORM\Column(length: 255)]
    private ?string $state = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getType(): ?TaskType
    {
        return $this->type;
    }

    public function setType(?TaskType $type): static
    {
        $this->type = $type;

        return $this;
    }

    public function getUserId(): ?int
    {
        return $this->userId;
    }

    public function setUserId(int $userId): static
    {
        $this->userId = $userId;

        return $this;
    }

    public function getRequest(): ?string
    {
        return $this->request;
    }

    public function setRequest(string $request): static
    {
        $this->request = $request;

        return $this;
    }

    public function getPercent(): ?int
    {
        return $this->percent;
    }

    public function setPercent(int $percent): static
    {
        $this->percent = $percent;

        return $this;
    }

    public function getPlatform(): ?Platform
    {
        return $this->platform;
    }

    public function setPlatform(?Platform $platform): static
    {
        $this->platform = $platform;

        return $this;
    }

    public function getResponse(): ?string
    {
        return $this->response;
    }

    public function setResponse(string $response): static
    {
        $this->response = $response;

        return $this;
    }

    public function getItemCount(): ?int
    {
        return $this->itemCount;
    }

    public function setItemCount(?int $itemCount): static
    {
        $this->itemCount = $itemCount;

        return $this;
    }

    public function getExtra(): ?array
    {
        return $this->extra;
    }

    public function setExtra(?array $extra): static
    {
        $this->extra = $extra;

        return $this;
    }

    public function getCompletedAt(): ?DateTimeImmutable
    {
        return $this->completedAt;
    }

    public function setCompletedAt(?DateTimeImmutable $completedAt): static
    {
        $this->completedAt = $completedAt;

        return $this;
    }

    public function getExecuteTime(): ?float
    {
        return $this->executeTime;
    }

    public function setExecuteTime(?float $executeTime): static
    {
        $this->executeTime = $executeTime;

        return $this;
    }

    public function getState(): ?string
    {
        return $this->state;
    }

    public function setState(string $state): static
    {
        $this->state = $state;

        return $this;
    }
}
