<?php

namespace Wb3\SharedBundle\Entity;

use App\Repository\ProductRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Doctrine\ORM\Mapping\Cache;
use Wb3\SharedBundle\Entity\Trait\TimestampTrait;

/**
 * places:
 * draft:
 * metadata:
 * description: 'Yeni geldi'
 * created:
 * metadata:
 * description: 'Platform eşleştirmesi eşleştirme yapıldı.'
 * approved:
 * metadata:
 * description: 'Onaylandı. Kategori yoneticisi platformda onayladı.'
 * uploaded:
 * metadata:
 * description: 'Ürün platforma yüklendi'
 * platform_will_review:
 * metadata:
 * description: 'Platform tarafından incelenecek ürünler'
 * waiting_vendor_approve:
 * metadata:
 * description: 'Ürün için satıcı tarafından onay bekliyor'
 * waiting_missing_info:
 * metadata:
 * description: 'Ürün için eksik bilgi bekleniyor'
 * vendor_declined:
 * metadata:
 * description: 'Satıcı tarafından reddedildi'
 * platform_declined:
 * metadata:
 * description: 'Platform tarafından reddedildi'
 * published:
 * metadata:
 * description: 'Yayınlandı.'
 * closed:
 * metadata:
 * description: 'Kapatıldı.'
 * out_of_stock:
 * metadata:
 * description: 'Stokta yok.'
 */
#[ORM\Entity(repositoryClass: ProductRepository::class)]
#[ORM\HasLifecycleCallbacks]
#[ORM\UniqueConstraint(name: 'sku_color_unique', columns: ['sku_color'])]
#[Cache(usage: 'NONSTRICT_READ_WRITE', region: 'write_rare')]
class Product extends BaseEntity
{
    const STATE_DRAFT = 'draft';
    const STATE_WAITING_APPROVE = 'waitingApprove';
    const STATE_APPROVED = 'approved';
    const STATE_NOT_APPROVED = 'notApproved';
    const STATE_PUBLISHED = 'published';

    use TimestampTrait;

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    protected ?int $id = null;

    #[ORM\Column(length: 90)]
    protected string $title;


    #[ORM\Column(length: 50)]
    protected string $sku;

    #[ORM\Column(length: 50)]
    protected string $skuColor;

    #[ORM\Column(length: 50)]
    protected string $state = 'draft';

    #[ORM\Column(length: 100, nullable: true)]
    protected ?string $transition = null;
    #[ORM\OneToMany(targetEntity: ProductVariant::class, mappedBy: 'product',cascade: ['persist','remove'])]
    #[Cache(usage: 'NONSTRICT_READ_WRITE', region: 'write_rare')]
    #[ORM\OrderBy(["stockCode" => "ASC"])]
    protected Collection $variants;

    #[ORM\OneToMany(targetEntity: ProductImage::class, mappedBy: 'product',cascade: ['persist','remove'])]
    #[Cache(usage: 'NONSTRICT_READ_WRITE', region: 'write_rare')]
    protected Collection $images;

    #[ORM\OneToMany(targetEntity: ProductPrice::class, mappedBy: 'product',cascade: ['persist','remove'])]
    #[Cache(usage: 'NONSTRICT_READ_WRITE', region: 'write_rare')]
    protected Collection $prices;
    #[ORM\OneToMany(targetEntity: ProductAttributeValueProduct::class, mappedBy: 'product',cascade: ['persist','remove'])]
    #[Cache(usage: 'NONSTRICT_READ_WRITE', region: 'write_rare')]
    protected Collection $attributes;

    #[ORM\Column(length: 64)]
    protected ?string $hash = null;

    /**
     * @var Collection<int, ProductSmallChange>
     */
    #[ORM\OneToMany(targetEntity: ProductSmallChange::class, mappedBy: 'product')]
    private Collection $smallChanges;

    /**
     * @var Collection<int, PlatformProduct>
     */
    #[ORM\OneToMany(targetEntity: PlatformProduct::class, mappedBy: 'product')]
    private Collection $platformProducts;

    #[ORM\ManyToOne(inversedBy: 'products')]
    #[Cache(usage: 'NONSTRICT_READ_WRITE', region: 'write_rare')]
    #[ORM\JoinColumn(name: 'brand_id', referencedColumnName: 'id', nullable: true, onDelete: 'SET NULL')]
    private ?ProductBrand $brand = null;

    #[ORM\ManyToOne(targetEntity: ProductCategory::class)]
    //#[Cache(usage: 'NONSTRICT_READ_WRITE', region: 'write_rare')]
    private ?ProductCategory $category = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $titleOriginal = null;

    #[ORM\Column(type: 'integer', options: ['default' => 0])]
    private ?int $quantity = 0;

    #[ORM\Column(length: 20, options: ['default' => 'OFFLINE'])]
    private ?string $korgunState = null;

    public function __construct()
    {
        $this->variants = new ArrayCollection();
        $this->images = new ArrayCollection();
        $this->prices = new ArrayCollection();
        $this->smallChanges = new ArrayCollection();
        $this->platformProducts = new ArrayCollection();
        $this->attributes = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getTitle(): ?string
    {
        return $this->title;
    }

    public function setTitle(string $title): static
    {
        $this->title = $title;

        return $this;
    }

    public function getSkuColor(): ?string
    {
        return $this->skuColor;
    }

    public function setSkuColor(string $skuColor): static
    {
        $this->skuColor = $skuColor;

        return $this;
    }

    public function getSku(): string
    {
        return $this->sku;
    }

    public function setSku(string $sku): Product
    {
        $this->sku = $sku;
        return $this;
    }

    public function getColor() : string
    {
        return (int) explode(' - ', $this->skuColor)[1];

    }

    public function getState(): ?string
    {
        return $this->state;
    }

    public function setState(string $state): static
    {
        $this->state = $state;

        return $this;
    }

    public function getTransition(): ?string
    {
        return $this->transition;
    }

    public function setTransition(?string $transition): static
    {
        $this->transition = $transition;

        return $this;
    }

    /**
     * @return Collection<int, ProductVariant>
     */
    public function getVariants(): Collection
    {
        return $this->variants;
    }

    public function addVariant(ProductVariant $variant): static
    {
        if (!$this->variants->contains($variant)) {
            $this->variants->add($variant);
            $variant->setProduct($this);
        }

        return $this;
    }

    public function removeVariant(ProductVariant $variant): static
    {
        if ($this->variants->removeElement($variant)) {
            // set the owning side to null (unless already changed)
            if ($variant->getProduct() === $this) {
                $variant->setProduct(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, ProductImage>
     */
    public function getImages(): Collection
    {
        return $this->images;
    }

    public function addImage(ProductImage $image): static
    {
        if (!$this->images->contains($image)) {
            $this->images->add($image);
            $image->setProduct($this);
        }

        return $this;
    }

    public function removeImage(ProductImage $image): static
    {
        if ($this->images->removeElement($image)) {
            // set the owning side to null (unless already changed)
            if ($image->getProduct() === $this) {
                $image->setProduct(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, ProductPrice>
     */
    public function getPrices(): Collection
    {
        return $this->prices;
    }

    public function addPrice(ProductPrice $price): static
    {
        if (!$this->prices->contains($price)) {
            $this->prices->add($price);
            $price->setProduct($this);
        }

        return $this;
    }

    public function removePrice(ProductPrice $price): static
    {
        if ($this->prices->removeElement($price)) {
            // set the owning side to null (unless already changed)
            if ($price->getProduct() === $this) {
                $price->setProduct(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, ProductAttributeValueProduct>
     */
    public function getAttributes(): Collection
    {
        return $this->attributes;
    }

    public function addAttributeValue(ProductAttributeValueProduct $valueProduct): static
    {
        if (!$this->attributes->contains($valueProduct)) {
            $this->attributes->add($valueProduct);
            $valueProduct->setProduct($this);
        }

        return $this;
    }

    public function removeAttributeValue(ProductAttributeValueProduct $valueProduct): static
    {
        if ($this->prices->removeElement($valueProduct)) {
            // set the owning side to null (unless already changed)
            if ($valueProduct->getProduct() === $this) {
                $valueProduct->setProduct(null);
            }
        }

        return $this;
    }
    public function getCategory(): ?ProductCategory
    {
        return $this->category;
    }

    public function setCategory($category): static
    {
        $this->category = $category;
        return $this;
    }

    public function getHash(): ?string
    {
        return $this->hash;
    }

    public function setHash(?string $hash) : self
    {
        $this->hash = $hash;

        return $this;
    }

    /**
     * @return Collection<int, ProductSmallChange>
     */
    public function getSmallChanges(): Collection
    {
        return $this->smallChanges;
    }

    public function addSmallChange(ProductSmallChange $smallChange): static
    {
        if (!$this->smallChanges->contains($smallChange)) {
            $this->smallChanges->add($smallChange);
            $smallChange->setProduct($this);
        }

        return $this;
    }

    public function removeSmallChange(ProductSmallChange $smallChange): static
    {
        if ($this->smallChanges->removeElement($smallChange)) {
            // set the owning side to null (unless already changed)
            if ($smallChange->getProduct() === $this) {
                $smallChange->setProduct(null);
            }
        }

        return $this;
    }

    public function __toString(): string
    {
        return $this->getSkuColor().", ". $this->getTitle();
    }

    /**
     * @return Collection<int, PlatformProduct>
     */
    public function getPlatformProducts(): Collection
    {
        return $this->platformProducts;
    }

    public function addPlatformProduct(PlatformProduct $platformProduct): static
    {
        if (!$this->platformProducts->contains($platformProduct)) {
            $this->platformProducts->add($platformProduct);
            $platformProduct->setProduct($this);
        }

        return $this;
    }

    public function removePlatformProduct(PlatformProduct $platformProduct): static
    {
        if ($this->platformProducts->removeElement($platformProduct)) {
            // set the owning side to null (unless already changed)
            if ($platformProduct->getProduct() === $this) {
                $platformProduct->setProduct(null);
            }
        }

        return $this;
    }

    public function getBrand(): ?ProductBrand
    {
        return $this->brand;
    }

    public function setBrand(?ProductBrand $brand): static
    {
        $this->brand = $brand;

        return $this;
    }

    public function getTitleOriginal(): ?string
    {
        return $this->titleOriginal;
    }

    public function setTitleOriginal(string $titleOriginal): static
    {
        $this->titleOriginal = $titleOriginal;

        return $this;
    }

    public function calculateQuantity(): int
    {
        $qty=0;
        /** @var ProductVariant $variant */
        foreach($this->variants AS $variant) {
            $qty += $variant->calculateQuantity();
        }

        return $qty;
    }

    public function getQuantity(): int
    {
        return $this->quantity;
    }



    public function setQuantity(int $quantity): static
    {
        $this->quantity = $quantity;

        return $this;
    }

    public function getKorgunState(): ?string
    {
        return $this->korgunState;
    }

    public function setKorgunState(string $korgunState): static
    {
        $this->korgunState = $korgunState;

        return $this;
    }

    /** Verilen özellik id sine göre özellik getirir.
     *
     * @param int $attributeId
     *
     * @return ProductAttributeValueProduct|bool
     */
    public function getAttributeByAttributeId(int $attributeId) : ProductAttributeValueProduct | bool
    {
        return $this->attributes->filter(function(ProductAttributeValueProduct $attributeValueProduct) use ($attributeId) {
            return $attributeValueProduct->getAttribute()->getId() === $attributeId;
        })->first();
    }
}
