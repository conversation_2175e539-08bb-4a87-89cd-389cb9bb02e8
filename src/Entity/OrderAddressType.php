<?php

namespace Wb3\SharedBundle\Entity;

use App\Repository\OrderAddressTypeRepository;
use Doctrine\ORM\Mapping as ORM;
use Wb3\SharedBundle\Entity\Trait\TimestampTrait;

#[ORM\Entity(repositoryClass: OrderAddressTypeRepository::class)]
class OrderAddressType
{
    use TimestampTrait;
    CONST ID_INVOICE_ADDRESS = 1;
    CONST ID_CARGO_ADDRESS = 2;
    CONST ID_INVOICE_ADDRESS_MODIFIED =3;
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(length: 255)]
    private ?string $name = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): static
    {
        $this->name = $name;

        return $this;
    }
}
