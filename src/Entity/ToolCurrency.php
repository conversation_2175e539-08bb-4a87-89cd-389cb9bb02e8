<?php

namespace Wb3\SharedBundle\Entity;

use App\Repository\ToolCurrencyRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Wb3\SharedBundle\Entity\Trait\TimestampTrait;
#[ORM\Entity(repositoryClass: ToolCurrencyRepository::class)]
class ToolCurrency
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(length: 10)]
    private ?string $currencyCode = null;

    #[ORM\Column(type: Types::DECIMAL, precision: 15, scale: 8)]
    private ?string $currencyRate = null;

    #[ORM\Column(type: Types::DECIMAL, precision: 15, scale: 8, nullable: true)]
    private ?string $modifyRate = null;

    #[ORM\Column(length: 255)]
    private ?string $currenyName = null;

    use TimestampTrait;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getCurrencyCode(): ?string
    {
        return $this->currencyCode;
    }

    public function setCurrencyCode(string $currencyCode): static
    {
        $this->currencyCode = $currencyCode;

        return $this;
    }

    public function getCurrencyRate(): ?string
    {
        return $this->currencyRate;
    }

    public function setCurrencyRate(string $currencyRate): static
    {
        $this->currencyRate = $currencyRate;

        return $this;
    }

    public function getModifyRate(): ?string
    {
        return $this->modifyRate;
    }

    public function setModifyRate(?string $modifyRate): static
    {
        $this->modifyRate = $modifyRate;

        return $this;
    }

    public function getCurrenyName(): ?string
    {
        return $this->currenyName;
    }

    public function setCurrenyName(string $currenyName): static
    {
        $this->currenyName = $currenyName;

        return $this;
    }
}
