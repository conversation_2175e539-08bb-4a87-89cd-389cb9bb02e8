<?php

namespace Wb3\SharedBundle\Entity;

use DateTimeInterface;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Wb3\SharedBundle\Repository\ToolIysRepository;

#[ORM\Entity(repositoryClass: ToolIysRepository::class)]
class ToolIys
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(length: 100)]
    private ?string $retailer = null;

    #[ORM\Column(length: 150)]
    private ?string $recipient = null;

    #[ORM\Column(length: 20)]
    private ?string $type = null;

    #[ORM\Column(length: 150)]
    private ?string $customerName = null;

    #[ORM\Column(length: 150)]
    private ?string $source = null;

    #[ORM\Column]
    private ?bool $status = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    private ?DateTimeInterface $consentDate = null;

    #[ORM\Column(length: 255)]
    private ?string $activationCode = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    private ?DateTimeInterface $activationSentDate = null;

    #[ORM\Column(length: 150)]
    private ?string $recipientType = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getRetailer(): ?string
    {
        return $this->retailer;
    }

    public function setRetailer(string $retailer): static
    {
        $this->retailer = $retailer;

        return $this;
    }

    public function getRecipient(): ?string
    {
        return $this->recipient;
    }

    public function setRecipient(string $recipient): static
    {
        $this->recipient = $recipient;

        return $this;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(string $type): static
    {
        $this->type = $type;

        return $this;
    }

    public function getCustomerName(): ?string
    {
        return $this->customerName;
    }

    public function setCustomerName(string $customerName): static
    {
        $this->customerName = $customerName;

        return $this;
    }

    public function getSource(): ?string
    {
        return $this->source;
    }

    public function setSource(string $source): static
    {
        $this->source = $source;

        return $this;
    }

    public function isStatus(): ?bool
    {
        return $this->status;
    }

    public function setStatus(bool $status): static
    {
        $this->status = $status;

        return $this;
    }

    public function getConsentDate(): ?DateTimeInterface
    {
        return $this->consentDate;
    }

    public function setConsentDate(DateTimeInterface $consentDate): static
    {
        $this->consentDate = $consentDate;

        return $this;
    }

    public function getActivationCode(): ?string
    {
        return $this->activationCode;
    }

    public function setActivationCode(string $activationCode): static
    {
        $this->activationCode = $activationCode;

        return $this;
    }

    public function getActivationSentDate(): ?DateTimeInterface
    {
        return $this->activationSentDate;
    }

    public function setActivationSentDate(DateTimeInterface $activationSentDate): static
    {
        $this->activationSentDate = $activationSentDate;

        return $this;
    }

    public function getRecipientType(): ?string
    {
        return $this->recipientType;
    }

    public function setRecipientType(string $recipientType): static
    {
        $this->recipientType = $recipientType;

        return $this;
    }
}
