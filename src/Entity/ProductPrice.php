<?php

namespace Wb3\SharedBundle\Entity;

use Doctrine\ORM\Mapping as ORM;
use App\Repository\ProductPriceRepository;
use Doctrine\ORM\Mapping\Cache;
use Wb3\SharedBundle\Entity\Trait\TimestampTrait;

#[ORM\Entity(repositoryClass: ProductPriceRepository::class)]
#[ORM\Index(fields:['product','type'])]
#[Cache(usage: 'NONSTRICT_READ_WRITE', region: 'write_rare')]
class ProductPrice
{
const PRODUCT_PRICE_INSTALLMENT_ID = 1;
const PRODUCT_PRICE_CASH_ORIGINAL_ID = 3;
const PRODUCT_PRICE_CASH_DISCOUNTED_ID = 2;
const PRODUCT_PRICE_INTERNET_ID = 3;

    use TimestampTrait;
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\ManyToOne(inversedBy: 'prices')]
    #[ORM\JoinColumn(nullable: false)]
    private Product $product;

    #[ORM\Column]
    private float $price;

    #[ORM\Column(length: 64)]
    private string $hash;

    #[ORM\ManyToOne(inversedBy: 'productPrices')]
    #[ORM\JoinColumn(nullable: false)]
    private ?ProductPriceType $type = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getProduct(): ?Product
    {
        return $this->product;
    }

    public function setProduct(?Product $product): static
    {
        $this->product = $product;

        return $this;
    }

    public function getPrice(): ?float
    {
        return $this->price;
    }

    public function setPrice(float $price): static
    {
        $this->price = $price;

        return $this;
    }

    public function getType(): ?ProductPriceType
    {
        return $this->type;
    }

    public function setType(?ProductPriceType $ProductPriceType): static
    {
        $this->type = $ProductPriceType;

        return $this;
    }
    public function getHash(): ?string
    {
        return $this->hash;
    }

    public function setHash(string $hash) : self
    {
        $this->hash = $hash;
        return $this;
    }

}
