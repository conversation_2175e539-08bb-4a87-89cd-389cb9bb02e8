<?php

namespace Wb3\SharedBundle\Entity;

use App\Repository\OrderTrackRepository;
use Doctrine\ORM\Mapping as ORM;
use Wb3\SharedBundle\Entity\Trait\TimestampTrait;

#[ORM\Entity(repositoryClass: OrderTrackRepository::class)]
#[ORM\Index(fields:['order'])]
class OrderTrack
{
    use TimestampTrait;
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\OneToOne(inversedBy: 'track', cascade: ['persist', 'remove'])]
    #[ORM\JoinColumn(nullable: false)]
    private ?Order $order = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $trackNumber = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $trackUrl = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getOrder(): ?Order
    {
        return $this->order;
    }

    public function setOrder(Order $order): static
    {
        $this->order = $order;

        return $this;
    }

    public function getTrackNumber(): ?string
    {
        return $this->trackNumber;
    }

    public function setTrackNumber(?string $trackNumber): static
    {
        $this->trackNumber = $trackNumber;

        return $this;
    }

    public function getTrackUrl(): ?string
    {
        return $this->trackUrl;
    }

    public function settrackUrl(?string $trackUrl): static
    {
        $this->trackUrl = $trackUrl;

        return $this;
    }
}
