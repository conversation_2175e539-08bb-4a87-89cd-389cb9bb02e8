<?php

namespace Wb3\SharedBundle\Entity;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use App\Repository\PlatformBufferRepository;
use Symfony\Component\Validator\Constraints as Assert;
use Wb3\SharedBundle\Entity\Trait\TimestampTrait;

#[ORM\Entity(repositoryClass: PlatformBufferRepository::class)]
class PlatformBuffer
{
    use TimestampTrait;
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column]
    #[Assert\NotBlank]
    #[Assert\Type('integer')]
    private int $minQuantity;

    /**
     * @var Collection<int, Platform>
     */
    #[ORM\ManyToMany(targetEntity: Platform::class, inversedBy: 'platformBuffers')]
    #[Assert\NotBlank]
    private Collection $platforms;

    /**
     * @var Collection<int, PlatformBufferType>
     */
    #[ORM\ManyToMany(targetEntity: PlatformBufferType::class, inversedBy: 'platformBuffers')]
    #[Assert\NotBlank]
    private Collection $types;

    #[ORM\Column(length: 255, nullable: true)]
    #[Assert\Type('string')]
    private ?string $description = null;

    #[ORM\ManyToOne(inversedBy: 'platformBuffers')]
    #[Assert\NotBlank]
    private ProductVariant $productVariant;

    public function __construct()
    {
        $this->platforms = new ArrayCollection();
        $this->types = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getMinQuantity(): int
    {
        return $this->minQuantity;
    }

    public function setMinQuantity(int $minQuantity): static
    {
        $this->minQuantity = $minQuantity;

        return $this;
    }

    /**
     * @return Collection<int, Platform>
     */
    public function getPlatforms(): Collection
    {
        return $this->platforms;
    }

    public function setPlatforms(Collection $platforms): PlatformBuffer
    {
        $this->platforms = $platforms;
        return $this;
    }

    public function addPlatform(Platform $platform): static
    {
        if (!$this->platforms->contains($platform)) {
            $this->platforms->add($platform);
        }

        return $this;
    }

    public function removePlatform(Platform $platform): static
    {
        $this->platforms->removeElement($platform);

        return $this;
    }

    public function getTypes(): Collection
    {
        return $this->types;
    }

    public function setTypes(Collection $types): PlatformBuffer
    {
        $this->types = $types;
        return $this;
    }

    public function addType(PlatformBuffer $platformBufferType): static
    {
        if (!$this->types->contains($platformBufferType)) {
            $this->types->add($platformBufferType);
        }

        return $this;
    }

    public function removeType(PlatformBuffer $platformBufferType): static
    {
        $this->types->removeElement($platformBufferType);

        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): static
    {
        $this->description = $description;

        return $this;
    }

    public function getProductVariant(): ProductVariant
    {
        return $this->productVariant;
    }

    public function setProductVariant(ProductVariant $productVariant): static
    {
        $this->productVariant = $productVariant;

        return $this;
    }
}
