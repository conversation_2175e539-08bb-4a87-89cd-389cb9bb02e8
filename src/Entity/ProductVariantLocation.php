<?php

namespace Wb3\SharedBundle\Entity;

use Doctrine\ORM\Mapping as ORM;
use Doctrine\ORM\Mapping\Cache;
use Wb3\SharedBundle\Entity\Trait\TimestampTrait;
use App\Repository\PlatformProductVariantRepository;

#[ORM\Entity(repositoryClass: PlatformVariantLocationRepository::class)]
#[ORM\HasLifecycleCallbacks]
#[Cache(usage: 'NONSTRICT_READ_WRITE', region: 'write_rare')]
#[ORM\UniqueConstraint(columns: ['variant_id', 'location_id'])]
class ProductVariantLocation
{
    use TimestampTrait;
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\ManyToOne(targetEntity: ProductVariant::class,cascade: ['persist'])]
    private ProductVariant $variant;

    #[ORM\ManyToOne(targetEntity: Location::class)]
    #[ORM\JoinColumn(nullable: false)]
    private Location $location;
    #[ORM\Column(type:'integer')]
    private int $quantity = 0;

    #[ORM\Column(length: 64, nullable: true)]
    private ?string $hash;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getVariant()
    {
        return $this->variant;
    }

    public function setVariant(ProductVariant $variant) : self
    {
        $this->variant = $variant;
        return $this;
    }

    public function getLocation() : Location
    {
        return $this->location;
    }

    public function setLocation($location) : self
    {
        $this->location = $location;
        return $this;
    }

    public function getQuantity(): int
    {
        return $this->quantity;
    }

    public function setQuantity(int $quantity): self
    {
        $this->quantity = $quantity;
        return $this;
    }

    public function getHash(): ?string
    {
        return $this->hash;
    }

    public function setHash(string $hash) : self
    {
        $this->hash = $hash;

        return $this;
    }

}
