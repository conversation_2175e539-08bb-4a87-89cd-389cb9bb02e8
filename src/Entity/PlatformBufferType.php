<?php

namespace Wb3\SharedBundle\Entity;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use App\Repository\PlatformBufferTypeRepository;
use Wb3\SharedBundle\Entity\Trait\TimestampTrait;

#[ORM\Entity(repositoryClass: PlatformBufferTypeRepository::class)]
class PlatformBufferType
{
    use TimestampTrait;
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(length: 255)]
    private ?string $name = null;

    /**
     * @var Collection<int, PlatformBuffer>
     */
    #[ORM\OneToMany(targetEntity: PlatformBuffer::class, mappedBy: 'type')]
    private Collection $buffers;

    public function __construct()
    {
        $this->buffers = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): static
    {
        $this->name = $name;

        return $this;
    }

    /**
     * @return Collection<int, PlatformBuffer>
     */
    public function getBuffers(): Collection
    {
        return $this->buffers;
    }
    public function __toString(): string
    {
        return $this->getName();
    }
}
