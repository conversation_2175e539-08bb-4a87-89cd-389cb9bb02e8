<?php

namespace Wb3\SharedBundle\Entity;

use Doctrine\ORM\Mapping as ORM;
use App\Repository\ProductImageRepository;
use Doctrine\ORM\Mapping\Cache;
use Wb3\SharedBundle\Entity\Trait\TimestampTrait;

#[ORM\Entity(repositoryClass: ProductImageRepository::class)]
#[ORM\UniqueConstraint(columns: ['product_id', 'url'])]
#[Cache(usage: 'NONSTRICT_READ_WRITE', region: 'write_rare')]
class ProductImage
{
    use TimestampTrait;
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\ManyToOne(cascade: ['persist'], inversedBy: 'images')]
    private Product $product;

    #[ORM\Column(length: 255)]
    private string $url;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getProduct(): ?Product
    {
        return $this->product;
    }

    public function setProduct(?Product $product): static
    {
        $this->product = $product;

        return $this;
    }

    public function getUrl(): ?string
    {
        return $this->url;
    }

    public function getThumbUrl(): ?string
    {
        $pathInfo = pathinfo($this->getUrl());
        $dirNameArray = explode("/",$pathInfo['dirname']);
        $newPath = implode("/",array_splice($dirNameArray,0,-1)) . '/kucuk/' . $pathInfo['basename'];

        return $newPath;
    }

    public function setUrl(string $url): static
    {
        $this->url = $url;

        return $this;
    }

}
