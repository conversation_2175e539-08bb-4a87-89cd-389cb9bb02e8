<?php

namespace Wb3\SharedBundle\Entity;

use Doctrine\ORM\Mapping as ORM;
use Wb3\SharedBundle\Entity\Trait\TimestampTrait;
use App\Repository\PlatformProductRepository;

#[ORM\Entity(repositoryClass: PlatformProductRepository::class)]
class PlatformProduct
{
    use TimestampTrait;
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\ManyToOne(inversedBy: 'platformProducts')]
    private ?Platform $platform = null;

    #[ORM\ManyToOne(inversedBy: 'platformProducts')]
    private ?Product $product = null;

    #[ORM\Column(nullable: true)]
    private ?array $attributes = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getPlatform(): ?Platform
    {
        return $this->platform;
    }

    public function setPlatform(?Platform $platform): static
    {
        $this->platform = $platform;

        return $this;
    }

    public function getProduct(): ?Product
    {
        return $this->product;
    }

    public function setProduct(?Product $product): static
    {
        $this->product = $product;

        return $this;
    }

    public function getAttributes(): ?array
    {
        return $this->attributes;
    }

    public function setAttributes(?array $attributes): static
    {
        $this->attributes = $attributes;

        return $this;
    }
}
