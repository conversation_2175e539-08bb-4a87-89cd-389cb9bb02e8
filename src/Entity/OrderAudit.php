<?php

namespace Wb3\SharedBundle\Entity;

use App\Repository\OrderAuditRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: OrderAuditRepository::class)]
class OrderAudit
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(length: 10)]
    private ?string $type = null;

    #[ORM\Column(length: 255)]
    private ?string $objectId = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $discriminator = null;

    #[ORM\Column(length: 40, nullable: true)]
    private ?string $transaction_hash = null;

    #[ORM\Column(nullable: true)]
    private ?array $diffs = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $blameId = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $blameUser = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $blame_user_fqdn = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $blame_user_firewall = null;

    #[ORM\Column(length: 45, nullable: true)]
    private ?string $ip = null;

    #[ORM\Column]
    private ?\DateTimeImmutable $createdAt = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(string $type): static
    {
        $this->type = $type;

        return $this;
    }

    public function getObjectId(): ?string
    {
        return $this->objectId;
    }

    public function setObjectId(string $objectId): static
    {
        $this->objectId = $objectId;

        return $this;
    }

    public function getDiscriminator(): ?string
    {
        return $this->discriminator;
    }

    public function setDiscriminator(?string $discriminator): static
    {
        $this->discriminator = $discriminator;

        return $this;
    }

    public function getTransactionHash(): ?string
    {
        return $this->transaction_hash;
    }

    public function setTransactionHash(?string $transaction_hash): static
    {
        $this->transaction_hash = $transaction_hash;

        return $this;
    }

    public function getDiffs(): ?array
    {
        return $this->diffs;
    }

    public function setDiffs(?array $diffs): static
    {
        $this->diffs = $diffs;

        return $this;
    }

    public function getBlameId(): ?string
    {
        return $this->blameId;
    }

    public function setBlameId(?string $blameId): static
    {
        $this->blameId = $blameId;

        return $this;
    }

    public function getBlameUser(): ?string
    {
        return $this->blameUser;
    }

    public function setBlameUser(?string $blameUser): static
    {
        $this->blameUser = $blameUser;

        return $this;
    }

    public function getBlameUserFqdn(): ?string
    {
        return $this->blame_user_fqdn;
    }

    public function setBlameUserFqdn(?string $blame_user_fqdn): static
    {
        $this->blame_user_fqdn = $blame_user_fqdn;

        return $this;
    }

    public function getBlameUserFirewall(): ?string
    {
        return $this->blame_user_firewall;
    }

    public function setBlameUserFirewall(?string $blame_user_firewall): static
    {
        $this->blame_user_firewall = $blame_user_firewall;

        return $this;
    }

    public function getIp(): ?string
    {
        return $this->ip;
    }

    public function setIp(?string $ip): static
    {
        $this->ip = $ip;

        return $this;
    }

    public function getCreatedAt(): ?\DateTimeImmutable
    {
        return $this->createdAt;
    }

    public function setCreatedAt(\DateTimeImmutable $createdAt): static
    {
        $this->createdAt = $createdAt;

        return $this;
    }
}
