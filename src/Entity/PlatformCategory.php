<?php

namespace Wb3\SharedBundle\Entity;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use App\Repository\PlatformCategoryRepository;
use Wb3\SharedBundle\Entity\Trait\TimestampTrait;

#[ORM\Entity(repositoryClass: PlatformCategoryRepository::class)]
class PlatformCategory
{
    use TimestampTrait;
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\ManyToOne(inversedBy: 'categories')]
    private ?Platform $platform = null;

    #[ORM\Column(length: 255)]
    private ?string $remoteId = null;

    #[ORM\Column(length: 255)]
    private ?string $name = null;

    #[ORM\Column(length: 255)]
    private ?string $path = null;

    #[ORM\Column]
    private array $attributes = [];

    /**
     * @var Collection<int, PlatformProductVariant>
     */
    #[ORM\OneToMany(targetEntity: PlatformProductVariant::class, mappedBy: 'platformCategory')]
    private Collection $productVariantPlatforms;

    public function __construct()
    {
        $this->productVariantPlatforms = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getPlatform(): ?Platform
    {
        return $this->platform;
    }

    public function setPlatform(?Platform $platform): static
    {
        $this->platform = $platform;

        return $this;
    }

    public function getRemoteId(): ?string
    {
        return $this->remoteId;
    }

    public function setRemoteId(string $remoteId): static
    {
        $this->remoteId = $remoteId;

        return $this;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): static
    {
        $this->name = $name;

        return $this;
    }

    public function getPath(): ?string
    {
        return $this->path;
    }

    public function setPath(string $path): static
    {
        $this->path = $path;

        return $this;
    }

    public function getAttributes(): array
    {
        return $this->attributes;
    }

    public function setAttributes(array $attributes): static
    {
        $this->attributes = $attributes;

        return $this;
    }

    /**
     * @return Collection<int, PlatformProductVariant>
     */
    public function getProductVariantPlatforms(): Collection
    {
        return $this->productVariantPlatforms;
    }

    public function addProductVariantPlatform(PlatformProductVariant $productVariantPlatform): static
    {
        if (!$this->productVariantPlatforms->contains($productVariantPlatform)) {
            $this->productVariantPlatforms->add($productVariantPlatform);
            $productVariantPlatform->setPlatformCategory($this);
        }

        return $this;
    }

    public function removeProductVariantPlatform(PlatformProductVariant $productVariantPlatform): static
    {
        if ($this->productVariantPlatforms->removeElement($productVariantPlatform)) {
            // set the owning side to null (unless already changed)
            if ($productVariantPlatform->getPlatformCategory() === $this) {
                $productVariantPlatform->setPlatformCategory(null);
            }
        }

        return $this;
    }
}
