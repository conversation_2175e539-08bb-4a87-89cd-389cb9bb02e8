<?php

namespace Wb3\SharedBundle\Entity;

use App\Repository\PlatformForbiddenProductTypeRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Wb3\SharedBundle\Entity\Trait\TimestampTrait;

#[ORM\Entity(repositoryClass: PlatformForbiddenProductTypeRepository::class)]
class PlatformForbiddenProductType
{
    use TimestampTrait;
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(length: 255)]
    private ?string $name = null;

    /**
     * @var Collection<int, PlatformForbiddenProduct>
     */
    #[ORM\OneToMany(targetEntity: PlatformForbiddenProduct::class, mappedBy: 'type')]
    private Collection $products;

    public function __construct()
    {
        $this->products = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): static
    {
        $this->name = $name;

        return $this;
    }

    /**
     * @return Collection<int, PlatformForbiddenProduct>
     */
    public function getProducts(): Collection
    {
        return $this->products;
    }

    public function addPlatformForbiddenProduct(PlatformForbiddenProduct $platformForbiddenProduct): static
    {
        if (!$this->products->contains($platformForbiddenProduct)) {
            $this->products->add($platformForbiddenProduct);
            $platformForbiddenProduct->setType($this);
        }

        return $this;
    }

    public function removePlatformForbiddenProduct(PlatformForbiddenProduct $platformForbiddenProduct): static
    {
        if ($this->products->removeElement($platformForbiddenProduct)) {
            // set the owning side to null (unless already changed)
            if ($platformForbiddenProduct->getType() === $this) {
                $platformForbiddenProduct->setType(null);
            }
        }

        return $this;
    }

    public function __toString(): string
    {
       return $this->getName();
    }
}
