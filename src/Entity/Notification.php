<?php

namespace Wb3\SharedBundle\Entity;

use App\Repository\NotificationRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: NotificationRepository::class)]
#[ORM\Table(name: "tool_notification")]
class Notification
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(length: 50)]
    private string $code;

    #[ORM\Column(length: 255)]
    private ?string $title = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $parameters = null;

    #[ORM\Column(options: ['default' => false])]
    private bool $isEmail = false;
    #[ORM\Column(length: 255, nullable: true)]
    private ?string $emailTitle = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $emailContent = null;

    #[ORM\Column(options: ['default' => false])]
    private bool $isSms = false;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $smsContent = null;

    #[ORM\Column(options: ['default' => false])]
    private ?bool $isDiscord = false;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $discordChannel = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $discordContent = null;

    #[ORM\Column(options: ['default' => false])]
    private ?bool $isTelegram = null;

    #[ORM\Column(type: Types::TEXT, nullable: true, options: ['default' => null])]
    private ?string $telegramContent = null;

    #[ORM\Column(length: 255, options: ['default' => ''])]
    private ?string $telegramChannel = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getCode(): string
    {
        return $this->code;
    }

    public function setCode(string $code): static
    {
        $this->code = $code;

        return $this;
    }

    public function getTitle(): string
    {
        return $this->title;
    }

    public function setTitle(string $title): static
    {
        $this->title = $title;

        return $this;
    }

    public function getParameters(): ?string
    {
        return $this->parameters;
    }

    public function setParameters(?string $parameters): static
    {
        $this->parameters = $parameters;

        return $this;
    }

    public function isEmail(): ?bool
    {
        return $this->isEmail;
    }

    public function setEmail(?bool $isEmail): static
    {
        $this->isEmail = $isEmail;

        return $this;
    }

    public function getEmailTitle(): ?string
    {
        return $this->emailTitle;
    }

    public function setEmailTitle(?string $emailTitle): static
    {
        $this->emailTitle = $emailTitle;

        return $this;
    }

    public function getEmailContent(): ?string
    {
        return $this->emailContent;
    }

    public function setEmailContent(?string $emailContent): static
    {
        $this->emailContent = $emailContent;

        return $this;
    }

    public function isSms(): ?bool
    {
        return $this->isSms;
    }

    public function setSms(bool $isSms): static
    {
        $this->isSms = $isSms;

        return $this;
    }

    public function getSmsContent(): ?string
    {
        return $this->smsContent;
    }

    public function setSmsContent(?string $smsContent): static
    {
        $this->smsContent = $smsContent;

        return $this;
    }

    public function isDiscord(): ?bool
    {
        return $this->isDiscord;
    }

    public function setDiscord(bool $is_discord): static
    {
        $this->isDiscord = $is_discord;

        return $this;
    }

    public function getDiscordChannel(): ?string
    {
        return $this->discordChannel;
    }

    public function setDiscordChannel(?string $discordChannel): static
    {
        $this->discordChannel = $discordChannel;

        return $this;
    }

    public function getDiscordContent(): ?string
    {
        return $this->discordContent;
    }

    public function setDiscordContent(?string $discordContent): static
    {
        $this->discordContent = $discordContent;

        return $this;
    }

    public function isTelegram(): ?bool
    {
        return $this->isTelegram;
    }

    public function setIsTelegram(?bool $isTelegram): static
    {
        $this->isTelegram = $isTelegram;

        return $this;
    }

    public function getTelegramContent(): ?string
    {
        return $this->telegramContent;
    }

    public function setTelegramContent(?string $telegramContent): static
    {
        $this->telegramContent = $telegramContent;

        return $this;
    }

    public function getTelegramChannel(): ?string
    {
        return $this->telegramChannel;
    }

    public function setTelegramChannel(string $telegramChannel): static
    {
        $this->telegramChannel = $telegramChannel;

        return $this;
    }
}
