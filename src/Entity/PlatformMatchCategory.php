<?php

namespace Wb3\SharedBundle\Entity;

use App\Repository\PlatformMatchCategoryRepository;
use DateTimeImmutable;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Wb3\SharedBundle\Entity\Trait\TimestampTrait;

#[ORM\Entity(repositoryClass: PlatformMatchCategoryRepository::class)]
class PlatformMatchCategory
{
    use TimestampTrait;
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\ManyToOne(inversedBy: 'platformMatchCategories')]
    private ?Platform $platform = null;

    #[ORM\Column]
    private ?string $remoteId = null;

    #[ORM\Column(length: 255)]
    private ?string $name = null;

    /**
     * @var Collection<int, PlatformMatchCategoryAttribute>
     */

    /**
     * @var Collection<int, PlatformMatchVariant>
     */
    #[ORM\OneToMany(targetEntity: PlatformMatchVariant::class, mappedBy: 'platformMatchCategory')]
    private Collection $platformMatchVariants;

    #[ORM\Column(length: 255)]
    private ?string $path = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $remoteParentId = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $attributes = null;

    #[ORM\Column(nullable: true)]
    private ?DateTimeImmutable $attributesExpireAt = null;

    public function __construct()
    {
        $this->platformMatchVariants = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getPlatform(): ?Platform
    {
        return $this->platform;
    }

    public function setPlatform(?Platform $platform): static
    {
        $this->platform = $platform;

        return $this;
    }

    public function getRemoteId(): ?string
    {
        return $this->remoteId;
    }

    public function setRemoteId(?string $remoteId): static
    {
        $this->remoteId = $remoteId;

        return $this;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): static
    {
        $this->name = $name;

        return $this;
    }

    /**
     * @return Collection<int, PlatformMatchVariant>
     */
    public function getPlatformMatchVariants(): Collection
    {
        return $this->platformMatchVariants;
    }

    public function addPlatformMatchProductVariant(PlatformMatchVariant $platformMatchProductVariant): static
    {
        if (!$this->platformMatchVariants->contains($platformMatchProductVariant)) {
            $this->platformMatchVariants->add($platformMatchProductVariant);
            $platformMatchProductVariant->setCategory($this);
        }

        return $this;
    }

    public function removePlatformMatchProductVariant(PlatformMatchVariant $platformMatchProductVariant): static
    {
        if ($this->platformMatchVariants->removeElement($platformMatchProductVariant)) {
            // set the owning side to null (unless already changed)
            if ($platformMatchProductVariant->getCategory() === $this) {
                $platformMatchProductVariant->setCategory(null);
            }
        }

        return $this;
    }

    public function getPath(): ?string
    {
        return $this->path;
    }

    public function setPath(string $path): static
    {
        $this->path = $path;

        return $this;
    }

    public function getRemoteParentId(): ?string
    {
        return $this->remoteParentId;
    }

    public function setRemoteParentId(?string $remoteParentId): static
    {
        $this->remoteParentId = $remoteParentId;

        return $this;
    }

    public function __toString(): string
    {
        return $this->getId() . " - ". $this->getPlatform()->getTitle()." - ".$this->name;
    }

    public function getAttributes(): ?string
    {
        return $this->attributes;
    }

    public function setAttributes(?string $attributes): static
    {
        $this->attributes = $attributes;

        return $this;
    }

    public function getAttributesExpireAt(): ?DateTimeImmutable
    {
        return $this->attributesExpireAt;
    }

    public function setAttributesExpireAt(DateTimeImmutable $attributesExpireAt): static
    {
        $this->attributesExpireAt = $attributesExpireAt;

        return $this;
    }
}
