<?php

namespace Wb3\SharedBundle\Entity;

use App\Repository\OrderKorgunInvoiceRepository;
use DateTimeInterface;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Wb3\SharedBundle\Entity\Trait\TimestampTrait;

#[ORM\Entity(repositoryClass: OrderKorgunInvoiceRepository::class)]
class OrderKorgunInvoice
{
    use TimestampTrait;
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $fatEkod = null;

    #[ORM\Column(length: 255)]
    private ?string $faturaNo = null;

    #[ORM\Column(length: 255)]
    private ?string $fatTip = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    private ?DateTimeInterface $fatTar = null;

    #[ORM\Column(length: 255)]
    private ?string $belgeNo = null;

    #[ORM\Column]
    private ?bool $eArsivMi = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getFatEkod(): ?string
    {
        return $this->fatEkod;
    }

    public function setFatEkod(?string $fatEkod): static
    {
        $this->fatEkod = $fatEkod;

        return $this;
    }

    public function getFaturaNo(): ?string
    {
        return $this->faturaNo;
    }

    public function setFaturaNo(string $faturaNo): static
    {
        $this->faturaNo = $faturaNo;

        return $this;
    }

    public function getFatTip(): ?string
    {
        return $this->fatTip;
    }

    public function setFatTip(string $fatTip): static
    {
        $this->fatTip = $fatTip;

        return $this;
    }

    public function getFatTar(): ?DateTimeInterface
    {
        return $this->fatTar;
    }

    public function setFatTar(DateTimeInterface $fatTar): static
    {
        $this->fatTar = $fatTar;

        return $this;
    }

    public function getBelgeNo(): ?string
    {
        return $this->belgeNo;
    }

    public function setBelgeNo(string $belgeNo): static
    {
        $this->belgeNo = $belgeNo;

        return $this;
    }

    public function isEArsivMi(): ?bool
    {
        return $this->eArsivMi;
    }

    public function setEArsivMi(bool $eArsivMi): static
    {
        $this->eArsivMi = $eArsivMi;

        return $this;
    }
}
