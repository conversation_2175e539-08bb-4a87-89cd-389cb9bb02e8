<?php

namespace App\FormType;

use Doctrine\Common\Collections\Order;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class OrderSalePlatformPaidFormType extends AbstractType
{

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('invoiceNumber', TextType::class, [
                'data' => '123',
                'attr' => [
                    'class' => 'form-control form-control-solid border-primary',
                    'placeholder' => 'Fatura Numarası',
                ],
                'label' => 'Fatura No',
                'label_attr' => [
                    'class' => 'form-label',
                ],
            ])
            ->add('trackNumber', TextType::class, [
                'data' => '1122',
                'label' => 'Takip Numarası',
                'label_attr' => [
                    'class' => 'form-label fw-bold',
                ],
            ])
            ->add('submit',SubmitType::class,[
                'label' => 'Kargola',
            ]);
    }
}