<?php

namespace App\FormType;

use Doctrine\Common\Collections\Order;
use Doctrine\ORM\EntityRepository;
use Doctrine\ORM\QueryBuilder;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\NotBlank;
use Wb3\SharedBundle\Entity\OrderCargo;

class OrderSaleVendorPaidFormType extends AbstractType
{

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('invoiceSerialNumber', TextType::class, [
                'data' => $options['data']['invoiceSerialNumber'] ?? null,
                'attr' => [
                    'class' => 'form-control form-control-solid border-primary',
                    'placeholder' => 'Seri No',
                ],
                'label' => 'Fatura Seri No',
                'label_attr' => [
                    'class' => 'form-label fw-bold',
                ],
                'constraints' => [
                    new NotBlank(),
                ]
            ])
            ->add('invoiceNumber', TextType::class, [
                'data' => $options['data']['invoiceNumber'] ?? null,
                'required'=>true,
                'attr' => [
                    'class' => 'form-control form-control-solid border-primary',
                    'placeholder' => 'Fatura No',
                ],
                'label' => 'Fatura No',
                'label_attr' => [
                    'class' => 'form-label',
                ],
                'constraints' => [
                    new NotBlank(),
                ]
            ])
            ->add('cargoCompany', EntityType::class, [
                'class' => OrderCargo::class,
                'query_builder' => function (EntityRepository $er):QueryBuilder {
                    return $er->createQueryBuilder('t')->where('t.isActive = true')
                        ->andWhere('t.id != 1');
                },
                'choice_label' => 'name',
                'label' => 'Kargo Firmasi',
                'label_attr' => [
                    'class' => 'form-label fw-bold',
                ],
            ])
            ->add('submit',SubmitType::class,[
                'label' => 'Kargola'
            ]);
            //->add('senderType', ChoiceType::class, [
            //    'data' => '',
            //    'choices' => [
            //        'Satıcı Ödemeli' => 1,
            //    ],
            //    'label' => 'Gönderi Tipi',
            //    'label_attr' => [
            //        'class' => 'form-label fw-bold',
            //    ],
            //]);
    }
    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            // Veya form verisini bir array olarak kullanmak istiyorsanız:
            'data_class' => null,
            'allow_extra_fields' => true,
        ]);
    }

}