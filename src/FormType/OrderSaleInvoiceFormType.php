<?php
namespace App\FormType;

use App\Repository\OrderCustomerAddressRepository;
use App\Repository\OrderCustomerAddressTypeRepository;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\Length;
use Wb3\SharedBundle\Entity\OrderAddress;
use Wb3\SharedBundle\Entity\OrderCustomer;
use Wb3\SharedBundle\Entity\OrderCustomerAddress;
use Wb3\SharedBundle\Entity\OrderCustomerAddressType;
use Symfony\Component\Form\Extension\Core\Type\TextType;

class OrderSaleInvoiceFormType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        // Müşteri nesnesini option olarak al
        /** @var OrderAddress $address */
        $address = $options['data'];
        $builder
            ->add('address', TextType::class, [
                'label' => 'Adres',
                'data' => $address->getAddress(),  // Adres varsa göster, yoksa boş bırak
                'attr' => [
                    'class' => 'form-control form-control-solid',
                    'placeholder' => 'Adresinizi giriniz',
                ],
                'label_attr' => [
                    'class' => 'form-label fw-bold',
                ],
                'constraints' => [
                    new Length([
                        'min' => 10,
                        'minMessage' => 'Adres en az {{ limit }} karakter olmalı.',
                        'max' => 140,
                        'maxMessage' => 'Adres en fazla {{ limit }} karakter olabilir.',
                    ]),
                ]
            ])
            ->add('town', TextType::class, [
                'label' => 'İlçe',
                'data' => $address->getTown(), // `customerTown` alanını doldur
                'attr' => [
                    'class' => 'form-control form-control-solid',
                    'placeholder' => 'İlçeyi giriniz',
                ],
                'label_attr' => [
                    'class' => 'form-label fw-bold',
                ],
            ])
            ->add('city', TextType::class, [
                'label' => 'Şehir',
                'data' => $address ? $address->getCity() : '', // `customerCity` alanını doldur
                'attr' => [
                    'class' => 'form-control form-control-solid',
                    'placeholder' => 'Şehri giriniz',
                ],
                'label_attr' => [
                    'class' => 'form-label fw-bold',
                ],
            ])
            ->add('phone', TextType::class, [
                'label' => 'Cep Telefonu',
                'data' => $address ? $address->getPhone() : '',
                'attr' => [
                    'class' => 'form-control form-control-solid',
                    'placeholder' => 'Telefon numaranızı giriniz',
                ],
                'label_attr' => [
                    'class' => 'form-label fw-bold',
                ],
                'required' => false
            ])
            ->add('submit', SubmitType::class, [
                'label' => 'Kaydet',
                'attr' => ['class' => 'btn btn-primary mt-5']
            ]);;

    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        // Formu oluştururken müşteri nesnesini parametre olarak bekle
        $resolver->setDefaults([
            'data_class' => OrderAddress::class,
            'customer' => null,  // Varsayılan değer null
            'address' => null,

        ]);
    }
}

