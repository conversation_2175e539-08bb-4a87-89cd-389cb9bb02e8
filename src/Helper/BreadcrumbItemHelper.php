<?php

namespace Wb3\SharedBundle\Helper;

class BreadcrumbItemHelper
{
    private string $name;
    private ?string $url;
    private ?string $path;

    public function __construct(?string $name, ?string $path = null,?string $url = null)
    {
        $this->name = $name;
        $this->path = $path;
        $this->url = $url;
    }
    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): BreadcrumbItemHelper
    {
        $this->name = $name;
        return $this;
    }

    public function getUrl(): ?string
    {
        return $this->url;
    }

    public function setUrl(?string $url): BreadcrumbItemHelper
    {
        $this->url = $url;
        return $this;
    }

    public function getPath(): ?string
    {
        return $this->path;
    }

    public function setPath(?string $path): BreadcrumbItemHelper
    {
        $this->path = $path;
        return $this;
    }

    public function toArray()
    {
        return [
            'name' => $this->getName(),
            'path' => $this->getPath(),
            'url' => $this->getUrl(),
        ];
    }

}