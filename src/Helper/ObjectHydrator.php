<?php

namespace Wb3\SharedBundle\Helper;

use ReflectionClass;
use S<PERSON>fony\Component\PropertyInfo\Extractor\ConstructorExtractor;
use S<PERSON>fony\Component\PropertyInfo\Extractor\PhpDocExtractor;
use Symfony\Component\PropertyInfo\PropertyInfoExtractor;
use Symfony\Component\Serializer\Encoder\JsonEncoder;
use Symfony\Component\Serializer\Normalizer\ArrayDenormalizer;
use Symfony\Component\Serializer\Normalizer\ObjectNormalizer;
use Symfony\Component\Serializer\Serializer;

abstract class ObjectHydrator
{
  public static function hydrate(array $content, object $dto,array $childObjects = []): object
  {
      $r = new ReflectionClass(get_class($dto)); //Stat
    foreach ($content as $key => $value) {
      if (property_exists($dto, $key)) {
          if(is_numeric($value ) || is_string($value))
            $dto->$key = $value;
      }
    }
foreach ($childObjects as $childObject) {
      if(property_exists($dto, $childObject['name'])) {
          $d = $r->getProperty($childObject['name']);
        if($r->getProperty($childObject['name'])->getType()->getName() == 'array') {
          $newValues = array_map(function($item) use ($childObject){
            return self::hydrate($item,new $childObject['class']);
          },$dto->{$childObject['name']});
          $dto->{$childObject['name']} = $newValues;
        }
        else {
          $dto->$childObject['name'] = self::hydrate($dto->$childObject['name'],new $childObject['class']);
        }
      }
}
    return $dto;
  }
    /** JSON u objeye çevirir. */
    public static function deserialize(string $json, string $className): object
    {
        $phpDocExtractor = new PhpDocExtractor();
        $typeExtractor = new PropertyInfoExtractor(
            typeExtractors: [
                new ConstructorExtractor([$phpDocExtractor]),
                $phpDocExtractor,
            ],
        );

        $normalizers = [
            new ObjectNormalizer(propertyTypeExtractor: $typeExtractor),
            new ArrayDenormalizer(),
        ];

        $encoders = [
            new JsonEncoder(),
        ];

        $serializer = new Serializer($normalizers, $encoders);

        return $serializer->deserialize($json, $className, 'json');
    }

    /** Objeyi JSON a çevirir */
    public static  function serialize(object $object) : string
    {
        $normalizers = [
            new ObjectNormalizer(),
        ];

        $encoders = [
            new JsonEncoder(),
        ];

        $serializer = new Serializer($normalizers, $encoders);

        return $serializer->serialize($object, 'json');
    }

    /** Objeyi array a çevirir  */

    public static function normalize(object $object)
    {
        $normalizers = [
            new ObjectNormalizer(),
        ];

        $encoders = [
            new JsonEncoder(),
        ];

        $serializer = new Serializer($normalizers, $encoders);

        return $serializer->normalize($object);
    }
}