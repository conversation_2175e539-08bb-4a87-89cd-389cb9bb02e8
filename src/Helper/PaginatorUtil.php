<?php
namespace Wb3\SharedBundle\Helper;

use Doctrine\ORM\Query;
use Doctrine\ORM\Tools\Pagination\Paginator;

class PaginatorUtil
{
    const PAGE_LIMIT = 20;

    /**
     * @param Query $dql
     * @param int   $page
     * @param int   $limit
     *
     * @return array
     */
    public static function paginate(Query $dql, int $page = 1, int $limit = self::PAGE_LIMIT, array $params = []): array
    {
        $limit = $limit > 0 ? $limit : self::PAGE_LIMIT;
        $page = $page > 0 ? $page : 1;

        $fetchJoinCollection = isset($params['fetchJoinCollection']) ? $params['fetchJoinCollection'] : true;
        $useOutputWalkers = isset($params['useOutputWalkers']) ? $params['useOutputWalkers'] : false;
        
        $paginator = new Paginator($dql, $fetchJoinCollection);
        $paginator->setUseOutputWalkers($useOutputWalkers);
        $paginator->getQuery()->setFirstResult($limit * ($page - 1))->setMaxResults($limit);

        return [
            'totalCount' => $paginator->count(),//Toplam Satır Sayısı
            'itemCount' => $paginator->getIterator()->count(), // Response Dönen Satır Sayısı
            'pageCount' => ceil($paginator->count() / $limit), // Toplam Sayfa Sayısı
            'currentPage' => $page, // Şuanki Sayfa
            'limit' => $limit, // items da olması beklenen satır sayısı
            'items' => $paginator->getQuery()->getResult(), // Satır Listesi
        ];
    }

    /**
     * @param Query $dql
     * @param int   $page
     * @param int   $limit
     *
     * @return array
     */
    public static function paginateArray(Query $dql, int $page = 1, int $limit = self::PAGE_LIMIT): array
    {
        $limit = $limit > 0 ? $limit : self::PAGE_LIMIT;
        $page = $page > 0 ? $page : 1;

        $paginator = new Paginator($dql);
        $paginator->getQuery()->setFirstResult($limit * ($page - 1))->setMaxResults($limit);
        $paginator->setUseOutputWalkers(false);

        return [
            'totalCount' => $paginator->count(),//Toplam Satır Sayısı
            'itemCount' => $paginator->getIterator()->count(), // Response Dönen Satır Sayısı
            'pageCount' => ceil($paginator->count() / $limit), // Toplam Sayfa Sayısı
            'currentPage' => $page, // Şuanki Sayfa
            'limit' => $limit, // items olması beklenen satır sayısı
            'items' => $paginator->getQuery()->getArrayResult(), // Satır Listesi
        ];
    }
}