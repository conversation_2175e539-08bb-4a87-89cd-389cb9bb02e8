<?php

namespace Wb3\SharedBundle\Helper;

class FilterUtility
{
    public static function decorateFilter(array $filters): array
    {
        $aliases = ['pavp'=>'productAttributeValueProduct'];
        $decoratedFilters = [];

        foreach ($aliases AS $alias => $entity) {
            foreach ($filters as $key => $value) {
                if (empty($value)) {
                    continue;
                }
                if(str_starts_with($key."-", $alias)) {
                    $decoratedFilters[$entity][str_replace($alias."-", '', $key)] = $value;
                } else {
                    $decoratedFilters[$key] = $value;
                }
            }
        }
        return $decoratedFilters;
    }
}