<?php
namespace Wb3\SharedBundle\Helper;

use DateTime;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

class StringUtility
{
    public static function addZero($value, $totalChar)
    {
        return str_pad($value, $totalChar, "0", STR_PAD_LEFT);
    }

    public static function validateDate($dateString)
    {
        if (!preg_match("/\d{4}-\d{1,2}-\d{1,2}/", $dateString)) {
            throw new BadRequestHttpException("INVALID_DATE_FORMAT");
        }
    }

    public static function parseDateRangeFilter($dateString)
    {
        if (!preg_match("/\d{4}\/\d{1,2}\/\d{1,2} - \d{4}\/\d{1,2}\/\d{1,2}/", $dateString)) {
            throw new BadRequestHttpException("INVALID_DATE_FORMAT");
        }

        $dateArray = explode(" - ", $dateString);

        $dateFrom = date_create_from_format('Y/m/d', $dateArray[0]);
        $dateFrom->setTime(00, 00, 00);

        $nextDate = date_create_from_format('Y/m/d', $dateArray[1]);
        $nextDate->setTime(23, 59, 59);

        return [
            'dateFrom' => $dateFrom,
            'dateTo' => $nextDate,
        ];
    }

    public static function giveMeOneDayDateFilter()
    {
        $from = new DateTime();
        $from->modify('-1 day');
        $to = new DateTime();
        $dateFilter = array(
            'dateFrom' => $from,
            'dateTo' => $to
        );
        return $dateFilter;
    }

    public static function replaceCharForUrl($input)
    {
        $a = array(
            'À', 'Á', 'Â', 'Ã', 'Ä', 'Å', 'Æ', 'Ç', 'È', 'É', 'Ê', 'Ë', 'Ì', 'Í', 'Î', 'Ï',
            'Ð', 'Ñ', 'Ò', 'Ó', 'Ô', 'Õ', 'Ö', 'Ø', 'Ù', 'Ú', 'Û', 'Ü', 'Ý', 'ß', 'à', 'á',
            'â', 'ã', 'ä', 'å', 'æ', 'ç', 'è', 'é', 'ê', 'ë', 'ì', 'í', 'î', 'ï', 'ñ', 'ò',
            'ó', 'ô', 'õ', 'ö', 'ø', 'ù', 'ú', 'û', 'ü', 'ý', 'ÿ', 'Ā', 'ā', 'Ă', 'ă', 'Ą',
            'ą', 'Ć', 'ć', 'Ĉ', 'ĉ', 'Ċ', 'ċ', 'Č', 'č', 'Ď', 'ď', 'Đ', 'đ', 'Ē', 'ē', 'Ĕ',
            'ĕ', 'Ė', 'ė', 'Ę', 'ę', 'Ě', 'ě', 'Ĝ', 'ĝ', 'Ğ', 'ğ', 'Ġ', 'ġ', 'Ģ', 'ģ', 'Ĥ',
            'ĥ', 'Ħ', 'ħ', 'Ĩ', 'ĩ', 'Ī', 'ī', 'Ĭ', 'ĭ', 'Į', 'į', 'İ', 'ı', 'Ĳ', 'ĳ', 'Ĵ',
            'ĵ', 'Ķ', 'ķ', 'Ĺ', 'ĺ', 'Ļ', 'ļ', 'Ľ', 'ľ', 'Ŀ', 'ŀ', 'Ł', 'ł', 'Ń', 'ń', 'Ņ',
            'ņ', 'Ň', 'ň', 'ŉ', 'Ō', 'ō', 'Ŏ', 'ŏ', 'Ő', 'ő', 'Œ', 'œ', 'Ŕ', 'ŕ', 'Ŗ', 'ŗ',
            'Ř', 'ř', 'Ś', 'ś', 'Ŝ', 'ŝ', 'Ş', 'ş', 'Š', 'š', 'Ţ', 'ţ', 'Ť', 'ť', 'Ŧ', 'ŧ',
            'Ũ', 'ũ', 'Ū', 'ū', 'Ŭ', 'ŭ', 'Ů', 'ů', 'Ű', 'ű', 'Ų', 'ų', 'Ŵ', 'ŵ', 'Ŷ', 'ŷ',
            'Ÿ', 'Ź', 'ź', 'Ż', 'ż', 'Ž', 'ž', 'ſ', 'ƒ', 'Ơ', 'ơ', 'Ư', 'ư', 'Ǎ', 'ǎ', 'Ǐ',
            'ǐ', 'Ǒ', 'ǒ', 'Ǔ', 'ǔ', 'Ǖ', 'ǖ', 'Ǘ', 'ǘ', 'Ǚ', 'ǚ', 'Ǜ', 'ǜ', 'Ǻ', 'ǻ', 'Ǽ',
            'ǽ', 'Ǿ', 'ǿ', '?', '’');
        $b = array('A', 'A', 'A', 'A', 'A', 'A', 'AE', 'C', 'E', 'E', 'E', 'E', 'I', 'I', 'I',
            'I', 'D', 'N', 'O', 'O', 'O', 'O', 'O', 'O', 'U', 'U', 'U', 'U', 'Y', 's', 'a', 'a',
            'a', 'a', 'a', 'a', 'ae', 'c', 'e', 'e', 'e', 'e', 'i', 'i', 'i', 'i', 'n', 'o',
            'o', 'o', 'o', 'o', 'o', 'u', 'u', 'u', 'u', 'y', 'y', 'A', 'a', 'A', 'a', 'A',
            'a', 'C', 'c', 'C', 'c', 'C', 'c', 'C', 'c', 'D', 'd', 'D', 'd', 'E', 'e', 'E',
            'e', 'E', 'e', 'E', 'e', 'E', 'e', 'G', 'g', 'G', 'g', 'G', 'g', 'G', 'g', 'H',
            'h', 'H', 'h', 'I', 'i', 'I', 'i', 'I', 'i', 'I', 'i', 'I', 'i', 'IJ', 'ij', 'J',
            'j', 'K', 'k', 'L', 'l', 'L', 'l', 'L', 'l', 'L', 'l', 'l', 'l', 'N', 'n', 'N',
            'n', 'N', 'n', 'n', 'O', 'o', 'O', 'o', 'O', 'o', 'OE', 'oe', 'R', 'r', 'R', 'r',
            'R', 'r', 'S', 's', 'S', 's', 'S', 's', 'S', 's', 'T', 't', 'T', 't', 'T', 't',
            'U', 'u', 'U', 'u', 'U', 'u', 'U', 'u', 'U', 'u', 'U', 'u', 'W', 'w', 'Y', 'y',
            'Y', 'Z', 'z', 'Z', 'z', 'Z', 'z', 's', 'f', 'O', 'o', 'U', 'u', 'A', 'a', 'I',
            'i', 'O', 'o', 'U', 'u', 'U', 'u', 'U', 'u', 'U', 'u', 'U', 'u', 'A', 'a', 'AE',
            'ae', 'O', 'o', '', ' ');

        return str_replace($a, $b, $input);
    }

    public static function slugify($text)
    {

        return strtolower(
            preg_replace(array('/[^a-zA-Z0-9 -]/', '/[ -]+/', '/^-|-$/'),
                array('', '-', ''),
                self::replaceCharForUrl($text)
            )
        );
    }

    public static function slugifyByDivider($text, string $divider = '_')
    {
        // replace non letter or digits by divider
        $text = preg_replace('~[^\pL\d]+~u', $divider, $text);

        // transliterate
        $text = iconv('utf-8', 'us-ascii//TRANSLIT', $text);

        // remove unwanted characters
        $text = preg_replace('~[^-\w]+~', '', $text);

        // trim
        $text = trim($text, $divider);

        // remove duplicate divider
        $text = preg_replace('~-+~', $divider, $text);

        // lowercase
        $text = strtolower($text);

        if (empty($text)) {
            return 'n-a';
        }

        return $text;
    }

    public static function generateUserPassword($simple = true)
    {
        if ($simple) {
            $paswd = rand(10000, 99999);
        } else {
            $paswd = substr(hash('sha512', rand()), 0, 8); // Reduces the size to 12 chars
        }

        return $paswd;
    }

    public static function sanitiseKeyword($keyword)
    {
        $maskingCharacter = '*';
        return  mb_substr($keyword,0,1).str_repeat($maskingCharacter, strlen($keyword) - 1);
    }

    public static function maskPhone($keyword)
    {
        $maskingCharacter = '*';
        return  substr($keyword,0,1).str_repeat($maskingCharacter, strlen($keyword) - 2).substr($keyword,strlen($keyword) - 2,2);
    }

    public static function maskPhoneForForgetOption($keyword)
    {
        $maskingCharacter = '*';
        return str_repeat($maskingCharacter, strlen($keyword) - 2).substr($keyword,strlen($keyword) - 2,2);
    }

    public static function maskEmail($email)
    {
        $em   = explode("@",$email);
        $name = implode('@', array_slice($em, 0, count($em)-1));
        $len  = floor(strlen($name)/2);

        return substr($name,0, $len) . str_repeat('*', $len) . "@" . end($em);
    }

    public static function createCardMask($cardNumber)
    {
        $maskingCharacter = '*';
        return substr($cardNumber, 0, 6) . str_repeat($maskingCharacter, strlen($cardNumber) - 10) . substr($cardNumber, -4);
    }

    public static function removeMask($inputValue)
    {
        return str_replace(['-', ' '], '', $inputValue);
    }

    public function moneyFormat($val)
    {
        return number_format($val,2, '.', ',');
    }
    public static function generateHash()
    {
        return hash_hmac('SHA256', microtime(true).rand(1, 10000), "PMK");
    }

    public static  function dateTr($date)
    {
        $aylar = array(
            'January' => 'Ocak',
            'February' => 'Şubat',
            'March' => 'Mart',
            'April' => 'Nisan',
            'May' => 'Mayıs',
            'June' => 'Haziran',
            'July' => 'Temmuz',
            'August' => 'Ağustos',
            'September' => 'Eylül',
            'October' => 'Ekim',
            'November' => 'Kasım',
            'December' => 'Aralık',
            'Monday' => 'Pazartesi',
            'Tuesday' => 'Salı',
            'Wednesday' => 'Çarşamba',
            'Thursday' => 'Perşembe',
            'Friday' => 'Cuma',
            'Saturday' => 'Cumartesi',
            'Sunday' => 'Pazar',
            'Jan' => 'Oca',
            'Feb' => 'Şub',
            'Mar' => 'Mar',
            'Apr' => 'Nis',
            'May' => 'May',
            'Jun' => 'Haz',
            'Jul' => 'Tem',
            'Aug' => 'Ağu',
            'Sep' => 'Eyl',
            'Oct' => 'Eki',
            'Nov' => 'Kas',
            'Dec' => 'Ara'

        );
        return strtr($date, $aylar);
    }

    public static function makeWordstoURL($word) {

        $forbiddenLetters=array("ç","ğ","İ","ı","ş","ö","ü","Ü","Ç","Ğ","Ş","Ö","%","*","&","(",")"," - "," ","\r\n");
        $forbiddenLettersReplace=array("c","g","I","i","s","o","u","U","C","G","S","O","","","","","","-","-","");
        return strtolower(str_replace($forbiddenLetters, $forbiddenLettersReplace, $word));
    }
}
