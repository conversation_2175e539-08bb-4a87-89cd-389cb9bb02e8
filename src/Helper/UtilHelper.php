<?php

namespace Wb3\SharedBundle\Helper;

class UtilHelper
{
public  static function getExecTimeInMiliSecond(float $start) : int
    {
        return ceil(round((microtime(true) - $start), 3) * 1000);
    }

    public static function calculatePercent(float $x, float $y)
    {
        return round($x/$y,4) * 100;

    }

    public static function numberFormat(float $mumber)
    {
        return number_format($mumber);

    }

    public static  function isSkuColor(string $skuColor) : bool
    {
        return count(explode(" - ",$skuColor)) == 2;
    }
    public static  function isSVendorStockCode(string $vendorStockCode) : bool
    {
        return count(explode(" - ",$vendorStockCode)) == 3;
    }
    public static function ensureValidUtf8($str) {
        return mb_convert_encoding($str, 'UTF-8', 'UTF-8');
    }

}