<?php

namespace Wb3\SharedBundle\Helper;

class ContentHelper
{
    private array $breadcrumbs = [];
    private array|Object $data = [];


    public function getBreadcrumbs(): array
    {
        return $this->breadcrumbs;
    }

    public function addBreadcrumb(BreadcrumbItemHelper|string $breadcrumbItemHelper): ContentHelper
    {
        if(is_string($breadcrumbItemHelper)){
            $breadcrumbItemHelper = new BreadcrumbItemHelper($breadcrumbItemHelper);
        }
       // $bih->setUrl(); // if url is null, set it to # (default value
        $this->breadcrumbs[] = $breadcrumbItemHelper->toArray();
        return $this;
    }

    public function getData(): object|array
    {
        return $this->data;
    }

    public function setData(object|array $data): ContentHelper
    {
        $this->data = $data;
        return $this;
    }




    public function toArray() : array
    {
        return [
            'breadcrumbs' => $this->getBreadcrumbs(),
            'data' => $this->getData()
        ];
    }
}