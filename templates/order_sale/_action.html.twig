<form method="get">
<div class="d-flex align-items-center overflow-auto">
    <!-- Search -->
    <div class="d-flex flex-wrap gap-4">
        <div class="position-relative w-md-200px flex-grow-1" data-bs-toggle="tooltip" data-bs-placement="top" title="Müşteri adına, stok koduna, sipariş numarası, ürün adı ve barkod">


            <input type="text" data-kt-ecommerce-product-filter="search"
                   class="form-control form-control form-control-solid form-control-sm"
                   placeholder="Arama"  name="q" value="{{ data.filters.q ?? null }}" >

    </div>
    <!-- Search -->

    <!--begin::Order Status-->
    <div class="position-relative w-md-175px flex-grow-1"> <!-- Updated width -->
        <select name="state" data-control="select2" data-allow-clear="true" data-close-on-select="true" data-placeholder="Durum" class="form-select form-select-sm w-md-175px form-select-solid" onchange="this.form.submit()">
            <option value=""></option>
            {% for state in data.states %}
                <option value="{{ state.key }}"
                        {% if(data.filters.state is defined and data.filters.state == state.key) %}selected
                        {% endif %}>
                    {{ state.key | mapOrderStateToTitle }}({{ state.count }}) </option>
            {% endfor %}
        </select>
    </div>
    <!--end::Order Status-->

    <!--begin::Wrapper-->
        <div class="position-relative w-md-175px flex-grow-1"> <!-- Updated width -->
            <select name="brandName" data-control="select2" data-allow-clear="true" data-close-on-select="true" data-placeholder="Marka" class="form-select form-select-sm w-md-175px form-select-solid" onchange="this.form.submit()">
                <option value=""></option>
                {% for b in data.brands %}
                    <option value="{{ b.key[0] }}"
                            {% if(data.filters.brandName is defined and data.filters.brandName == b.key[0]) %}selected
                            {% endif %}>
                        {{ b.key[0] }}({{ b.count }}) </option>
                {% endfor %}
            </select>
        </div>





        <div class="position-relative w-md-175px flex-grow-1">
            <select name="platformTitle" data-control="select2" data-allow-clear="true" data-close-on-select="true" data-placeholder="Platform " class="form-select form-select-sm w-md-175px form-select-solid" onchange="this.form.submit()">
                <option value=""></option>
                {% for o in data.platforms %}
                    <option value="{{ o.key }}"
                            {% if(data.filters.platformTitle is defined and data.filters.platformTitle == o.key) %}selected
                            {% endif %}>
                        {{ o.key }}({{ o.count }})</option>
                {% endfor %}
            </select>
        </div>

        <!--begin::Select-->
        <div class="position-relative w-md-175px flex-grow-1"> <!-- Updated width -->
            <select name="locationName" data-control="select2" data-allow-clear="true" data-close-on-select="true" data-placeholder="Lokasyon" class="form-select form-select-sm w-md-175px form-select-solid" onchange="this.form.submit()">
                <option value=""></option>
                {% for loc in data.locations %}
                    <option value="{{ loc.key[0] }}"
                            {% if(data.filters.locationName is defined and data.filters.locationName == loc.key[0]) %} selected
                            {% endif %}>
                        {{ loc.key[0] }}({{ loc.count }})
                    </option> <span class="badge badge-circle badge-info ms-auto">15</span>
                {% endfor %}
            </select>

        </div>

    <!--begin::Separartor-->
    <div class="bullet bg-secondary h-35px w-1px mx-5"></div>
    <!--end::Separartor-->

    <div class="position-relative w-md-175px flex-grow-1">
        <select  data-control="" data-close-on-select="true" name="sortBy" class="form-select form-select-sm w-md-175px form-select-solid" data-placeholder="Sıralama" onchange="this.form.submit()">
            {% for key, label in data.sortByOptions %}
                <option value="{{ key }}"
                        {% if data.filters.sortBy is defined and data.filters.sortBy == key %} selected {% endif %}>
                    {{ label }}
                </option>
            {% endfor %}
        </select>
    </div>
        <!--end::Select-->
        <!--begin::Actions-->
        <div class="d-flex align-items-center ms-3">
            <a href="{{ path('app_ordersale_index') }}" class="btn btn-sm btn-icon btn-light" data-bs-toggle="tooltip" data-bs-placement="top" aria-label="Enable row view" data-bs-original-title="Enable row view" data-kt-initialized="1">
                <i class="ki-duotone ki-minus">
                </i>
            </a>
        </div>
        <!--end::Actions-->
    </div>
    <!--end::Wrapper-->
</div>
</form>