<div class="table-responsive ">
    <table class="table  table-striped table-row-bordered align-middle" id="kt_ecommerce_sales_table">
        <thead>
        <tr class="text-start text-gray-500 fw-bold fs-8 text-uppercase gs-0" role="row">
            <th class="min-w-200px dt-type-numeric dt-orderable-asc dt-orderable-desc">
                <span class="dt-column-title" role="button">Si<PERSON><PERSON>ş</span>
            </th>
            <th class="min-w-125px dt-type-numeric dt-orderable-asc dt-orderable-desc">
                <span class="dt-column-title" role="button">Platform</span>
            </th>

            <th class="min-w-125px dt-type-numeric dt-orderable-asc dt-orderable-desc">
                <span class="dt-column-title" role="button">Müşteri Adı</span>
            </th>
            <th class="min-w-125px dt-type-numeric dt-orderable-asc dt-orderable-desc">
                <span class="dt-column-title" role="button">Lokasyon</span>
            </th>

            <th class="min-w-150px dt-type-numeric dt-orderable-asc dt-orderable-desc" colspan="2">
                <span class="dt-column-title" role="button">Ürün Bilgisi</span>
            </th>
            <th class="w-75px  d-flex" data-dt-column="7" rowspan="1" colspan="1" aria-label="İşlem">
                <span class="dt-column-title">İşlem</span>
            </th>
        </tr>
        </thead>
<!--end::Table head-->
        {%  if data.pagination.items | length %}
        {% for o in  data.pagination.items %}
            {% include 'order_sale/_table_order.html.twig' %}
        {% endfor %}
        {% else %}
        <tr>
            <td colspan="8">
                {% include'@SharedBundle/snipped/item_not_found.html.twig' %}
            </td>
        </tr>
        {% endif %}

    </table>

</div>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        document.querySelectorAll('.ki-copy-success').forEach(function (icon) {
            icon.addEventListener('click', function (e) {
                e.preventDefault();

                const textToCopy = this.getAttribute('data-copy');
                if (!textToCopy) return;

                // Kopyalama işlemi
                function copyText() {
                    if (navigator.clipboard && window.isSecureContext) {
                        return navigator.clipboard.writeText(textToCopy);
                    } else {
                        const textarea = document.createElement('textarea');
                        textarea.value = textToCopy;
                        textarea.style.position = 'fixed';
                        textarea.style.opacity = '0';
                        document.body.appendChild(textarea);
                        textarea.focus();
                        textarea.select();
                        try {
                            document.execCommand('copy');
                            document.body.removeChild(textarea);
                            return Promise.resolve();
                        } catch (err) {
                            document.body.removeChild(textarea);
                            return Promise.reject();
                        }
                    }
                }

                // Toast'ı göster
                function showToast(iconElement) {
                    const toast = document.createElement('div');
                    toast.textContent = 'Kopyalandı';
                    toast.style.position = 'absolute';
                    toast.style.background = '#333';
                    toast.style.color = '#fff';
                    toast.style.padding = '4px 8px';
                    toast.style.borderRadius = '4px';
                    toast.style.fontSize = '12px';
                    toast.style.top = '-24px';
                    toast.style.left = '0';
                    toast.style.whiteSpace = 'nowrap';
                    toast.style.zIndex = '9999';
                    toast.style.opacity = '0';
                    toast.style.transition = 'opacity 0.3s';

                    iconElement.style.position = 'relative';
                    iconElement.appendChild(toast);

                    requestAnimationFrame(() => {
                        toast.style.opacity = '1';
                    });

                    setTimeout(() => {
                        toast.style.opacity = '0';
                        setTimeout(() => {
                            iconElement.removeChild(toast);
                        }, 300);
                    }, 1500);
                }

                copyText()
                    .then(() => showToast(this))
                    .catch(() => console.error('Kopyalama başarısız'));
            });
        });
    });
</script>






