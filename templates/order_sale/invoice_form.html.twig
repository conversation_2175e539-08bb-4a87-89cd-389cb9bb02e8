{% extends '@SharedBundle/base/base_iframe.html.twig' %}
{% block body %}
    <div>
        <div class="row">
            <div class="col-md-4 text-end">
                {% for oi in data.order.items %}
                    {# @var oi \Wb3\SharedBundle\Entity\OrderItem #}
                    <a class="d-block overlay" data-fslightbox="lightbox-basic"
                       href="{{ oi.productVariant.product.images[0].url }}">
                        <img src="{{ oi.productVariant.product.images[0].url }}" width="100"/>
                        <div class="overlay-layer card-rounded bg-dark bg-opacity-25 shadow min-w-50px min-h-50px">
                            <i class="bi bi-eye-fill text-white fs-3x"></i>
                        </div>
                    </a>
                {% endfor %}
            </div>
            <div class="col-md-8">
                <div class="border p-4 rounded-3">
                    <h2>{{ data.order.customer.name }}</h2>
                    <h4>#{{ data.order.orderNumber }}</h4>
                    <b>{{ data.order.orderAt|date('d F Y H:i') }}</b>
                </div>
                <div class="border p-4 rounded-3">
                    {% for oi in data.order.items %}
                        {# @var oi \Wb3\SharedBundle\Entity\OrderItem #}
                        <p>{{ oi.productVariant.product.title }}</p>
                        <p class="fs-5 text-primary">{{ oi.price }}₺</p>
                    {% endfor %}
                </div>
            </div>
        </div>
        <div>
            {{ form_start(data.form) }}
            <div class="mb-3"> {# Added a div for better grouping of label, warning, and input #}
                {{ form_label(data.form.address) }}
                <div id="address-warning" class="text-danger mb-2 warning-blink fs-5 fw-bolder" style="display: none;"></div>
                {{ form_widget(data.form.address) }}
                <small id="address-char-count" class="form-text text-muted"></small>
            </div>
            <div class="row">
                <div class="col-md-4">
                    {{ form_row(data.form.town) }}
                </div>
                <div class="col-md-4">
                    {{ form_row(data.form.city) }}
                </div>
                <div class="col-md-4">
                    {{ form_row(data.form.phone) }}
                </div>
            </div>
            {# We need to make sure the submit button has an ID if we want to directly target it for disabling #}
            {{ form_row(data.form.submit, {'attr': {'id': 'form-submit-button'}}) }}
            {{ form_end(data.form) }}
        </div>
        <div class="modal-footer">
            <div class="d-flex justify-content-start">
                <img src="{{ asset('bundles/shared/assets/images/platform/'~ data.order.platform.code ~'.png') }}" height="50"/>
            </div>
            <button type="button" class="btn btn-light btn-active-danger" data-bs-dismiss="modal">Kapat</button>
            <a href="{{ path('app_ordersale_invoice',{'order': data.order.id}) }}" class="btn btn-success invoice-button" id="invoice-button" style="width:180px;">
                <span class="indicator-label">
                    Faturalandır
                </span>
                <span class="indicator-progress">
                    Faturalandırılıyor <span class="spinner-border spinner-border-sm align-right ms-2"></span>
                </span>
            </a>
        </div>
    </div>
{% endblock %}

{% block javascripts %}
    {{ parent() }} {# Important: This ensures any parent JavaScripts are still included #}
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const invoiceButton = document.getElementById('invoice-button');
            const formSubmitButton = document.getElementById('form-submit-button');
            const addressInput = document.getElementById('order_sale_invoice_form_address');
            const addressCharCount = document.getElementById('address-char-count');
            const addressWarning = document.getElementById('address-warning');

            const minLength = 10;
            const maxLength = 120;
            let isLocked = false; // Butonlar kilitliyse true olur

            function toggleButtonsState(isValid) {
                if (isLocked) {
                    // Eğer kilitliyse asla etkinleştirme
                    if (invoiceButton) {
                        invoiceButton.style.opacity = '0.5';
                        invoiceButton.style.pointerEvents = 'none';
                    }
                    if (formSubmitButton) {
                        formSubmitButton.style.opacity = '0.5';
                        formSubmitButton.style.pointerEvents = 'none';
                    }
                    return;
                }

                if (invoiceButton) {
                    invoiceButton.style.opacity = isValid ? '1' : '0.5';
                    invoiceButton.style.pointerEvents = isValid ? 'auto' : 'none';
                }
                if (formSubmitButton) {
                    formSubmitButton.style.opacity = isValid ? '1' : '0.5';
                    formSubmitButton.style.pointerEvents = isValid ? 'auto' : 'none';
                }
            }

            function updateAddressInfo() {
                const currentLength = addressInput.value.length;
                addressCharCount.textContent = `${currentLength}/${maxLength} karakter`;

                addressWarning.style.display = 'none';
                let isValid = true;

                if (currentLength > maxLength) {
                    const extraChars = currentLength - maxLength;
                    addressWarning.textContent = `Adreste ${extraChars} fazladan karakter bulunuyor. Lütfen adresi düzenleyiniz. Düzenledikten sonra KAYDET butonuna basmayı unutmayınız! `;
                    addressWarning.style.display = 'block';
                    isValid = false;
                    isLocked = true; // Karakter fazlaysa kilitle
                } else if (currentLength < minLength && currentLength > 0) {
                    const missingChars = minLength - currentLength;
                    addressWarning.textContent = `Adreste ${missingChars} eksik karakter bulunuyor. Lütfen adresi düzenleyiniz!!!!`;
                    addressWarning.style.display = 'block';
                    isValid = false;
                } else if (currentLength === 0) {
                    isValid = false;
                }

                toggleButtonsState(isValid);
            }

            // Sayfa açıldığında kontrol et
            if (addressInput) {
                updateAddressInfo();
                addressInput.addEventListener('input', updateAddressInfo);
            }

            if (invoiceButton) {
                invoiceButton.addEventListener('click', function() {
                    this.setAttribute('data-kt-indicator', 'on');
                });
            }
        });
    </script>
{% endblock %}