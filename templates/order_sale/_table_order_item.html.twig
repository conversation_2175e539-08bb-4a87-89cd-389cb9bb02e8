{% if(orderItem.productVariant is not null) %}
<table class="w-100" border="0">
    <tr>
        <td width="15%">
            <div class="d-flex align-orderItem.-center">
                <div class="ms-5">
                    {{ orderItem.location.name ?? 'Bilinmiyor' }}
                </div>
            </div>
        </td>
        <td>
            <table id="orderItem" border="0" style="padding: 5px;">
                <tbody>
                <tr>
                    <td rowspan="2" class="p-3">
                        {% if orderItem.productVariant.product.images | first %}
                                <a href="{{ (orderItem.productVariant.product.images | first).url }}" target="_blank">
                                    <img src="{{ (orderItem.productVariant.product.images | first).url }}" class="rounded w-100px" />
                                </a>
                            {% else  %}
                            <img src="{{ asset('bundles/shared/assets/images/no-image.webp') }}" class="rounded w-100px" />
                        {% endif %}
                    </td>
                </tr>
                <tr>
                    <td>
                        <table>
                            <tbody>
                            <tr>
                                <td colspan="2">
                                    <a href="{{ pim_base_url ~ '/product/' ~ orderItem.productVariant.product.id }}"
                                       class="text-gray-800 text-hover-primary">
                                        {{ orderItem.productVariant.product.title }}
                                    </a>
                                    <i class="ki-duotone ki-copy-success" data-copy="{{ orderItem.productVariant.product.title }}" style="cursor:pointer;">
                                        <span class="path1"></span>
                                        <span class="path2"></span>
                                    </i>

                                </td>
                            </tr>
                            <tr>
                                <td colspan="2"><b>Stok Kodu:</b>
                                    <a href="{{ mpi_base_url ~ '/platform-product-variant/?q=' ~ orderItem.productVariant.stockCode }}"
                                       class="text-gray-800 text-hover-primary ">
                                        {{orderItem.productVariant.stockCode}}
                                    </a>
                                    <i class="ki-duotone ki-copy-success" data-copy="{{orderItem.productVariant.stockCode}}" style="cursor:pointer;">
                                        <span class="path1"></span>
                                        <span class="path2"></span>
                                    </i>
                                </td>

                            </tr>
                            <tr>
                                <td><b>Beden:</b> {{ orderItem.productVariant.size }} Numara</td>
                                <td><b>Renk:</b> {{ orderItem.color }}</td>
                            </tr>
                            <tr>
                                <td><b>Marka:</b> {{ orderItem.productVariant.product.brand.name ?? '-' }}</td>
                                <td><b>Sezon:</b> {{ orderItem.season }}</td>
                            </tr>
                            <tr>
                                <td>
                                    <b>Durum:</b>
                                     {% if orderItem.state == 'out_of_stock' %}
                                        <a class="badge badge-danger" href="http://cokyakinda.com">{{ orderItem.state | mapOrderItemStateToTitle }}</a><a href=""> Bildir</a>
                                     {% elseif orderItem.state == 'price_error' %}
                                         <a target="_blank" href="{{ path('app_ordercheck_index') }}?filters[q]={{ orderItem.order.orderNumber }}"  class="badge badge-warning">{{ orderItem.state | mapOrderItemStateToTitle }}</a>
                                         <a href="{{ path('app_ordersale_notifypriceerror',{'orderItem': orderItem.id}) }}"><i class="ki-solid ki-send fs-2"></i>Fiyat Hatası Bildir</a>
                                    {% else %}
                                        {% if orderItem.state == 'ready' %}
                                            <a class="badge badge-light-primary">{{ orderItem.state | mapOrderItemStateToTitle }}</a>
                                        {% elseif orderItem.state == 'transferred' %}
                                            <a class="badge badge-light-info">{{ orderItem.state | mapOrderItemStateToTitle }}</a>
                                        {% elseif orderItem.state == 'location_defined' %}
                                            <a class="badge badge-light-secondary">{{ orderItem.state | mapOrderItemStateToTitle }}</a>
                                        {% elseif orderItem.state == 'customer_canceled' %}
                                            <a class="badge badge-light">{{ orderItem.state | mapOrderItemStateToTitle }}</a>
                                        {% endif %}
                                    {% endif %}
                                </td>
                            </tr>

                            </tbody>
                        </table>
                    </td>
                </tr>
                </tbody>
            </table>
        </td>

        <div>
            <td>
                {% if orderItem.order.giftMessage %}
                <a class="btn btn-icon btn-warning" data-bs-toggle="popover" data-bs-placement="bottom" data-bs-content="{{ orderItem.order.giftMessage}}"><i class="ki-solid ki-gift fs-1"></i></a>
                {% endif %}
            </td>
        </div>

        <div class="ms-5">
            <td class="text-center dt-type-numeric">
                <span id="psf">{{ orderItem.price | number_format(2, '.', ',') }} ₺</span>
            </td>
        </div>
    </tr>
</table>
{% else %}
    <p class="alert alert-danger">Bu sipariş kalemi için ürün bulunamadı. Siparişi kontrol edin! (orderId: {{ orderItem.order.id }} orderItemId: {{orderItem.id}})</p>
{% endif %}
