{% set is_price_error = false %}
    {% for item in o.items %}
        {% if item.state == 'price_error' or item.state == 'price_error_notified' %}
            {% set is_price_error = true %}
        {% endif %}
    {% endfor %}

{% if  workflow_has_marked_place(o,'new') %}
    {% if is_price_error  %}
            <a href="{{ path('app_ordercheck_index')}}?filters[q]={{ o.orderNumber}}" class="badge badge-warning">Fiyat Hatası</a>
        {% else %}
            <a href="#" data-href="{{ path('app_ordersale_invoiceform',{'order': o.id}) }}"
               type="button"
               class="open-invoice-modal btn {% if hasReadyItem %}btn-primary{% else %}btn-success{% endif %} h-45px border-2"
               style="width:105px;">
                Satış Yap
            </a>
    {% endif %}
{% endif %}


{% if  workflow_has_marked_place(o,'waiting_payment') %}
    <a class="badge py-5 px-4 fs-7 badge-warning">Ödeme bekleniyor</a>
{% endif %}
{% if  workflow_has_marked_place(o,'customer_cancel_requested') %}
    Müşteri iptal isteği gönderdi.
{% endif %}
{% if workflow_has_marked_place(o,'delivered') %}
    Teslim Edildi
{% endif %}
{% if  workflow_has_marked_place(o,'undelivered') %}
    Teslim Edilemedi
{% endif %}
{% if  workflow_has_marked_place(o,'vendor_canceled') %}
    Satıcı İptal Etti
{% endif %}
{% if  workflow_has_marked_place(o,'customer_canceled') %}
    Müşteri İptal Etti
{% endif %}
{% if  workflow_has_marked_place(o,'in_cargo') %}
    Kargoda
{% endif %}

{% if workflow_has_marked_place(o, 'invoiced') %}
    <a href="#"
       class="btn btn-warning h-45px border-2 open-cargo-modal"
       data-url="{{ path('app_ordersale_cargoform', {'order': o.id}) }}"
       style="width:105px;">
        Kargola
    </a>
{% endif %}


<!-- Satış Modal -->
<div class="modal fade" id="invoiceModal" tabindex="-1" aria-labelledby="invoiceModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="invoiceModalLabel">Satış Formu</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Kapat"></button>
            </div>
            <div class="modal-body" style="height: 80vh;">
                <iframe id="invoiceIframe" src="" width="100%" height="100%" frameborder="0"></iframe>
            </div>
        </div>
    </div>
</div>

<!-- Kargo Modal -->
<div class="modal fade" id="cargoModal" tabindex="-1" aria-labelledby="cargoModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="cargoModalLabel">Kargo Formu</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Kapat"></button>
            </div>
            <div class="modal-body" style="height: 80vh;">
                <iframe id="cargoIframe" src="" width="100%" height="100%" frameborder="0"></iframe>
            </div>
        </div>
    </div>
</div>

<script>
    // Sayfa yüklendiğinde mevcut event listener'ları temizle
    $(document).ready(function() {
        // Önceki event listener'ları kaldır
        $('#invoiceModal').off('hidden.bs.modal');
        
        // Satış Modal
        $(document).on('click', '.open-invoice-modal', function(e) {
            e.preventDefault();
            // Göstergeyi aktif et
            $(this).attr("data-kt-indicator", "on");

            const url = $(this).data('href');
            $('#invoiceIframe').attr('src', url);
            $('#invoiceModal').modal('show');

            // Modal gösterildikten sonra göstergeyi kapat
            $('#invoiceModal').on('shown.bs.modal', function () {
                $('.open-invoice-modal').removeAttr("data-kt-indicator");
            });
        });

        // Modal kapatıldığında
        $('#invoiceModal').on('hidden.bs.modal', function () {
            console.log('Modal kapatıldı, sayfa yenileniyor...');
            $('#invoiceIframe').attr('src', '');
            // Eğer hala aktifse göstergeyi kapat
            $('.open-invoice-modal').removeAttr("data-kt-indicator");
            // Modal kapatıldığında sayfayı yenile
            setTimeout(function() {
                window.location.reload(true);
            }, 100);
        });

        // Kargo Modal
        $(document).on('click', '.open-cargo-modal', function(e) {
            e.preventDefault();
            const url = $(this).data('url');
            $('#cargoIframe').attr('src', url);
            $('#cargoModal').modal('show');
        });

        $('#cargoModal').on('hidden.bs.modal', function () {
            $('#cargoIframe').attr('src', '');
        });
    });
</script>







<div class="modal fade" id="invoiceModal" tabindex="-1" aria-labelledby="invoiceModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl"> <!-- modal-xl ile geniş modal -->
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="invoiceModalLabel">Satış Formu</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Kapat"></button>
            </div>
            <div class="modal-body" style="height: 80vh;">
                <iframe id="invoiceIframe" src="" width="100%" height="100%" frameborder="0"></iframe>
            </div>
        </div>
    </div>
</div>


<div class="modal fade" id="cargoModal" tabindex="-1" aria-labelledby="cargoModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="cargoModalLabel">Kargo Formu</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Kapat"></button>
            </div>
            <div class="modal-body" style="height: 80vh;">
                <iframe id="cargoIframe" src="" width="100%" height="100%" frameborder="0"></iframe>
            </div>
        </div>
    </div>
</div>

<script>
    // Satış Modal
    $(document).on('click', '.open-invoice-modal', function(e) {
        e.preventDefault();
        const url = $(this).data('href');
        $('#invoiceIframe').attr('src', url);
        $('#invoiceModal').modal('show');
    });

    $('#invoiceModal').on('hidden.bs.modal', function () {
        $('#invoiceIframe').attr('src', '');
    });

    // Kargo Modal
    $(document).on('click', '.open-cargo-modal', function(e) {
        e.preventDefault();
        const url = $(this).data('url');
        $('#cargoIframe').attr('src', url);
        $('#cargoModal').modal('show');
    });

    $('#cargoModal').on('hidden.bs.modal', function () {
        $('#cargoIframe').attr('src', '');
    });
</script>