{# @var check \Wb3\SharedBundle\Entity\OrderItemCheck #}
{% set diffPrice = check.item.price - check.item.vendorPrice %}
{% set diffPercent = check.item.vendorPrice > 0 ? (diffPrice / check.item.vendorPrice) * 100 : 100 %}
<tr class="text-end">
    <td>
        <input type="checkbox" class="row-checkbox form-check-input" data-order-item-check-id="{{ check.id }}">
    </td>
    <td class="text-muted">{{ check.id }}</td>
    <td class="bg-{{ diffPrice > 0 ? 'success' : 'danger' }} text-white">{{ diffPrice | formatPrice }}</td>
    <td class="text-{{ diffPrice > 0 ? 'success' : 'danger' }}">%{{ diffPercent | formatPrice}}</td>
    <td>{{ check.item.platformDiscount | formatPrice }}</td>
    <td>{{ check.item.vendorDiscount | formatPrice }}</td>
    <td>{{ check.item.vendorPrice | formatPrice }}</td>
    <td>{{ check.item.price | formatPrice }}</td>
    <td>{{ check.createdAt |date('d.m.Y H:i:s') }}</td>
    <td class="text-start">
        <a href="{{ mpi_base_url ~ '/platform-product-variant/?q=' ~ check.item.productVariant.stockCode }}"
           class="text-gray-600 text-hover-primary ">
            {{ check.item.productVariant.stockCode }}
        </a>
        <i class="ki-duotone ki-copy-success" data-copy="{{ check.item.productVariant.stockCode }}" style="cursor:pointer;">
            <span class="path1"></span>
            <span class="path2"></span>
        </i></td>
    <td class="text-start w-150px"> {# Added px-3 here #}
        <a href="{{ sos_base_url ~ '/order/' ~ check.item.order.id ~'/detail'}}"
           class="text-gray-600 text-hover-primary ">
            {{ check.item.order.orderNumber }}
        </a>
        <i class="ki-duotone ki-copy-success" data-copy="{{ check.item.order.orderNumber}}" style="cursor:pointer;">
            <span class="path1"></span>
            <span class="path2"></span>
        </i>
    </td>
        {% if (check.item.campaignItem ) %}
            <td class="bg-success text-white text-start">
                <a href="{{ promo_base_url ~ '/campaign/' ~ check.item.campaignItem.campaign.id }}"
                   class="text-white text-hover-primary">
                    <span class="badge badge-info">{{ check.item.campaignItem.price ~ " TL"  }}</span> {{ check.item.campaignItem.campaign.name }}
                </a>
            </td>
            <td>{{ check.item.campaignItem.campaign.type.name }}</td>
        {% else %}
            <td>-</td>
            <td>-</td>
        {% endif %}
    <td>
        <img src="{{ asset('bundles/shared/assets/images/platform/'~ check.item.order.platform.code ~'.png') }}" class="w-50px" alt="Platform Image">
    </td>
    <td>
        <div class="btn-group">
            <button data-order-item-check-id="{{ check.id }}" class="approveButton btn btn-success w-120px h-30px d-flex justify-content-center align-items-center " type="button">
                Onayla
            </button>
            <button class="btn btn-secondary dropdown-toggle dropdown-toggle-split w-40px h-30px d-flex justify-content-center align-items-center" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                <span class="visually-hidden">Toggle Dropdown</span>
            </button>
            <ul class="dropdown-menu">
                <li>
                    <a class="dropdown-item notifyPriceErrorButton" data-order-item-id="{{ check.item.id }}" type="button" id="">Fiyat Hatası Bildir</a>
                </li>
            </ul>
        </div>
    </td>
</tr>