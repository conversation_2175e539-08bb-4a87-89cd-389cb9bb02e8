{# @var check \Wb3\SharedBundle\Entity\OrderItemCheck #}
{% set diffPrice = check.item.price - check.item.vendorPrice %}
{% set diffPercent = check.item.vendorPrice > 0 ? (diffPrice / check.item.vendorPrice) * 100 : 100 %}
<tr class="text-end">
    <td class="text-muted">{{ check.id }}</td>
    <td class="bg-{{ diffPrice > 0 ? 'success' : 'danger' }} text-white">{{ diffPrice | formatPrice }}</td>
    <td class="text-{{ diffPrice > 0 ? 'success' : 'danger' }}">%{{ diffPercent | formatPrice}}</td>
    <td>{{ check.item.platformDiscount | formatPrice }}</td>
    <td>{{ check.item.vendorDiscount | formatPrice }}</td>
    <td>{{ check.item.vendorPrice | formatPrice }}</td>
    <td>{{ check.item.price | formatPrice }}</td>
    <td>{{ check.createdAt |date('d.m.Y H:i') }}</td>
    <td class="text-start">{{ check.item.productVariant.stockCode }}</td>
    <td class="text-start">{{ check.item.order.orderNumber }}</td>
        {% if (check.item.campaignItem ) %}
            <td class="bg-success text-white">
                <span class="badge badge-info">{{ check.item.campaignItem.price ~ " TL"  }}</span> {{ check.item.campaignItem.campaign.name }}
            </td>
            <td>{{ check.item.campaignItem.campaign.type.name }}</td>
        {% else %}
            <td>-</td>
            <td>-</td>
        {% endif %}
    <td>
        <img src="{{ asset('bundles/shared/assets/images/platform/'~ check.item.order.platform.code ~'.png') }}" class="w-50px" alt="Platform Image">
    </td>
    <td>
        <div class="btn-group">
            <button data-order-item-check-id="{{ check.id }}" class="approveButton btn btn-success w-120px h-30px d-flex justify-content-center align-items-center " type="button">
                Onayla
            </button>
            <button class="btn btn-secondary dropdown-toggle dropdown-toggle-split w-40px h-30px d-flex justify-content-center align-items-center" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                <span class="visually-hidden">Toggle Dropdown</span>
            </button>
            <ul class="dropdown-menu">
                <li>
                    <a class="dropdown-item notifyPriceErrorButton" data-order-item-id="{{ check.item.id }}" type="button" id="">Fiyat Hatası Bildir</a>
                </li>
            </ul>
        </div>
    </td>
</tr>