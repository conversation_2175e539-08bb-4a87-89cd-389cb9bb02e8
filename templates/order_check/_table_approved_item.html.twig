{# @var check \Wb3\SharedBundle\Entity\OrderItemCheck #}
<tr>
    <td><a href="{{ sos_base_url ~ '/order/' ~ check.item.order.id ~'/detail'}}"
           class="text-gray-600 text-hover-primary ">
            {{ check.item.order.orderNumber }}
        </a>
        <i class="ki-duotone ki-copy-success" data-copy="{{ check.item.order.orderNumber}}" style="cursor:pointer;">
            <span class="path1"></span>
            <span class="path2"></span>
        </i></td>
    <td>{{ check.item.price | formatPrice }} ₺</td>
    <td><img src="{{ asset('bundles/shared/assets/images/platform/'~ check.item.order.platform.code ~'.png') }}" class="w-120px h-30px"></td>
    <td>{{ check.approvedAt | date('d.m.Y H:i:s') }}</td>
    <td>
        {{ check.user.firstName ~ " " ~ check.user.lastName }}</td>
    <td class="text-start"> {# Buraya text-start eklendi #}
        {% if check.item.campaignItem %}
            <a href="{{ promo_base_url ~ '/campaign/' ~ check.item.campaignItem.campaign.id }}"
               class="text-gray-600 text-hover-primary">
                <span class="badge badge-info">#{{ check.item.campaignItem.id }}</span> {{ check.item.campaignItem.campaign.name }}
            </a>
        {% else %}
            -
        {% endif %}
    </td>
    <td class="text-start">{{ check.description }}</td>
</tr>