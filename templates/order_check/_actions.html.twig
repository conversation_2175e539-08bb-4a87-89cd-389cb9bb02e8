<form method="GET">
    <div class="d-flex align-items-center overflow-auto">
        <!-- Search -->
        <div class="d-flex align-items-center overflow-auto">
            <div data-bs-toggle="tooltip" data-bs-placement="top" title="<PERSON>üş<PERSON><PERSON> adına, stok koduna, sipariş numarası, ürün adı ve barkod">


                <input type="text" data-kt-ecommerce-product-filter="search"
                       class="form-control form-control form-control-solid form-control-sm"
                       placeholder="Sipariş Numarası"  name="filters[q]" value="{{ data.filters.q ?? null }}" />

            </div>
        </div>
            <!-- Search -->
        <!-- dropdownmenu.html.twig -->
        <div class="position-relative w-md-140px flex-grow-1 m-5"> <!-- Updated width -->
        <label>
            <select class="form-select form-control form-select-sm" name="filters[platformId]" onchange="this.form.submit()">
                <option value="">Platform Seçiniz</option>
                {% for p in data.platforms %}
                    {# @var p \Wb3\SharedBundle\Entity\Platform #}
                    <option value="{{ p.id }}" {% if(data.filters.platformId is defined and data.filters.platformId == p.id) %} selected="selected" {% endif %}>{{ p.title }}</option>
                {% endfor %}
            </select>
        </label>
    </div>
    </div>
</form>