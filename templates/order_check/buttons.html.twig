<div class="d-flex justify-content-between align-items-center mb-3">
    <!-- Tab <PERSON>ı -->
    <div class="d-flex align-items-center">
        <div class="d-flex align-items-center position-relative tab-button" data-tab="1">
            <button type="button" class="btn btn-link text-decoration-none text-dark d-flex align-items-center ps-0 active" data-target="#table-aksiyon-bekleyenler">
                <span class="badge bg-info text-white position-relative me-2 p-2 rounded-circle" style="left: 0;">{{ data.checkPagination.items | length }}</span>
                <span class="fw-bold">Aksiyon Bekleyenler</span>
            </button>
            <div class="tab-indicator bg-primary"></div>
        </div>

        <div class="d-flex align-items-center ms-4 position-relative tab-button" data-tab="2">
            <button type="button" class="btn btn-link text-decoration-none text-dark d-flex align-items-center" data-target="#table-onaylananlar">
                <div class="symbol symbol-30px symbol-circle me-3">
                <span class="symbol-label bg-success">
                    <i class="ki-solid ki-check fs-2 text-white">
                        <span class="path1"></span>
                        <span class="path2"></span>
                    </i>
                </span>
                </div>
                <span>Onaylananlar</span>
            </button>
            <div class="tab-indicator bg-primary"></div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const tabButtons = document.querySelectorAll('.tab-button');

            // Initialize indicators' base styles and hide them
            tabButtons.forEach(tabButton => {
                const indicator = tabButton.querySelector('.tab-indicator');
                if (indicator) {
                    indicator.style.position = 'absolute';
                    indicator.style.bottom = '0';
                    indicator.style.left = '0';
                    indicator.style.width = '100%';
                    indicator.style.height = '3px'; // Thickness of the line
                    indicator.style.transform = 'scaleX(0)'; // Hidden by default
                    indicator.style.transformOrigin = 'bottom left';
                    indicator.style.transition = 'transform 0.3s ease, opacity 0.3s ease';
                    indicator.style.opacity = '0'; // Hidden by default
                }
            });

            // Set initial active state for the first tab
            const initialActiveButton = document.querySelector('.tab-button button.active');
            if (initialActiveButton) {
                const initialIndicator = initialActiveButton.closest('.tab-button').querySelector('.tab-indicator');
                if (initialIndicator) {
                    initialIndicator.style.transform = 'scaleX(1)';
                    initialIndicator.style.opacity = '1'; // Fully opaque for active
                }
            }

            tabButtons.forEach(tabButton => {
                const button = tabButton.querySelector('button');
                const indicator = tabButton.querySelector('.tab-indicator');

                // Click event for tab activation
                button.addEventListener('click', function() {
                    // Deactivate all tabs and hide their indicators
                    tabButtons.forEach(btn => {
                        btn.querySelector('button').classList.remove('active');
                        const otherIndicator = btn.querySelector('.tab-indicator');
                        if (otherIndicator) {
                            otherIndicator.style.transform = 'scaleX(0)';
                            otherIndicator.style.opacity = '0';
                        }
                    });

                    // Activate the clicked tab and show its indicator
                    this.classList.add('active');
                    if (indicator) {
                        indicator.style.transform = 'scaleX(1)';
                        indicator.style.opacity = '1'; // Fully opaque when active
                    }

                    // *** Add your logic here to show/hide the corresponding tab content ***
                    // const targetId = this.dataset.target;
                    // document.querySelectorAll('.tab-pane').forEach(pane => pane.classList.remove('active', 'show'));
                    // document.querySelector(targetId).classList.add('active', 'show');
                });

                // Hover effects
                tabButton.addEventListener('mouseenter', function() {
                    // Only apply hover effect if the tab is NOT currently active
                    if (!button.classList.contains('active') && indicator) {
                        indicator.style.transform = 'scaleX(1)';
                        indicator.style.opacity = '0.5'; // Transparent (50% opacity) for hover
                    }
                });

                tabButton.addEventListener('mouseleave', function() {
                    // Only hide hover effect if the tab is NOT currently active
                    if (!button.classList.contains('active') && indicator) {
                        indicator.style.transform = 'scaleX(0)';
                        indicator.style.opacity = '0';
                    }
                });
            });
        });
    </script>

    <!-- Seçili Siparişleri Onayla Butonu -->
    <div>
        <button id="bulk-approve-button" class="btn btn-primary">Seçili Siparişleri Onayla</button>
    </div>
</div>
