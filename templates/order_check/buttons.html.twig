<!-- Butonlar -->
<div class="d-flex justify-content-start align-items-center mb-3">
    <div class="d-flex align-items-center position-relative tab-button" data-tab="1">
        <button type="button" class="btn btn-link text-decoration-none text-dark d-flex align-items-center ps-0 active" data-target="#table-aksiyon-be<PERSON><PERSON>ler">
            <span class="badge bg-info text-white position-relative me-2 p-2 rounded-circle" style="left: 0;">{{ data.checkPagination.items | length }}</span>
            <span class="fw-bold">Aks<PERSON><PERSON> Bekleyenler</span>
        </button>
        <div class="active-indicator bg-primary" style="height: 3px; width: 100%; position: absolute; bottom: 0;"></div>
    </div>
    <div class="d-flex align-items-center ms-4 position-relative tab-button" data-tab="2">
        <button type="button" class="btn btn-link text-decoration-none text-dark d-flex align-items-center" data-target="#table-onaylananlar">
            <div class="symbol symbol-30px symbol-circle me-3">
				<span class="symbol-label bg-success">
						<i class="ki-solid ki-check fs-2 text-white">
							<span class="path1"></span>
							<span class="path2"></span>
						</i>
				</span>
            </div>
            <span>Onaylananlar</span>
        </button>
        <div class="hover-indicator bg-primary-opacity" style="height: 3px; width: 100%; position: absolute; bottom: 0;"></div>
    </div>
</div>

<style>
    .tab-button .active {
        font-weight: bold;
        color: #0d6efd; /* Metronic'in birincil rengi */
    }

    .tab-button .active + .active-indicator {
        background-color: #0d6efd;
    }

    .tab-button .hover-indicator,
    .tab-button .active-indicator {
        height: 3px;
        width: 100%;
        position: absolute;
        bottom: 0;
        background-color: transparent;
    }

    .tab-button .bg-primary-opacity {
        background-color: rgba(13, 110, 253, 0.25); /* Hafif opak bir arka plan rengi */
    }

    .tab-button .bg-primary {
        background-color: #0d6efd; /* Aktif olan tuşun altındaki çizgi */
    }
</style>

<!-- JavaScript Kodu -->
<script>
    document.querySelectorAll('.tab-button button').forEach(button => {
        button.addEventListener('click', function() {
            document.querySelectorAll('.tab-button button').forEach(btn => {
                btn.classList.remove('active');
                btn.nextElementSibling.classList.remove('bg-primary');
            });
            this.classList.add('active');
            this.nextElementSibling.classList.add('bg-primary');
        });
    });
</script>