<div class="d-flex justify-content-between">

    <!-- Table Wrapper with <PERSON>rollbar -->
    <div class="table-wrapper">
        <table id="table-aksiyon-bekleyenler" class="table table-responsive align-middle table-row-dashed fs-6 gy-5 dataTable no-footer" style="width: 100%; margin: auto;">
            <thead>
            <tr class="text-start text-gray-500 fw-bold fs-7 text-uppercase gs-0">
                <th>
                    <input type="checkbox" id="select-all-checkbox" class="form-check-input">
                </th>
                <th>#</th>
                <th class="text-center">Fiyat Fark</th>
                <th class="text-center">İndirim Oranı</th>
                <th class="text-center">Platform İndirimi</th>
                <th class="text-center">Satıcı İndirimi</th>
                <th class="text-center">Satıcı Fiyatı</th>
                <th class="text-center">Platform Satış Fiyatı</th>
                <th class="text-center">Tarih</th>
                <th class="text-center">Stok Kodu</th>
                <th class="text-center">Sipariş No</th>
                <th class="text-center">Kampanya Var mı?</th>
                <th class="text-center">Kampanya türü</th>
                <th class="text-center">Platform</th>
                <th class="text-center">Aksiyonlar</th>
            </tr>
            </thead>
            <tbody class="fw-semibold text-gray-600 text-center">
            {%  if data.checkPagination.items %}
                {% for check in data.checkPagination.items %}
                    {% include 'order_check/_table_item.html.twig' %}
                {% endfor %}
            {% else %}
                <tr>
                    <td colspan="8">
                        {{ include('@SharedBundle/snipped/item_not_found.html.twig') }}
                    </td>
                </tr>
            {% endif %}
            </tbody>
            <tfoot>
            <tr>
                <td colspan="11">
                    {% set data = {'pagination': data.checkPagination,'approvedPagination': data.approvedPagination} %}
                    {% include '@SharedBundle/snipped/pager.html.twig' %}
                </td>
            </tr>
            </tfoot>
        </table>
        {{ include('order_check/_table_approved.html.twig') }}

    </div>

    <!-- Modals -->
    {{ include('order_check/_modal_confirmation.html.twig') }}
</div>

<style>
    .table-wrapper {
        overflow-x: auto;
        max-height: 1000px; /* Adjust this height as needed */
    }

    .table {
        min-width: 1000px; /* Ensure the table is wide enough for horizontal scrolling */
    }
</style>

<script>
    document.querySelectorAll('.tab-button button').forEach(button => {
        button.addEventListener('click', function() {
            document.querySelectorAll('.table-responsive').forEach(table => {
                table.classList.add('d-none');
            });
            const target = this.getAttribute('data-target');
            document.querySelector(target).classList.remove('d-none');
            document.querySelector(target).classList.add('d-block');
        });
    });
</script>
<script>
    $('.notifyPriceErrorButton').click(function(){
        var orderItemId = $(this).data('order-item-id');
        window.url = "{{ path('app_ordercheck_notifypriceerror', {'orderItem': 'PLACEHOLDER'}) }}".replace('PLACEHOLDER',orderItemId);
        Swal.fire({
            html: `Emin misiniz?`,
            icon: "warning",
            buttonsStyling: false,
            showCancelButton: true,
            confirmButtonText: "Evet",
            cancelButtonText: 'Hayır',
            customClass: {
                confirmButton: "btn btn-primary",
                cancelButton: 'btn btn-danger'
            }
        })
            .then((result) => {
                if (result.isConfirmed) {

                    $.get( window.url, function( data ) {
                        console.log(data);
                        if(data.isSuccess) {
                            Swal.fire("Bildirildi", "", "success");
                            location.reload();
                        } else {
                            Swal.fire("Bir Hata Oluştu", data.message, "error");
                        }
                    });
                }
            });
    });


    $('.approveButton').click(function(){
        var orderItemCheckId = $(this).data('order-item-check-id');
        window.url = "{{ path('app_ordercheck_approve', {'orderItemCheck': 'PLACEHOLDER'}) }}".replace('PLACEHOLDER',orderItemCheckId);

        const lastDescription = localStorage.getItem('lastDescription') || '';

        Swal.fire({
            title: "Açıklama",
            input: "text",
            inputValue: lastDescription,
            inputAttributes: {
                autocapitalize: "off",
                required: true,
                placeholder: 'İşlem için açıklama giriniz.'
            },
            showCancelButton: false,
            confirmButtonText: "Onayla",
            showLoaderOnConfirm: true,
            didOpen: () => {
                // input elementine eriş
                const input = Swal.getInput();
                // inputa yazıldıkça localStorage güncelle
                input.addEventListener('input', (e) => {
                    localStorage.setItem('lastDescription', e.target.value);
                });
            },
            preConfirm: async (description) => {
                try {
                    const url = window.url + '?description=' + encodeURIComponent(description);
                    const response = await fetch(url);
                    console.log(response);
                } catch (error) {
                    Swal.fire("Bir Hata Oluştu", "", "error");
                }
            },
            allowOutsideClick: () => !Swal.isLoading()
        }).then((result) => {
            if (result.isConfirmed) {
                Swal.fire("Onaylandı", "", "success");
                location.reload();
            }
        });
    });


    // Hepsini seç
    document.getElementById('select-all-checkbox').addEventListener('change', function () {
        const isChecked = this.checked;
        document.querySelectorAll('.row-checkbox').forEach(cb => cb.checked = isChecked);
    });

    // Toplu Onayla
    document.getElementById('bulk-approve-button').addEventListener('click', async function () {
        const selectedIds = Array.from(document.querySelectorAll('.row-checkbox:checked'))
            .map(cb => cb.dataset.orderItemCheckId);

        if (selectedIds.length === 0) {
            Swal.fire("Uyarı", "Lütfen en az bir öğe seçin.", "warning");
            return;
        }

        const lastDescription = localStorage.getItem('lastDescription') || '';

        const { value: description } = await Swal.fire({
            title: "Açıklama",
            input: "text",
            inputValue: lastDescription,
            inputAttributes: {
                autocapitalize: "off",
                required: true,
                placeholder: 'İşlem için açıklama giriniz.'
            },
            confirmButtonText: "Onayla",
            showCancelButton: true,
            showLoaderOnConfirm: true,
            didOpen: () => {
                const input = Swal.getInput();
                input.addEventListener('input', (e) => {
                    localStorage.setItem('lastDescription', e.target.value);
                });
            },
            preConfirm: async (description) => {
                if (!description) {
                    Swal.showValidationMessage("Açıklama gerekli");
                    return false;
                }
                return description;
            },
            allowOutsideClick: () => !Swal.isLoading()
        });

        if (description) {
            let successCount = 0;
            for (const id of selectedIds) {
                const url = "{{ path('app_ordercheck_approve', {'orderItemCheck': 'PLACEHOLDER'}) }}".replace('PLACEHOLDER', id) + '?description=' + encodeURIComponent(description);
                try {
                    const response = await fetch(url);
                    if (response.ok) {
                        successCount++;
                    }
                } catch (e) {
                    console.error(`ID ${id} için hata:`, e);
                }
            }

            Swal.fire("Tamamlandı", `${successCount} sipariş onaylandı.`, "success").then(() => location.reload());
        }
    });
</script>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        document.querySelectorAll('.ki-copy-success').forEach(function (icon) {
            icon.addEventListener('click', function (e) {
                e.preventDefault();

                const textToCopy = this.getAttribute('data-copy');
                if (!textToCopy) return;

                // Kopyalama işlemi
                function copyText() {
                    if (navigator.clipboard && window.isSecureContext) {
                        return navigator.clipboard.writeText(textToCopy);
                    } else {
                        const textarea = document.createElement('textarea');
                        textarea.value = textToCopy;
                        textarea.style.position = 'fixed';
                        textarea.style.opacity = '0';
                        document.body.appendChild(textarea);
                        textarea.focus();
                        textarea.select();
                        try {
                            document.execCommand('copy');
                            document.body.removeChild(textarea);
                            return Promise.resolve();
                        } catch (err) {
                            document.body.removeChild(textarea);
                            return Promise.reject();
                        }
                    }
                }

                // Toast'ı göster
                function showToast(iconElement) {
                    const toast = document.createElement('div');
                    toast.textContent = 'Kopyalandı';
                    toast.style.position = 'absolute';
                    toast.style.background = '#333';
                    toast.style.color = '#fff';
                    toast.style.padding = '4px 8px';
                    toast.style.borderRadius = '4px';
                    toast.style.fontSize = '12px';
                    toast.style.top = '-24px';
                    toast.style.left = '0';
                    toast.style.whiteSpace = 'nowrap';
                    toast.style.zIndex = '9999';
                    toast.style.opacity = '0';
                    toast.style.transition = 'opacity 0.3s';

                    iconElement.style.position = 'relative';
                    iconElement.appendChild(toast);

                    requestAnimationFrame(() => {
                        toast.style.opacity = '1';
                    });

                    setTimeout(() => {
                        toast.style.opacity = '0';
                        setTimeout(() => {
                            iconElement.removeChild(toast);
                        }, 300);
                    }, 1500);
                }

                copyText()
                    .then(() => showToast(this))
                    .catch(() => console.error('Kopyalama başarısız'));
            });
        });
    });
</script>
