{% extends '@Shared/base/base.html.twig' %}
{% block javascript %}
{% endblock %}
    {% block body %}
        <!--begin::Products-->
        <div class="card shadow-sm">
            <!--begin::Card body-->
            <div class="card-body pt-0">
                {%  include 'order_transfer/_table.html.twig' %}
                {% include '@SharedBundle/snipped/pager.html.twig' %}
            </div>
            <!--end::Card body-->
        </div>
        <!--end::Products-->

    {% endblock %}
    {% block footer %}
        <script>
            $(document).ready(function () {
                $('.buttonTransfer').click(function () {
                    let orderItemId = $(this).data('order-item-id');
                    const storageKey = 'lastSevkEdenAdi'; // tüm butonlar için ortak key

                    let savedName = localStorage.getItem(storageKey) || '';

                    window.url = "{{ path('app_ordertransfer_transfer', {'orderItem': 'PLACEHOLDER'}) }}".replace('PLACEHOLDER', orderItemId);

                    Swal.fire({
                        title: "Sevk eden kişinin adını giriniz.",
                        input: "text",
                        inputValue: savedName, // input'a otomatik yaz
                        inputAttributes: {
                            autocapitalize: "off",
                            required: true,
                            placeholder: 'Ad Soyad'
                        },
                        showCancelButton: false,
                        confirmButtonText: "Onayla",
                        showLoaderOnConfirm: true,
                        preConfirm: async (name) => {
                            try {
                                localStorage.setItem(storageKey, name); // yazılan değeri sakla
                                const url = window.url + '?name=' + encodeURIComponent(name);
                                const response = await fetch(url);
                                console.log(response);
                            } catch (error) {
                                Swal.fire("Bir Hata Oluştu", "", "error");
                            }
                        },
                        allowOutsideClick: () => !Swal.isLoading()
                    }).then((result) => {
                        if (result.isConfirmed) {
                            Swal.fire("Onaylandı", "", "success");
                            location.reload();
                        }
                    });
                });
            });
        </script>
    {% endblock %}

    {% block actions %}
        {% include 'order_transfer/_action.html.twig' %}
    {% endblock %}