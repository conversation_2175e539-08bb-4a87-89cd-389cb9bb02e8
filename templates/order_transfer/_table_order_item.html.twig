{% if(orderItem.productVariant is not null) %}
<table class="w-100" border="0">
    <tr>
        <td width="15%">
            <div class="d-flex align-orderItem.-center">
                <div class="ms-5">
                    {{ orderItem.location.name ?? 'Bilinmiyor' }}
                </div>
            </div>
        </td>
        <td>
            <table id="orderItem" border="0" style="padding: 5px;">
                <tbody>
                <tr>
                    <td rowspan="2" class="p-3">
                        {% if orderItem.productVariant.product.images | first %}
                                <a href="{{ (orderItem.productVariant.product.images | first).url }}" target="_blank">
                                    <img src="{{ (orderItem.productVariant.product.images | first).url }}" class="rounded w-100px" />
                                </a>
                            {% else  %}
                            <img src="{{ asset('bundles/shared/assets/images/no-image.webp') }}" class="rounded w-100px" />
                        {% endif %}
                    </td>
                </tr>
                <tr>
                    <td>
                        <table>
                            <tbody>
                            <tr>
                                <td colspan="2">{{orderItem.productVariant.product.title}}</td>
                            </tr>
                            <tr>
                                <td colspan="2"><b>Stok Kodu:</b> {{orderItem.productVariant.stockCode}}
                                </td>
                            </tr>
                            <tr>
                                <td><b>Beden:</b> {{ orderItem.productVariant.size }} Numara</td>
                                <td><b>Renk:</b> {{ orderItem.color }}</td>
                            </tr>
                            <tr>
                                <td><b>Marka:</b> {{ orderItem.productVariant.product.brand.name ?? '-' }}</td>
                                <td><b>Sezon:</b> {{ orderItem.season }}</td>
                            </tr>
                            <tr>
                                <td>
                                    <b>Durum:</b>
                                     {% if orderItem.state == 'out_of_stock' %}
                                        <a class="badge badge-danger" href="">{{ orderItem.state | mapOrderItemStateToTitle }}</a><a href=""> Bildir</a>
                                     {% elseif orderItem.state == 'price_error' %}
                                         <a href="#"  class="badge badge-warning">{{ orderItem.state | mapOrderItemStateToTitle }}</a>
                                         <a href="{{ path('app_ordersale_notifypriceerror',{'orderItem': orderItem.id}) }}">Fiyat Hatası Bildir</a>
                                    {% else %}
                                        {{ orderItem.state | mapOrderItemStateToTitle}}
                                    {% endif %}
                                </td>
                            </tr>

                            </tbody>
                        </table>
                    </td>
                </tr>
                </tbody>
            </table>
        </td>
        <td class="text-center dt-type-numeric">
              <span id="psf">{{ orderItem.price | number_format(2, '.', ',') }} ₺</span>
        </td>
        <td class="text-end">
            {% include 'order_transfer/_table_order_action_button.html.twig' %}
        </td>
    </tr>
</table>
{% else %}
    <p class="alert alert-danger">Bu sipariş kalemi için ürün bulunamadı. Siparişi kontrol edin! (orderItemId: {{orderItem.id}})</p>
{% endif %}
