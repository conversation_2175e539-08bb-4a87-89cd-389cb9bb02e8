{# @var o \Wb3\SharedBundle\Entity\Order #}

<tr>
    <td style="vertical-align: middle;" width="10%">
        <div class="d-flex">
            <div>
                #<a href="{{ path('app_order_detail',{'order':o.id}) }}" class="text-gray-800 text-hover-primary fw-bold">{{ o.orderNumber }}
                </a>
                <i class="ki-duotone ki-copy-success" data-copy="{{ o.orderNumber }}" style="cursor:pointer;">
                    <span class="path1"></span>
                    <span class="path2"></span>
                </i>
                </a>
                <br>
                <span>{{ o.orderAt|date('d.m.Y H:i') }}</span>
                <a data-bs-toggle="tooltip" data-bs-toggle="tooltip" data-bs-delay-show="100" data-bs-placement="top"  data-toggle="tooltip"
                   title="Sipariş {{ o.createdAt|date('d.m.Y H:i') }} tarihinde tarafımıza aktarıldı ve mağaza ekranlarına ilk bu tarihte yansıdı.
                   Bu bilgi simgesinin kırmızı olması, siparişin platformdan tarafımıza geç yansıtıldığını (30 dk dan fazla) belirtmektedir.">
                    <i class="fa fa-question-circle w-25px {% if o.orderAt.timestamp -  o.createdAt.timestamp < 30*60 %}text-success{% else %}text-danger{% endif %}"></i>
                </a>
                <br>
            </div>
        </div>
        {% set remainingTime = o.getRemainingTime() %}
        <span class="{% if remainingTime and remainingTime.h < 2 %} animation-blink  {% endif %}"
              style="color: #bdbaba; font-weight: bold; display: block; color: red;">
                <i class="ki-duotone ki-time text-danger">
                    <span class="path1"></span>
                    <span class="path2"></span>
                </i>
                {% if remainingTime %}
                    Kalan Süre: {{ remainingTime.h }} sa., {{ remainingTime.i }} dak.
                {% else %}
                    Süre doldu!
                {% endif %}
        </span>
        <div>Kargo: {{ o.cargo.name }}</div>
        <div>Durum: {{ o.state | mapOrderStateToTitle }}</div>
    </td>
    <td  style="vertical-align: middle;">
        <div class="d-flex align-orderItem.-center">
            <div class="ms-5 text-center">
                <b><img src="{{ asset('bundles/shared/assets/images/platform/'~ o.platform.code ~'.png') }}" style="max-width: 75px" /></b>
                {% if o.extras['isExport'] and o.addresses | length %}
                    <br/>
                    <img src="{{ asset('bundles/shared/assets/images/country-flag/' ~ o.addresses[0].countryCode | lower ~ '.svg') }}" width="30" title="{{ o.addresses[0].country }}" />
                {% endif %}

            </div>
        </div>
    </td>
    <td style="vertical-align: middle;">
        <div class="d-flex align-items-center">
            <div class="ms-5">
                <a class="text-gray-800 text-hover-primary fw-bold">{{o.customer.name }}</a>
            </div>
        </div>
    </td>
    <td colspan="3">
        {% for orderItem in o.items %}
            {% include 'order_transfer/_table_order_item.html.twig' %}
        {% endfor %}
    </td>
</tr>