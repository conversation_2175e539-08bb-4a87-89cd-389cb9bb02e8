<form method="get">
<div class="d-flex align-items-center overflow-auto">
    <!-- Search -->
    <div class="d-flex flex-wrap gap-4">
        <div class="position-relative w-md-100px flex-grow-1" data-bs-toggle="tooltip" data-bs-placement="top" title="Müşteri adına, stok koduna, sipariş numarası, ürün adı ve barkod">


            <input type="text" data-kt-ecommerce-product-filter="search"
                   class="form-control form-control form-control-solid form-control-sm"
                   placeholder="Arama"  name="q" value="{{ data.filters.q ?? null }}" >

    </div>
    <!-- Search -->

    <!--begin::Wrapper-->
        <div class="position-relative w-md-140px flex-grow-1"> <!-- Updated width -->
            <select name="brandName" data-control="select2" data-close-on-select="true" data-placeholder="Marka" class="form-select form-select-sm w-md-125px form-select-solid" onchange="this.form.submit()">
                <option value=""></option>
                {% for b in data.brands %}
                    <option value="{{ b.name }}"
                            {% if(data.filters.brandName is defined and data.filters.brandName == b.name) %}selected
                            {% endif %}>
                        {{ b.name }} </option>
                {% endfor %}
            </select>
        </div>





        <div class="position-relative w-md-130px flex-grow-1">
            <select name="platformCode" data-control="select2" data-close-on-select="true" data-placeholder="Platform " class="form-select form-select-sm w-md-125px form-select-solid" onchange="this.form.submit()">
                <option value=""></option>
                {% for o in data.platforms %}
                    <option value="{{ o.code }}"
                            {% if(data.filters.platformCode is defined and data.filters.platformCode == o.code) %}selected
                            {% endif %}>
                        {{ o.title }} </option>
                {% endfor %}
            </select>
        </div>

        <!--begin::Select-->
        <div class="position-relative w-md-120px flex-grow-1"> <!-- Updated width -->
            <select name="locationCode" data-control="select2" data-close-on-select="true" data-placeholder="Lokasyon" class="form-select form-select-sm w-md-125px form-select-solid" onchange="this.form.submit()">
                <option value=""></option>
                {% for loc in data.locations %}
                    <option value="{{ loc.code }}"
                            {% if(data.filters.locationCode is defined and data.filters.locationCode == loc.code) %} selected
                            {% endif %}>
                        {{ loc.name }}
                    </option> <span class="badge badge-circle badge-info ms-auto">15</span>
                {% endfor %}
            </select>

        </div>

        <!--begin::Select-->
        <div class="position-relative w-md-120px flex-grow-1"> <!-- Updated width -->
            <select name="itemState" data-control="select2" data-close-on-select="true" data-placeholder="Durum" class="form-select form-select-sm w-md-125px form-select-solid" onchange="this.form.submit()">
                <option value=""></option>
                {% for state in data.itemStates %}
                    <option value="{{ state }}"
                            {% if(data.filters.itemState is defined and data.filters.itemState == state) %} selected
                            {% endif %}>
                        {{ state | mapOrderItemStateToTitle}}
                    </option> <span class="badge badge-circle badge-info ms-auto">15</span>
                {% endfor %}
            </select>

        </div>

    <!--begin::Separartor-->
    <div class="bullet bg-secondary h-35px w-1px mx-5"></div>
    <!--end::Separartor-->

    <div class="position-relative w-md-140px flex-grow-1">
        <select  data-control="" data-close-on-select="true" name="sortBy" class="form-select form-select-sm w-md-125px form-select-solid" data-placeholder="Sıralama" onchange="this.form.submit()">
            {% for key, label in data.sortByOptions %}
                <option value="{{ key }}"
                        {% if data.filters.sortBy is defined and data.filters.sortBy == key %} selected {% endif %}>
                    {{ label }}
                </option>
            {% endfor %}
        </select>
    </div>
        <!--end::Select-->
        <!--begin::Actions-->
        <div class="d-flex align-items-center ms-3">
            <a href="{{ path('app_ordersale_index') }}" class="btn btn-sm btn-icon btn-light" data-bs-toggle="tooltip" data-bs-placement="top" aria-label="Enable row view" data-bs-original-title="Enable row view" data-kt-initialized="1">
                <i class="ki-duotone ki-minus">
                </i>
            </a>
        </div>
        <!--end::Actions-->
    </div>
    <!--end::Wrapper-->
</div>
</form>