{% if data.pagination.pageCount is defined %}
    {% set data = data.pagination %}
    {% if data.pageCount > 0 %}
        <div class="d-flex flex-stack flex-wrap pt-10">
            <div class="fs-7 fw-semibold text-gray-700">

                {% if data['currentPage'] == data['pageCount'] %}
                    {{ '%totalAll% kayıttan %start% - %end% arasındaki kayıtlar gösteriliyor' | trans({'%totalAll%': data['totalCount'] | number_format(0, ',', '.'), '%start%': data['totalCount'] - data['itemCount'] + 1, '%end%': data['totalCount']}) }}
                {% else %}
                    {% if data['currentPage'] == 1 %}
                        {{ '%totalAll% kayıttan %start% - %end% arasındaki kayıtlar gösteriliyor' | trans({'%totalAll%': data['totalCount'] | number_format(0, ',', '.'), '%start%': 1, '%end%': data['itemCount'] * data['currentPage']}) }}
                    {% else %}
                        {{ '%totalAll% kayıttan %start% - %end% arasındaki kayıtlar gösteriliyor' | trans({'%totalAll%': data['totalCount'] | number_format(0, ',', '.'), '%start%': data['itemCount'] * data['currentPage'] - data['limit'] + 1, '%end%': data['itemCount'] * data['currentPage']}) }}
                    {% endif %}
                {% endif %}

            </div>
            <div class="p-2 bd-highlight mt-1">
                <div class="dataTables_paginate paging_simple_numbers float-end" id="data-table-default_paginate">
                    {% if data['pageCount'] > 1 %}
                        <ul class="pagination pagination-sm">
                            {# `«` arrow #}
                            <li class="paginate_button page-item {{ data['currentPage'] == 1 ? 'disabled' }}">
                                <a class="page-link" href=" {{ path(app.request.attributes.get('_route'), app.request.attributes.get('_route_params')|merge(app.request.query.all|merge({'page':  data['currentPage']-1 < 1 ? 1 : data['currentPage']-1 }))) }}">«</a>
                            </li>
                            {% set before = max(1, data['currentPage'] - 4) %}
                            {% set after = min(data['currentPage'] + 4, data['pageCount']) %}
                            {% if data['pageCount'] > 10 %}
                                {% if data['currentPage'] > 5 %}
                                    {# firstPageNumber #}
                                    <li class="paginate_button page-item  {{ data['currentPage'] == before ? 'active' }} ">
                                        <a class="page-link" href="{{ path(app.request.attributes.get('_route'), app.request.attributes.get('_route_params')|merge(app.request.query.all|merge({'page': 1 }))) }}">1</a>
                                    </li>
                                    {# `...` #}
                                    {% if data['currentPage'] != 6 %}
                                        <li class="paginate_button page-item">
                                            <a style="cursor: default" class="page-link">...</a>
                                        </li>
                                    {% endif %}
                                {% endif %}
                                {% for i in before..after %}
                                    <li class="paginate_button page-item  {{ data['currentPage'] == i ? 'active' }} ">
                                        <a class="page-link" href=" {{ path(app.request.attributes.get('_route'), app.request.attributes.get('_route_params')|merge(app.request.query.all|merge({'page': i }))) }}">{{ i }}</a>
                                    </li>
                                {% endfor %}
                            {% else %}
                                {# Render each page number #}
                                {% for i in 1..data['pageCount'] %}
                                    <li class="paginate_button page-item  {{ data['currentPage'] == i ? 'active' }} ">
                                        <a class="page-link" href=" {{ path(app.request.attributes.get('_route'), app.request.attributes.get('_route_params')|merge(app.request.query.all|merge({'page': i }))) }}">{{ i }}</a>
                                    </li>
                                {% endfor %}
                            {% endif %}

                            {% if data['currentPage'] <= data['pageCount'] -5 %}
                                {% if data['currentPage'] != data['pageCount'] -5 %}
                                    {% if data['pageCount'] > 10 %}
                                        <li class="paginate_button page-item">
                                            <a style="cursor: default" class="page-link">...</a>
                                        </li>
                                    {% endif %}
                                {% endif %}
                                {% if data['pageCount'] > 10 %}
                                    <li class="paginate_button page-item  {{ data['pageCount'] == after ? 'active' }} ">
                                        <a class="page-link" href="{{ path(app.request.attributes.get('_route'), app.request.attributes.get('_route_params')|merge(app.request.query.all|merge({'page': data['pageCount'] }))) }}">{{ data['pageCount'] }}</a>
                                    </li>
                                {% endif %}
                            {% endif %}
                            {# `»` arrow #}
                            <li class="paginate_button page-item {{ data['currentPage'] == data['pageCount'] ? 'disabled' }}">
                                <a class="page-link" href=" {{ path(app.request.attributes.get('_route'), app.request.attributes.get('_route_params')|merge(app.request.query.all|merge({'page': data['currentPage']+1 <= data['pageCount'] ? data['currentPage']+1 : data['currentPage'] }))) }}">»</a>
                            </li>
                        </ul>
                    {% endif %}
                </div>
            </div>
                <div class="p-7 bd-highlight">
                    {% if data['pageCount'] > 1 and data['currentPage'] == 1 %}
                    <form action="{{ path(app.request.attributes.get('_route'), app.request.attributes.get('_route_params')|merge(app.request.query.all))}}" method="get">
                        {% for key,value in  app.request.query.all %}
                            {% if key!='limit' %}
                                {% if value is iterable %}
                                    {% for val in value %}
                                        <input type="hidden" name="{{ key }}[]" value="{{ val }}">
                                    {% endfor %}
                                {% else %}
                                    <input type="hidden" name="{{ key }}" value="{{ value }}">
                                {% endif %}
                            {% endif %}
                        {% endfor %}
                        <select name="limit" aria-controls="kt_ecommerce_products_table" class="form-select form-select-sm form-select-solid" onchange="$(this).parent().submit()">
                            <option value="10" {% if data['limit'] == 10 %}selected{% endif %}>10</option>
                            <option value="20" {% if data['limit'] == 20 %}selected{% endif %}>20</option>
                            <option value="50" {% if data['limit'] == 50 %}selected{% endif %}>50</option>
                            <option value="100" {% if data['limit'] == 100 %}selected{% endif %}>100</option>
                        </select>
                    </form>
            {% endif %}
                </div>
        </div>
    {% endif %}
{% endif %}