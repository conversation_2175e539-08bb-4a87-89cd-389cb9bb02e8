<h2><PERSON><PERSON></h2>
<div class="d-flex flex-wrap">
    {% for brand in data.brands %}
        <!--begin::Stat-->
        <div class="border border-gray-300 border-dashed rounded min-w-125px py-3 px-4 me-6 mb-3">
            <!--begin::Number-->
            <div class="d-flex align-items-center">
                <i class="ki-duotone ki-arrow-up fs-3 text-success me-2">
                    <span class="path1"></span>
                    <span class="path2"></span>
                </i>
                <div class="fs-2 fw-bold" data-kt-countup="true" data-kt-countup-value="{{ brand.count }}">0</div>
            </div>
            <!--end::Number-->

            <!--begin::Label-->
            <div class="fw-semibold fs-6 text-gray-500">
                <a href="{{ path('app_ordersale_index') }}?q={{ brand.key[0] ?? '-' }}" class="text-gray-500">
                    {{ brand.key[0] ?? '-'  }}
                </a>
            </div>
            <!--end::Label-->
        </div>
        <!--end::Stat-->
    {% endfor %}
</div>
