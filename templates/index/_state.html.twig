<h2><PERSON><PERSON><PERSON></h2>
<div class="d-flex flex-wrap">
    {% for state in data.states %}
        <!--begin::Stat-->
        <div class="border border-gray-300 border-dashed rounded min-w-125px py-3 px-4 me-6 mb-3">
            <!--begin::Number-->
            <div class="d-flex align-items-center">
                <i class="ki-duotone ki-arrow-up fs-3 text-success me-2">
                    <span class="path1"></span>
                    <span class="path2"></span>
                </i>
                <div class="fs-2 fw-bold" data-kt-countup="true" data-kt-countup-value="{{ state.count }}">0</div>
            </div>
            <!--end::Number-->

            <!--begin::Label-->
            <div class="fw-semibold fs-6">
                <a href="{{ path('app_ordersale_index') }}?q={{ state.key }}" class="text-gray-500">
                    {{ state.key | mapOrderStateToTitle }}
                </a>
            </div>
            <!--end::Label-->
        </div>
        <!--end::Stat-->
    {% endfor %}
</div>
