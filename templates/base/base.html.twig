{%
    set menu =[
    {'title':'Ürün', 'baseUrl': pim_base_url,'icon': 'ki-cube-2',
        'children':[
        {'title':'Ürün','icon':'ki-cube-2','path':'/product'},
        {'title':'<PERSON><PERSON><PERSON>','icon':'ki-abstract-31','path':'/product-attribute'},
        {'title':'Özellik Değeri','icon':'ki-abstract-25','path':'/product-attribute-value'},
        {'title':'Fiyat Kuralları','icon':'ki-price-tag','path':'/product-price-rule'},
        ],
    },
        {'title':'Platform', 'baseUrl': mpi_base_url,'icon': 'ki-arrow-mix',
        'children':[
        {'title':'Ürün Listesi','icon':'ki-cube-2','path':'/platform-product-variant'},
        {'title':'Katalog Eşleştirme','icon':'ki-arrow-mix','path':'#',
            'children': [
            {'title':'Idefix','icon':'ki-arrow-mix','path':'/platform-product-match/if'},
        ]
        },
        {'title':'Yasaklı Ürün Yönetimi','icon':'ki-cross-circle','path':'/platform-forbidden-product'},
        {'title':'Buffer Yönetimi','icon':'ki-abstract-26','path':'/platform-buffer'},
        {'title':'Fiyat Kuralları','icon':'ki-price-tag','path':'/platform-price-rule'},
    ]
    },
    {'title':'Sipariş', 'baseUrl': sos_base_url,'icon': 'ki-purchase',
        'children':[
        {'title':'Sevk','icon':'ki-abstract-14','path':'/order-transfer'},
        {'title':'Satış','icon':'ki-purchase','path':'/order-sale'},
        {'title':'Sipariş Kontrol','icon':'ki-basket-ok','path':'/order-check'},
        {'title':'Sipariş Listesi','icon':'ki-abstract-14','path':'/order'},
        {'title':'Kargo','icon':'ki-abstract-14','path':'#',
            'children' : [
            {'title':'Trendyol Express','icon':'ki-arrow-mix','path':'/order-cargo/3'}
        ]
        },
    ]
    },
    {'title':'Kampanya','baseUrl': promo_base_url,'icon': 'ki-purchase',
        'children':[
        {'title':'Kampanya','icon':'ki-message-text','path':'/campaign'},
    ]
    },
    {'title':'Araçlar','baseUrl': tool_base_url,'icon': 'ki-message-programming',
        'children':[
        {'title':'Txt Birleştirici','icon':'ki-message-text','path':'/txt-combine'},
        {'title':'Lokasyon','icon':'ki-message-text','path':'/location'},
        {'title':'İleti Yönetim Sistemi (IYS)','icon':'ki-message-programming','path':'/iys'},
        {'title':'Kasa Devir','icon':'ki-message-programming','path':'/case-trasfer'},
        {'title':'Log Viewer','icon':'ki-message-programming','path':'#',
            'children': [
            {'title':'SOS','icon':'ki-message-text','path': '/log-viewer','baseUrl': sos_base_url},
            {'title':'MPI','icon':'ki-message-text','path': '/log-viewer','baseUrl': mpi_base_url},
        ]
        },
        {'title':'Bildirim','icon':'ki-message-programming','path':'/notification'},
        {'title':'Bildirim Log','icon':'ki-message-programming','path':'/notification-log'},
        {'title':'Kullanıcı','icon':'ki-message-programming','path':'/user'},
        {'title':'Kullanıcı Rolleri','icon':'ki-message-programming','path':'/user-role'},
    ]
    }
    ]
 %}
<!DOCTYPE html>
<!--
Author: Keenthemes
Product Name: MetronicProduct Version: 8.2.3
Purchase: https://1.envato.market/EA4JP
Website: http://www.keenthemes.com
Contact: <EMAIL>
Follow: www.twitter.com/keenthemes
Dribbble: www.dribbble.com/keenthemes
Like: www.facebook.com/keenthemes
License: For each use you must have a valid license purchased only from above link in order to legally use the theme for your project.
-->
<html lang="en">
<!--begin::Head-->
<head>
    <title>{% block title %}{{ ((breadcrumbs ?? []) | last).name ?? 'Başlıksız' }} - Workboard{% endblock %}</title>
    <base href="../"/>
    <title>Workboard</title>
    <meta charset="utf-8"/>
    <meta name="description" content="WB3. Made with Lovely!"/>
    <meta name="keywords" content="Workboard3, wb3, workboard"/>
    <meta name="viewport" content="width=device-width, initial-scale=1"/>
    <meta property="og:locale" content="tr_TR"/>
    <meta property="og:type" content="e-commerce"/>
    <meta property="og:title" content="Workboard 3"/>
    <meta property="og:site_name" content="Workboard3"/>
    <link rel="shortcut icon" href="{{ asset('bundles/shared/assets/media/logos/favicon.ico') }}"/>
    <!--begin::Fonts(mandatory for all pages)-->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Inter:300,400,500,600,700"/><!--end::Fonts-->
    <!--end::Vendor Stylesheets-->
    <!--begin::Global Stylesheets Bundle(mandatory for all pages)-->
    <link href="{{ asset('bundles/shared/assets/plugins/global/plugins.bundle.css') }}" rel="stylesheet" type="text/css"/>
    <link href="{{ asset('bundles/shared/assets/css/style.bundle.css') }}" rel="stylesheet" type="text/css"/>
    <!--end::Global Stylesheets Bundle-->
    <script>
        // Frame-busting to prevent site from being loaded within a frame without permission (click-jacking)
        if (window.top != window.self) {
            window.top.location.replace(window.self.location.href);
        }
    </script>
    {% block stylesheets %}
            {{ encore_entry_link_tags('app') }}
    {% endblock %}

    {% block javascripts %}{{ encore_entry_script_tags('app') }}{% endblock %}
    {% block importmap %}{{ importmap('app') }}{% endblock %}
    <script src="https://code.jquery.com/jquery-3.7.1.min.js" integrity="sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=" crossorigin="anonymous"></script>
    {%  block header %}{% endblock %}
</head>
<!--end::Head-->
<!--begin::Body-->
<body id="kt_app_body" data-kt-app-page-loading-enabled="false" data-kt-app-page-loading="off"
      data-kt-app-layout="dark-sidebar" data-kt-app-header-fixed="true" data-kt-app-header-fixed-mobile="true"
      data-kt-app-sidebar-enabled="true" data-kt-app-sidebar-fixed="true" data-kt-app-sidebar-minimize="on"
      data-kt-app-sidebar-hoverable="true" data-kt-app-sidebar-push-header="true"
      data-kt-app-sidebar-push-toolbar="true" data-kt-app-sidebar-push-footer="true" data-kt-app-toolbar-enabled="true"
      data-kt-app-toolbar-fixed="true" class="app-default">
<!--begin::App-->
<div class="d-flex flex-column flex-root app-root" id="kt_app_root">
    <!--begin::Page-->
    <div class="app-page  flex-column" id="kt_app_page">
        {% include '@SharedBundle/base/_header.html.twig' with {'currencies': data.currencies ?? []} %}
        <!--begin::Wrapper-->
        <div class="app-wrapper  flex-column flex-row-fluid " id="kt_app_wrapper">
            {% include '@SharedBundle/base/_sidebar.html.twig' %}
            <!--begin::Main-->
            <div class="app-main flex-column flex-row-fluid " id="kt_app_main">
                <!--begin::Content wrapper-->
                <div class="d-flex flex-column flex-column-fluid">
                    <!--begin:Toolbar-->
                    <div id="kt_app_toolbar" class="app-toolbar py-3 py-lg-6">
                        <!--begin::Toolbar container-->
                        <div id="kt_app_toolbar_container" class="app-container container-fluid d-flex flex-stack flex-wrap flex-md-nowrap">
                            <!--begin::Page title-->
                            <div data-kt-swapper="true" data-kt-swapper-mode="{default: 'prepend', lg: 'prepend'}" data-kt-swapper-parent="{default: '#kt_app_content_container', lg: '#kt_app_toolbar_container'}" class="page-title d-flex flex-column justify-content-center flex-wrap me-3 mb-5 mb-lg-0">
                                <!--begin::Title-->
                                <h1 class="page-heading d-flex text-gray-900 fw-bold fs-3 flex-column justify-content-center my-0"> {{ ((breadcrumbs ?? []) | last).name ?? '-' }}</h1>
                                <!--end::Title-->
                                <!--begin::Breadcrumb-->
                                {% include '@SharedBundle/base/_breadcrumb.html.twig' %}
                                <!--end::Breadcrumb-->
                            </div>
                            <!--end::Page title-->
                            <!--begin::Action group-->
                            {% block actions %}
                            {% endblock %}
                            <!--end::Action group-->
                        </div>
                        <!--end::Toolbar container-->
                    </div>
                    <!--end::Toolbar-->
                    <!--begin::Content-->
                    <div id="kt_app_content" class="app-content flex-column-fluid ">
                        <!--begin::Content container-->
                        <div id="kt_app_content_container" class="app-container container-fluid">

                {% block body %}{% endblock %}
                {% include '@SharedBundle/base/_footer.html.twig' %}

                        </div>
                        <!--end::Content container-->
                </div>
                <!--end::Content wrapper-->
            </div>
            <!--end:::Main-->
        </div>
        <!--end::Wrapper-->
    </div>
    <!--end::Page-->
</div>
<!--end::App-->
<!--layout-partial:partials/_drawers.html-->
<!--begin::Theme mode setup on page load-->
<script>
    var defaultThemeMode = "light";
    var themeMode;
    if (document.documentElement) {
        if (document.documentElement.hasAttribute("data-bs-theme-mode")) {
            themeMode = document.documentElement.getAttribute("data-bs-theme-mode");
        } else {
            if (localStorage.getItem("data-bs-theme") !== null) {
                themeMode = localStorage.getItem("data-bs-theme");
            } else {
                themeMode = defaultThemeMode;
            }
        }
        if (themeMode === "system") {
            themeMode = window.matchMedia("(prefers-color-scheme: dark)").matches ? "dark" : "light";
        }
        document.documentElement.setAttribute("data-bs-theme", themeMode);
    }
</script>
<!--end::Theme mode setup on page load-->
<!--begin::loader-->
<div class="page-loader flex-column">
    <img alt="Logo" class="theme-light-show max-h-50px" src="{{ asset('bundles/shared/assets/media/logos/keenthemes.svg') }}"/>
    <img alt="Logo" class="theme-dark-show max-h-50px" src="{{ asset('bundles/shared/assets/media/logos/keenthemes-dark.svg') }}"/>
    <div class="d-flex align-items-center mt-5">
        <span class="spinner-border text-primary" role="status"></span>
        <span class="text-muted fs-6 fw-semibold ms-5">Yükleniyor...</span>
    </div>
</div>
<!--end::Loader-->
<!--layout-partial:partials/_drawers.html-->
<!--begin::Scrolltop-->
<div id="kt_scrolltop" class="scrolltop" data-kt-scrolltop="true">
    <i class="ki-duotone ki-arrow-up"><span class="path1"></span><span class="path2"></span></i></div>
</div>
<script>
    var hostUrl = "assets/";        </script>
<!--begin::Global Javascript Bundle(mandatory for all pages)-->
<script src="{{ asset('bundles/shared/assets/plugins/global/plugins.bundle.js') }}"></script>
<script src="{{ asset('bundles/shared/assets/js/scripts.bundle.js') }}"></script>
<!--end::Global Javascript Bundle-->
    <!--end::Javascript-->
{% block footer %} {% endblock %}
<script>
    // In your Javascript (external .js resource or <script> tag)
    $(document).ready(function() {
        $('.js-select2').select2();
    });
</script>
</body>
<!--end::Body-->
</html>