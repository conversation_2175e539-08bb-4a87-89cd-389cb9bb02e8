<!--begin::Navbar-->
<div class="app-navbar flex-shrink-0">
    <!--begin::Search-->
    <div class="app-navbar-item align-items-stretch ms-1 ms-md-4">
        <!--layout-partial:partials/search/_dropdown.html-->
    </div>
    <!--end::Search-->
    <!--begin::Currency-->
    <div class="app-navbar-item ms-1 ms-md-4">
        <!--begin::Currency Dropdown-->
        <a href="#" class="btn btn-icon btn-custom btn-icon-muted btn-active-light btn-active-color-primary w-35px h-35px"
           data-kt-menu-trigger="{default:'click', lg: 'hover'}"
           data-kt-menu-attach="parent"
           data-kt-menu-placement="bottom-end">
            <i class="ki-duotone ki-wallet fs-1"><span class="path1"></span><span class="path2"></span></i>
        </a>

        <div class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-title-gray-700 menu-icon-gray-500 menu-active-bg menu-state-color fw-semibold py-4 fs-base w-200px"
             data-kt-menu="true">

            {% for currency in currencies %}
                <div class="menu-item px-3 my-0 d-flex align-items-center justify-content-between">
                <span class="menu-icon">
                    {% if currency.currencyCode == 'USD' %}
                        <i class="ki-duotone ki-dollar fs-2 text-success"><span class="path1"></span><span class="path2"></span></i>
                    {% elseif currency.currencyCode == 'EUR' %}
                        <i class="fas fa-euro-sign fs-2 text-primary"></i>
                    {% elseif currency.currencyCode == 'GBP' %}
                        <i class="fas fa-pound-sign fs-2 text-warning"></i>
                    {% else %}
                        <i class="ki-duotone ki-wallet fs-2"></i>
                    {% endif %}
                </span>

                    <span class="menu-title fw-bold">{{ currency.currencyCode }}</span>

                    {# currencyRate Türk Lirasına göre hesaplanmıyorsa, örnek olarak 1000 ile çarpıyoruz #}
                    <span class="badge badge-light-{{ currency.currencyCode == 'USD' ? 'success' : (currency.currencyCode == 'EUR' ? 'primary' : (currency.currencyCode == 'GBP' ? 'warning' : 'secondary')) }} fs-6">
                     {{ (1 / currency.currencyRate)|number_format(2, '.', ',') }}₺
                </span>
                </div>
            {% endfor %}
        </div>
        <!--end::Currency Dropdown-->
    </div>


    <!--end::Currency-->
    <!--begin::Activities-->
    <div class="app-navbar-item ms-1 ms-md-4">
        <!--begin::Drawer toggle-->
        <div class="btn btn-icon btn-custom btn-icon-muted btn-active-light btn-active-color-primary w-35px h-35px" id="kt_activities_toggle">
            <i class="ki-duotone ki-messages fs-2"><span class="path1"></span><span class="path2"></span><span class="path3"></span><span class="path4"></span><span class="path5"></span></i>
        </div>
        <!--end::Drawer toggle-->
    </div>
    <!--end::Activities-->
    <!--begin::Notifications-->
    <div class="app-navbar-item ms-1 ms-md-4">
        <!--begin::Menu- wrapper-->
        <div class="btn btn-icon btn-custom btn-icon-muted btn-active-light btn-active-color-primary w-35px h-35px" data-kt-menu-trigger="{default: 'click', lg: 'hover'}" data-kt-menu-attach="parent" data-kt-menu-placement="bottom-end" id="kt_menu_item_wow">
            <i class="ki-duotone ki-notification-status fs-2"><span class="path1"></span><span class="path2"></span><span class="path3"></span><span class="path4"></span></i>
        </div>
        <!--layout-partial:partials/menus/_notifications-menu.html-->
        <!--end::Menu wrapper-->
    </div>
    <!--end::Notifications-->
    <!--begin::Chat-->
    <div class="app-navbar-item ms-1 ms-md-4">
        <!--begin::Menu wrapper-->
        <div class="btn btn-icon btn-custom btn-icon-muted btn-active-light btn-active-color-primary w-35px h-35px position-relative" id="kt_drawer_chat_toggle">
            <i class="ki-duotone ki-message-text-2 fs-2"><span class="path1"></span><span class="path2"></span><span class="path3"></span></i>
            <span class="bullet bullet-dot bg-success h-6px w-6px position-absolute translate-middle top-0 start-50 animation-blink">
					</span>
        </div>
        <!--end::Menu wrapper-->
    </div>
    <!--end::Chat-->
    <!--begin::My apps links-->
    <div class="app-navbar-item ms-1 ms-md-4">
        <!--begin::Menu wrapper-->
        <div class="btn btn-icon btn-custom btn-icon-muted btn-active-light btn-active-color-primary w-35px h-35px" data-kt-menu-trigger="{default: 'click', lg: 'hover'}" data-kt-menu-attach="parent" data-kt-menu-placement="bottom-end">
            <i class="ki-duotone ki-element-11 fs-2"><span class="path1"></span><span class="path2"></span><span class="path3"></span><span class="path4"></span></i>
        </div>
        <!--layout-partial:partials/menus/_my-apps-menu.html-->
        <!--end::Menu wrapper-->
    </div>
    <!--end::My apps links-->
    <!--begin::Theme mode-->
    <div class="app-navbar-item ms-1 ms-md-4">
        <!--begin::Menu toggle-->
        <a href="#" class="btn btn-icon btn-custom btn-icon-muted btn-active-light btn-active-color-primary w-35px h-35px" data-kt-menu-trigger="{default:'click', lg: 'hover'}" data-kt-menu-attach="parent" data-kt-menu-placement="bottom-end">
            <i class="ki-duotone ki-night-day theme-light-show fs-1"><span class="path1"></span><span class="path2"></span><span class="path3"></span><span class="path4"></span><span class="path5"></span><span class="path6"></span><span class="path7"></span><span class="path8"></span><span class="path9"></span><span class="path10"></span></i><i class="ki-duotone ki-moon theme-dark-show fs-1"><span class="path1"></span><span class="path2"></span></i></a>
        <!--begin::Menu toggle-->
        <!--begin::Menu-->
        <div class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-title-gray-700 menu-icon-gray-500 menu-active-bg menu-state-color fw-semibold py-4 fs-base w-150px" data-kt-menu="true" data-kt-element="theme-mode-menu">
            <!--begin::Menu item-->
            <div class="menu-item px-3 my-0">
                <a href="#" class="menu-link px-3 py-2" data-kt-element="mode" data-kt-value="light">
						<span class="menu-icon" data-kt-element="icon">
						<i class="ki-duotone ki-night-day fs-2"><span class="path1"></span><span class="path2"></span><span class="path3"></span><span class="path4"></span><span class="path5"></span><span class="path6"></span><span class="path7"></span><span class="path8"></span><span class="path9"></span><span class="path10"></span></i></span>
                    <span class="menu-title">
						Light </span>
                </a>
            </div>
            <!--end::Menu item-->
            <!--begin::Menu item-->
            <div class="menu-item px-3 my-0">
                <a href="#" class="menu-link px-3 py-2" data-kt-element="mode" data-kt-value="dark">
						<span class="menu-icon" data-kt-element="icon">
						<i class="ki-duotone ki-moon fs-2"><span class="path1"></span><span class="path2"></span></i></span>
                    <span class="menu-title">
						Dark </span>
                </a>
            </div>
            <!--end::Menu item-->
            <!--begin::Menu item-->
            <div class="menu-item px-3 my-0">
                <a href="#" class="menu-link px-3 py-2" data-kt-element="mode" data-kt-value="system">
						<span class="menu-icon" data-kt-element="icon">
						<i class="ki-duotone ki-screen fs-2"><span class="path1"></span><span class="path2"></span><span class="path3"></span><span class="path4"></span></i></span>
                    <span class="menu-title">
						System </span>
                </a>
            </div>
            <!--end::Menu item-->
        </div>
        <!--end::Menu-->
    </div>
    <!--end::Theme mode-->
    <!--begin::User menu-->

    <div class="app-navbar-item ms-1 ms-md-4" id="kt_header_user_menu_toggle">
        <!--begin::Menu wrapper-->

        <a href="#" class="symbol symbol-35px btn-icon-muted btn-active-light btn-active-color-primary" data-kt-menu-trigger="{default:'click', lg: 'hover'}" data-kt-menu-attach="parent" data-kt-menu-placement="bottom-end">
            <img src="{{ (app.user is defined and app.user.email ?? '<EMAIL>') | gravatar }}" class="rounded-3" alt="user" style="background-color: white !important;"/>
        </a>
        <div  class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-title-gray-700 menu-icon-gray-500 menu-active-bg menu-state-color fw-semibold py-2 fs-base w-250px" data-kt-menu="true" data-kt-element="theme-mode-menu">
            <!--begin::Menu item-->
            <div class="menu-item px-1 my-0">
                {% if app.user and app.user.id %}
                    <div class="menu-link py-1" data-kt-element="mode">
                        <a href="{{ tool_base_url }}/user/detail/{{ app.user.id }}" class="menu-content fs-6 text-gray-900 fw-bold px-3 py-4">
                            <span class="menu-title">Kullanıcı bilgileri</span>
                        </a>
                    </div>
                {% endif %}
                <div class=" menu-link py-1" data-kt-element="mode">
                  <a href="{{ pim_base_url ~ '/logout' }}" class="menu-content fs-6 text-gray-900 fw-bold px-3 py-4">  <span class="menu-title">Çıkış Yap </span> </a>
                </div>

            </div>
        </div>
        <!--end::Menu wrapper-->
    </div>
    <!--end::User menu-->
    <!--begin::Header menu toggle-->
    <div class="app-navbar-item d-lg-none ms-2 me-n2" title="Show header menu">
        <div class="btn btn-flex btn-icon btn-active-color-primary w-30px h-30px" id="kt_app_header_menu_toggle">
            <i class="ki-duotone ki-element-4 fs-1"><span class="path1"></span><span class="path2"></span></i>
        </div>
    </div>
    <!--end::Header menu toggle-->
    <!--begin::Aside toggle-->
    <!--end::Header menu toggle-->
</div>
<!--end::Navbar-->