<tr>
    <td>
        <a href="{{ path('app_order_detail',{order: order.id }) }}"
           class="text-gray-600 text-hover-primary ">
            {{ order.orderNumber }}
        </a>
        <i class="ki-duotone ki-copy-success" data-copy="{{ order.orderNumber }}" style="cursor:pointer;">
            <span class="path1"></span>
            <span class="path2"></span>
        </i>
    </td>
    <td>{{ order.customer.name }} (Fat.isim: {{ order.customer.name }})</td>

    <td>
        {% for orderItem in order.items %}
            {% if orderItem.productVariant is not null %}
             <a href="{{ mpi_base_url ~ '/platform-product-variant/?q=' ~ orderItem.productVariant.stockCode }}" class="text-gray-600 text-hover-primary">
                {{orderItem.productVariant.stockCode}}
            </a>
                <i class="ki-duotone ki-copy-success" data-copy="{{orderItem.productVariant.stockCode}}" style="cursor:pointer;">
                    <span class="path1"></span>
                    <span class="path2"></span>
                </i><br/>
            {% else %}
                <p class="alert alert-danger">Ürün bulunamadı!</p>
            {% endif %}
        {% endfor %}
    </td>
    <td class="text-end">{{ order.totalPrice  | number_format(2, '.', ',') }} ₺</td>
    <td>{{ order.orderAt|date('d.m.Y H:i:s') }}</td>
    <td>
        {% if order.state == 'new' %}
            <span class="badge py-3 px-4 fs-7 badge-light-success">{{ order.state | mapOrderStateToTitle }}</span>
        {% elseif order.state == 'delivered' %}
            <span class="badge py-3 px-4 fs-7 badge-light-primary">{{ order.state | mapOrderStateToTitle }}</span>
        {% elseif order.state == 'undelivered' %}
        <span class="badge py-3 px-4 fs-7 badge-light-danger">{{ order.state | mapOrderStateToTitle }}</span>
        {% elseif order.state == 'customer_canceled' %}
        <span class="badge py-3 px-4 fs-7 badge-light-warning">{{ order.state | mapOrderStateToTitle }}</span>
        {% elseif order.state == 'vendor_canceled' %}
        <span class="badge py-3 px-4 fs-7 badge-danger">{{ order.state | mapOrderStateToTitle }}</span>
        {% elseif order.state == 'in_cargo' %}
        <span class="badge py-3 px-4 fs-7 badge-light-info">{{ order.state | mapOrderStateToTitle }}</span>
        {% elseif order.state == 'customer_cancel_requested' %}
        <span class="badge py-3 px-4 fs-7 badge-warning">{{ order.state | mapOrderStateToTitle }}</span>
        {% elseif order.state == 'waiting_payment' %}
        <span class="badge py-3 px-4 fs-7 badge-info">{{ order.state | mapOrderStateToTitle }}</span>
        {% elseif order.state == 'invoiced' %}
        <span class="badge py-3 px-4 fs-7 badge-success">{{ order.state | mapOrderStateToTitle }}</span>
        {% else %}
        <span class="badge py-3 px-4 fs-7 badge-success">{{ order.state | mapOrderStateToTitle }}</span></td>
    {% endif %}
    <td>

        <img src="{{ asset('bundles/shared/assets/images/platform/'~ order.platform.code ~'.png') }}" height="30"  alt="platform logo"/>

    </td>
    {% include 'order/_index_table_item_action.html.twig' %}
</tr>