<div class="card card-flush py-4 flex-row-fluid mt-10">
    <!--begin::Card header-->
    <div class="card-header">
        <div class="card-title">
            <h2>Si<PERSON><PERSON>ş Geçmişi</h2>
        </div>
    </div>
    <!--end::Card header-->
    <!--begin::Card body-->
    <div class="card-body pt-0">
        <div class="table-responsive">
            <!--begin::Table-->
            <table class="table align-middle table-row-dashed fs-6 gy-5 mb-0">
                <thead>
                <tr class="text-start text-gray-500 fw-bold fs-7 text-uppercase gs-0">
                    <th class="min-w-100px">Tarih</th>
                    <th class="min-w-100px">Kullanıcı</th>
                    <th class="min-w-175px">Değişiklik</th>
                </tr>
                </thead>
                <tbody class="fw-semibold text-gray-600">

                {% if data.audits|length %}
                {% for audit in data.audits %}
                {% if audit.changes.state.old is defined  %}
                    <tr>
                        <td>{{ audit.createdAt }}</td>
                        <td>{{ audit.user }}</td>
                    <td>
                        <div><span class="text-danger">{{ audit.changes.state.old | mapOrderStateToTitle }}</span> =>  <span class="text-success">{{ audit.changes.state.new | mapOrderStateToTitle }}</span></div>
                    </td>
                    </tr>
                    {% else %}
                    <tr>
                        <td>{{ audit.createdAt }}</td>
                        <td>system</td>
                        <td>{{ audit.type }}</td>
                    </tr>
                {% endif %}
                {% endfor %}
                {% else %}
                    <td colspan="3">
                        {% include '@SharedBundle/snipped/item_not_found.html.twig' %}
                    </td>
                {% endif %}
                </tbody>
            </table>
            <!--end::Table-->
        </div>
    </div>
    <!--end::Card body-->
</div>