<div class="card-header align-items-center py-1 gap-2 gap-md-5">
    <!--begin::Card toolbar-->
    <div class="card-toolbar flex-row-fluid justify-content-end gap-5">
        <!--begin::Export dropdown-->
        <button type="button" class="btn btn-light-primary" data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end">
            <i class="ki-duotone ki-exit-up fs-2">
                <span class="path1"></span>
                <span class="path2"></span>
            </i>Excel Dışa Aktar</button>
        <!--begin::Menu-->
        <div id="kt_ecommerce_report_returns_export_menu" class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-600 menu-state-bg-light-primary fw-semibold fs-7 w-200px py-4" data-kt-menu="true" style="">
            <!--begin::Menu item-->
            <div class="menu-item px-3">
                 <a href="#" class="menu-link px-3" id="personel-satis-raporu" data-kt-ecommerce-export="excel">Personel Satış Raporu</a>
            </div>
            <!--end::Menu item-->
        </div>
        <!--end::Menu-->
        <!--end::Export dropdown-->
    </div>
    <!--end::Card toolbar-->
</div>
<!--begin::Table-->
<div class="table-responsive">
    <table class="table table-row-dashed fs-6 gy-5">
        <thead>
        <tr class="text-gray-500 fw-bold fs-7 text-uppercase gs-0">
            <th class="min-w-100px">Sipariş Numarası</th>
            <th class="min-w-100px">Müşteri Adı</th>
            <th class="min-w-100px">Ürün</th>
            <th class="min-w-100px">Tutar</th>
            <th class="min-w-100px">Tarih</th>
            <th class="min-w-100px">Durum</th>
            <th class="min-w-100px">Platform</th>
            <th class="min-w-100px">İşlem</th>
        </tr>
        </thead>
        <tbody class="fw-semibold text-gray-600">
        <!-- Sample Data Row -->
        {% if data.pagination.items | length %}
        {% for order in  data.pagination.items %}
            {% include 'order/_index_table_item.html.twig' %}
        {% endfor %}
        {% else %}
            <tr>
                <td colspan="8">
                    {% include'@SharedBundle/snipped/item_not_found.html.twig' %}
                </td>
            </tr>
        {% endif %}
        <!-- Repeat for more rows as needed -->
        </tbody>
    </table>
</div>
<!--end::Table-->

<!--begin::Modal - Personel Satış Raporu-->
<div class="modal fade" tabindex="-1" id="kt_modal_personel_satis_raporu">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Personel Satış Raporu</h5>
                <!--begin::Close-->
                <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal" aria-label="Close">
                    <i class="ki-duotone ki-cross fs-2x">
                        <span class="path1"></span>
                        <span class="path2"></span>
                    </i>
                </div>
                <!--end::Close-->
            </div>

            <div class="modal-body">
                <div class="mb-0">
                    <label for="kt_daterangepicker_personel_rapor" class="form-label">Tarih Aralığı Seçin</label>
                    <input class="form-control form-control-solid" placeholder="Tarih aralığı seçin" id="kt_daterangepicker_personel_rapor"/>
                </div>
            </div>

            <div class="modal-footer">
                <button type="button" class="btn btn-light" data-bs-dismiss="modal">İptal</button>
                <button type="button" class="btn btn-primary" id="btn_download_personel_rapor">Raporu İndir</button>
            </div>
        </div>
    </div>
</div>
<!--end::Modal-->

<script>
    document.addEventListener('DOMContentLoaded', function () {
        document.querySelectorAll('.ki-copy-success').forEach(function (icon) {
            icon.addEventListener('click', function (e) {
                e.preventDefault();

                const textToCopy = this.getAttribute('data-copy');
                if (!textToCopy) return;

                // Kopyalama işlemi
                function copyText() {
                    if (navigator.clipboard && window.isSecureContext) {
                        return navigator.clipboard.writeText(textToCopy);
                    } else {
                        const textarea = document.createElement('textarea');
                        textarea.value = textToCopy;
                        textarea.style.position = 'fixed';
                        textarea.style.opacity = '0';
                        document.body.appendChild(textarea);
                        textarea.focus();
                        textarea.select();
                        try {
                            document.execCommand('copy');
                            document.body.removeChild(textarea);
                            return Promise.resolve();
                        } catch (err) {
                            document.body.removeChild(textarea);
                            return Promise.reject();
                        }
                    }
                }

                // Toast'ı göster
                function showToast(iconElement) {
                    const toast = document.createElement('div');
                    toast.textContent = 'Kopyalandı';
                    toast.style.position = 'absolute';
                    toast.style.background = '#333';
                    toast.style.color = '#fff';
                    toast.style.padding = '4px 8px';
                    toast.style.borderRadius = '4px';
                    toast.style.fontSize = '12px';
                    toast.style.top = '-24px';
                    toast.style.left = '0';
                    toast.style.whiteSpace = 'nowrap';
                    toast.style.zIndex = '9999';
                    toast.style.opacity = '0';
                    toast.style.transition = 'opacity 0.3s';

                    iconElement.style.position = 'relative';
                    iconElement.appendChild(toast);

                    requestAnimationFrame(() => {
                        toast.style.opacity = '1';
                    });

                    setTimeout(() => {
                        toast.style.opacity = '0';
                        setTimeout(() => {
                            iconElement.removeChild(toast);
                        }, 300);
                    }, 1500);
                }

                copyText()
                    .then(() => showToast(this))
                    .catch(() => console.error('Kopyalama başarısız'));
            });
        });
        
        // Personel Satış Raporu modal açma
        document.getElementById('personel-satis-raporu').addEventListener('click', function(e) {
            e.preventDefault();

            // Modal'ı aç
            const modal = new bootstrap.Modal(document.getElementById('kt_modal_personel_satis_raporu'));
            modal.show();
        });

        // Date range picker'ı başlat
        $("#kt_daterangepicker_personel_rapor").daterangepicker({
            locale: {
                format: 'DD/MM/YYYY',
                separator: ' - ',
                applyLabel: 'Uygula',
                cancelLabel: 'İptal',
                fromLabel: 'Başlangıç',
                toLabel: 'Bitiş',
                customRangeLabel: 'Özel Aralık',
                weekLabel: 'H',
                daysOfWeek: ['Pz', 'Pt', 'Sa', 'Ça', 'Pe', 'Cu', 'Ct'],
                monthNames: ['Ocak', 'Şubat', 'Mart', 'Nisan', 'Mayıs', 'Haziran',
                    'Temmuz', 'Ağustos', 'Eylül', 'Ekim', 'Kasım', 'Aralık'],
                firstDay: 1
            },
            startDate: moment().subtract(29, 'days'),
            endDate: moment(),
            ranges: {
                'Bugün': [moment(), moment()],
                'Dün': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
                'Son 7 Gün': [moment().subtract(6, 'days'), moment()],
                'Son 30 Gün': [moment().subtract(29, 'days'), moment()],
                'Bu Ay': [moment().startOf('month'), moment().endOf('month')],
                'Geçen Ay': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
            }
        });

        // Raporu İndir butonu
        document.getElementById('btn_download_personel_rapor').addEventListener('click', function(e) {
            e.preventDefault();

            // Seçilen tarih aralığını al
            const dateRange = $("#kt_daterangepicker_personel_rapor").val();
            if (!dateRange) {
                alert('Lütfen tarih aralığı seçin');
                return;
            }

            // Tarihleri ayır
            const dates = dateRange.split(' - ');
            const startDate = moment(dates[0], 'DD/MM/YYYY').format('YYYY-MM-DD');
            const endDate = moment(dates[1], 'DD/MM/YYYY').format('YYYY-MM-DD');

            // Yükleniyor göstergesi
            const button = this;
            const originalText = button.innerHTML;
            button.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Yükleniyor...';
            button.disabled = true;

            // AJAX isteği ile Excel dosyasını al
            fetch(`{{ path('app_order_personelsalesreport') }}?start_date=${startDate}&end_date=${endDate}`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Rapor oluşturulurken bir hata oluştu');
                    }
                    return response.blob();
                })
                .then(blob => {
                    // Excel dosyasını indir
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.style.display = 'none';
                    a.href = url;
                    a.download = `Personel_Satis_Raporu_${startDate}_${endDate}.xlsx`;
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);

                    // Modal'ı kapat
                    const modal = bootstrap.Modal.getInstance(document.getElementById('kt_modal_personel_satis_raporu'));
                    modal.hide();

                    // Butonu eski haline getir
                    button.innerHTML = originalText;
                    button.disabled = false;
                })
                .catch(error => {
                    console.error('Hata:', error);
                    alert('Rapor oluşturulurken bir hata oluştu: ' + error.message);

                    // Butonu eski haline getir
                    button.innerHTML = originalText;
                    button.disabled = false;
                });
        });
    });
</script>
