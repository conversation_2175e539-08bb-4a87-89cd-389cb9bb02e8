<div class="card-header align-items-center py-5 gap-2 gap-md-5">
    <!--begin::Card toolbar-->
    <div class="card-toolbar flex-row-fluid justify-content-end gap-5">
        <!--begin::Export dropdown-->
        <button type="button" class="btn btn-light-primary" data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end">
            <i class="ki-duotone ki-exit-up fs-2">
                <span class="path1"></span>
                <span class="path2"></span>
            </i>Excel Dışa Aktar</button>
        <!--begin::Menu-->
        <div id="kt_ecommerce_report_returns_export_menu" class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-600 menu-state-bg-light-primary fw-semibold fs-7 w-200px py-4" data-kt-menu="true" style="">
            <!--begin::Menu item-->
            <div class="menu-item px-3">
                 <a href="#" class="menu-link px-3" id="personel-satis-raporu" data-kt-ecommerce-export="excel">Personel Satış Raporu</a>
            </div>
            <!--end::Menu item-->
        </div>
        <!--end::Menu-->
        <!--end::Export dropdown-->
    </div>
    <!--end::Card toolbar-->
</div>
<!--begin::Table-->
<div class="table-responsive">
    <table class="table table-row-dashed fs-6 gy-5">
        <thead>
        <tr class="text-gray-500 fw-bold fs-7 text-uppercase gs-0">
            <th class="min-w-100px">Sipariş Numarası</th>
            <th class="min-w-100px">Müşteri Adı</th>
            <th class="min-w-100px">Ürün</th>
            <th class="min-w-100px">Tutar</th>
            <th class="min-w-100px">Tarih</th>
            <th class="min-w-100px">Durum</th>
            <th class="min-w-100px">Platform</th>
            <th class="min-w-100px">İşlem</th>
        </tr>
        </thead>
        <tbody class="fw-semibold text-gray-600">
        <!-- Sample Data Row -->
        {% if data.pagination.items | length %}
        {% for order in  data.pagination.items %}
            {% include 'order/_index_table_item.html.twig' %}
        {% endfor %}
        {% else %}
            <tr>
                <td colspan="8">
                    {% include'@SharedBundle/snipped/item_not_found.html.twig' %}
                </td>
            </tr>
        {% endif %}
        <!-- Repeat for more rows as needed -->
        </tbody>
    </table>
</div>
<!--end::Table-->

<script>
    document.addEventListener('DOMContentLoaded', function () {
        document.querySelectorAll('.ki-copy-success').forEach(function (icon) {
            icon.addEventListener('click', function (e) {
                e.preventDefault();

                const textToCopy = this.getAttribute('data-copy');
                if (!textToCopy) return;

                // Kopyalama işlemi
                function copyText() {
                    if (navigator.clipboard && window.isSecureContext) {
                        return navigator.clipboard.writeText(textToCopy);
                    } else {
                        const textarea = document.createElement('textarea');
                        textarea.value = textToCopy;
                        textarea.style.position = 'fixed';
                        textarea.style.opacity = '0';
                        document.body.appendChild(textarea);
                        textarea.focus();
                        textarea.select();
                        try {
                            document.execCommand('copy');
                            document.body.removeChild(textarea);
                            return Promise.resolve();
                        } catch (err) {
                            document.body.removeChild(textarea);
                            return Promise.reject();
                        }
                    }
                }

                // Toast'ı göster
                function showToast(iconElement) {
                    const toast = document.createElement('div');
                    toast.textContent = 'Kopyalandı';
                    toast.style.position = 'absolute';
                    toast.style.background = '#333';
                    toast.style.color = '#fff';
                    toast.style.padding = '4px 8px';
                    toast.style.borderRadius = '4px';
                    toast.style.fontSize = '12px';
                    toast.style.top = '-24px';
                    toast.style.left = '0';
                    toast.style.whiteSpace = 'nowrap';
                    toast.style.zIndex = '9999';
                    toast.style.opacity = '0';
                    toast.style.transition = 'opacity 0.3s';

                    iconElement.style.position = 'relative';
                    iconElement.appendChild(toast);

                    requestAnimationFrame(() => {
                        toast.style.opacity = '1';
                    });

                    setTimeout(() => {
                        toast.style.opacity = '0';
                        setTimeout(() => {
                            iconElement.removeChild(toast);
                        }, 300);
                    }, 1500);
                }

                copyText()
                    .then(() => showToast(this))
                    .catch(() => console.error('Kopyalama başarısız'));
            });
        });
        
        // Personel Satış Raporu Excel export
        document.getElementById('personel-satis-raporu').addEventListener('click', function(e) {
            e.preventDefault();
            
            // Yükleniyor göstergesi
            const button = this;
            button.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Yükleniyor...';
            button.disabled = true;
            
            // AJAX isteği ile Excel dosyasını al
            fetch('{{ path('app_order_personelsalesreport') }}')
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Rapor oluşturulurken bir hata oluştu');
                    }
                    return response.blob();
                })
                .then(blob => {
                    // Excel dosyasını indir
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.style.display = 'none';
                    a.href = url;
                    a.download = 'Personel_Satis_Raporu_' + new Date().toISOString().slice(0, 10) + '.xlsx';
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    
                    // Butonu eski haline getir
                    button.innerHTML = 'Personel Satış Raporu';
                    button.disabled = false;
                })
                .catch(error => {
                    console.error('Hata:', error);
                    alert('Rapor oluşturulurken bir hata oluştu: ' + error.message);
                    
                    // Butonu eski haline getir
                    button.innerHTML = 'Personel Satış Raporu';
                    button.disabled = false;
                });
        });
    });
</script>
