{% extends '@SharedBundle/base/base.html.twig' %}
 {# @var o \Wb3\SharedBundle\Entity\Order #}
{% set o = data.order %}
    {% block body %}
{% set o = data.order %}
        <div id="kt_app_content_container" class="app-container  container-xxl">
            <!--begin::Order details page-->
            <div class="d-flex flex-column gap-7 gap-lg-10">
                <div class="d-flex flex-column flex-xl-row gap-4 gap-lg-7">
                    <!--begin::Order details-->
                    {% include 'order/_detail_order.html.twig' %}
                    <!--end::Order details-->
                    <!--begin::Customer details-->
                    {% include 'order/_detail_customer.html.twig'%}
                    <!--end::Customer details-->
                </div>

                <div class="tab-content">
                    <!--begin::Tab pane-->
                    <!--begin::Tab pane-->
                    <div class="tab-pane fade show active" id="kt_ecommerce_sales_order_summary" role="tab-panel">
                        <!--begin::Order Item-->
                        {% include 'order/_detail_order_items.html.twig' %}
                        <!--end::Order Item-->
                    </div>
                    <!--end::Tab pane-->
                    <!--begin::Tab pane-->

                </div>
                <!--end::Tab content-->
            </div>
            <!--end::Order details page-->

            <div class="d-flex flex-column flex-xl-row gap-7 gap-lg-10 mt-10">
                <!--begin::Fatura Adresi-->
                {% include 'order/_detail_address.html.twig' with {'title': 'Fatura Adresi','address': data.invoiceAddress }%}
                <!--end::Fatura Adresi-->
                <!--begin::Teslimat Adresi-->
                {% include 'order/_detail_address.html.twig' with {'title': 'Teslimat Adresi','address': data.invoiceAddress } %}
                <!--end::Teslimat Adresi-->
            </div>

            {% include 'order/_detail_history.html.twig' %}
        </div>

    {% endblock %}