{% for item in o.items %}
    {% if item.productVariant is not null %}
<div class="accordion" id="kt_accordion_{{ loop.index }}">
    <div class="accordion-item">
        <h2 class="accordion-header" id="kt_accordion_{{ loop.index }}_header_1">
            <button class="accordion-button fs-4 fw-semibold collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#kt_accordion_{{ loop.index }}_body_1" aria-expanded="false" aria-controls="kt_accordion_1_body_1">
                <div class="d-flex align-items-center">
                    <img src="{{ item.productVariant.product.images[0].url ?? "-" }}" class="w-50px" />
                    <div class="ms-5">{{ item.productVariant.product.title }}</div>
                </div>
            </button>
        </h2>
        <div id="kt_accordion_{{ loop.index }}_body_1" class="accordion-collapse collapse" aria-labelledby="kt_accordion_{{ loop.index }}_header_1" data-bs-parent="#kt_accordion_1">
            <div class="accordion-body">
                <div>
                    <table class="table align-middle table-row-dashed fs-6 gy-5 mb-0 order-table">
                        <tr>
                            <td class="min-w-100px text-end">{{ item.productVariant.stockCode }}</td>
                            <td class="text-end">1 ÇİFT</td>
                            <td class="text-end">{{ item.price }}</td>
                            <td class="text-end">{{ item.price }}</td>
                        </tr>
                    </table>
                </div>
            </div>
            <div class="p-5">
                {{ include('order/_detail_order_item_history.html.twig') }}
            </div>
        </div>
    </div>

    {% else %}
    <p class="alert alert-danger">Ürün bulunamadı.</p>
    {% endif %}
{% endfor %}