<!--begin::Table-->
<table class="table align-middle table-row-dashed fs-6 gy-5 mb-0">
    <thead>
    <tr class="text-start text-gray-500 fw-bold fs-7 text-uppercase gs-0">
        <th class="min-w-100px">Ta<PERSON>h</th>
        <th class="min-w-100px">Kullanıcı</th>
        <th class="min-w-175px"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></th>
    </tr>
    </thead>
    <tbody class="fw-semibold text-gray-600">

    {% if data.orderItemAudits[loop.index-1]|length %}
        {% for audit in data.orderItemAudits[loop.index-1] %}
            {% if audit.changes.state.old is defined  %}
            <tr>
                <td>{{ audit.createdAt }}</td>
                <td>{{ audit.user }}</td>
                <td>
                    <div><span class="text-danger">{{ audit.changes.state.old | mapOrderItemStateToTitle }}</span> =>  <span class="text-success">{{ audit.changes.state.new | mapOrderItemStateToTitle }}</span></div>
                </td>
            </tr>
            {% endif %}
        {% endfor %}
    {% else %}
        <td colspan="3">
            {% include '@SharedBundle/snipped/item_not_found.html.twig' %}
        </td>
    {% endif %}
    </tbody>
</table>
<!--end::Table-->