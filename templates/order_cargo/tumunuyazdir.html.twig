<button type="button" class="btn btn-info w-100" data-bs-toggle="modal" data-bs-target="#teslimListesiModal">
    <b><PERSON>ü<PERSON><PERSON><PERSON><PERSON>ı<PERSON></b><br>
</button>

<!-- Modal -->
<div class="modal fade" id="teslimListesiModal" tabindex="-1" aria-labelledby="teslimListesiModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="teslimListesiModalLabel">Teslim Listesi Filtrele</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- Tarih Seçme Inputu -->
                <div class="mb-4">
                    <label for="tarihSec" class="form-label">Tarih <PERSON>:</label>
                    <input type="date" class="form-control" id="tarihSec">
                </div>

                <script>
                    // Bugünün tarihini default al
                    const today = new Date().toISOString().split('T')[0];

                    // Tarih inputuna bugünün tarihini varsayılan olarak yerleştir
                    document.getElementById('tarihSec').value = today;
                </script>

                <!-- Kargo ve Platform Seç Dropdown Menüler -->
                <div class="d-flex mb-4">
                    <!-- Kargo Seçme -->
                    <div class="dropdown mb-4 me-2">
                        <button class="btn btn-light dropdown-toggle fw-bold" type="button" id="kargoFilterButton" data-bs-toggle="dropdown" aria-expanded="false" style="font-size: 1.1rem; padding: 0.75rem 1.5rem;">
                            Kargo Seç: Tümü
                        </button>
                        <ul class="dropdown-menu" aria-labelledby="kargoFilterButton" style="min-width: 220px;">
                            <li><a class="dropdown-item" href="#" data-value="Tümü">Tümü</a></li>
                            <li><a class="dropdown-item" href="#" data-value="MNG Kargo">MNG Kargo</a></li>
                            <li><a class="dropdown-item" href="#" data-value="Sürat Kargo">Sürat Kargo</a></li>
                            <li><a class="dropdown-item" href="#" data-value="Aras Kargo">Aras Kargo</a></li>
                        </ul>
                    </div>

                    <!-- Platform Seçme -->
                    <div class="dropdown mb-4">
                        <button class="btn btn-light dropdown-toggle fw-bold" type="button" id="platformFilterButton" data-bs-toggle="dropdown" aria-expanded="false" style="font-size: 1.1rem; padding: 0.75rem 1.5rem;">
                            Platform Seç: Tümü
                        </button>
                        <ul class="dropdown-menu" aria-labelledby="platformFilterButton" style="min-width: 220px;">
                            <li><a class="dropdown-item" href="#" data-value="Tümü">Tümü</a></li>
                            <li><a class="dropdown-item" href="#" data-value="Ayakkabıonline">Ayakkabıonline</a></li>
                            <li><a class="dropdown-item" href="#" data-value="AliExpress">AliExpress</a></li>
                            <li><a class="dropdown-item" href="#" data-value="Amazon">Amazon</a></li>
                            <!-- Diğer platformlar burada yer alacak devamını kesin ekle -->
                        </ul>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" id="onaylaButton">Onayla</button>
            </div>
        </div>
    </div>
</div>

<script>
    // Kargo Seç dropdown'u için
    document.querySelectorAll('#kargoFilterButton + .dropdown-menu a').forEach(function (element) {
        element.addEventListener('click', function (event) {
            event.preventDefault();
            const selectedText = this.getAttribute('data-value');
            document.getElementById('kargoFilterButton').innerText = 'Kargo Seç: ' + selectedText;
        });
    });

    // Platform Seç dropdown'u için
    document.querySelectorAll('#platformFilterButton + .dropdown-menu a').forEach(function (element) {
        element.addEventListener('click', function (event) {
            event.preventDefault();
            const selectedText = this.getAttribute('data-value');
            document.getElementById('platformFilterButton').innerText = 'Platform Seç: ' + selectedText;
        });
    });

    // Onayla butonuna tıklandığında yönlendirme
    document.getElementById('onaylaButton').addEventListener('click', function() {
        window.open('cargo_report', '_blank');
    });
</script>
