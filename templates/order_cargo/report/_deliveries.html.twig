<div class="modal fade" id="printedModal" tabindex="-1" aria-labelledby="printedModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="printedModalLabel">Yaz<PERSON><PERSON>r<PERSON>lanlar</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- Date Selection Input -->
                <div class="mb-4">
                    <label for="datePicker" class="form-label">Ta<PERSON><PERSON>:</label>
                    <input type="date" class="form-control" data-model="printedDate">
                </div>

                <!-- Kargo Seç Dropdown -->
                <div class="mb-4">
                    <label for="kargoDropdown" class="form-label">Kargo <PERSON>:</label>
                    <div class="dropdown">
                        <label>
                            <select class="form-select" data-model="printedCargoId" data-action="live#action" data-live-action-param="allDeliveries">
                                <option value="">Tümü</option>
                                {% for cargo in orderCargoes %}
                                    {# @var cargo \Wb3\SharedBundle\Entity\OrderCargo #}
                                    <option value="{{ cargo.id }}" {{ cargo.id == printedCargoId ? 'selected="selected"' : null }}>{{ cargo.name }}</option>
                                {% endfor %}
                            </select>
                        </label>
                    </div>
                </div>

                <!-- Table -->
                <div class="table-responsive">
                    <table class="table table-row-dashed table-row-gray-300 align-middle gs-0 gy-4">
                        <thead>
                        <tr class="fw-bold text-muted">
                            <th class="min-w-150px">Dosya</th>
                            <th class="min-w-100px">Tarih</th>
                            <th class="min-w-100px">Kargo</th>
                        </tr>
                        </thead>
                        <tbody>
                        {% for ocd in deliveries %}
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="symbol symbol-50px me-3">
                                                <span class="symbol-label bg-light">
                                                    <i class="bi bi-file-earmark-text-fill fs-2x text-primary"></i>
                                                </span>
                                    </div>
                                    <div class="d-flex justify-content-start flex-column">
                                        <a target="_blank" href="{{ path('app_ordercargo_report',{'orderCargoDelivery':ocd.id}) }}" class="text-dark fw-bold text-hover-primary fs-6">Yazdır</a>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="text-dark fw-bold d-block fs-6">{{ ocd.createdAt}}</span>
                            </td>
                            <td>
                                <span class="text-dark fw-bold d-block fs-6">{{ ocd.cargoName }}</span>
                            </td>
                        </tr>
                        {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>