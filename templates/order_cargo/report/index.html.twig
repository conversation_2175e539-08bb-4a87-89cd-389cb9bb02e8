<!DOCTYPE html>
<html>
<head>
    <title><PERSON><PERSON> - Yazdır</title>
    <meta charset="UTF-8">
    <style>
        * {
            font-family: "Arial";
            font-size: 13px;
        }
        span {
            font-size: 15px !important;
            font-weight: bold;
        }
        table {
            counter-reset: rowNumber - 1;
            border-collapse: collapse;
        }

        table tr {
            counter-increment: rowNumber;
        }

        table, th, td {
            border: 1px solid #dedede;
        }

        table th {
            font-size: 13px !important;
        }

        table tr td:first-child::before {
            content: counter(rowNumber);
            min-width: 1em;
            margin-right: 0.5em;
        }

        .imza {
            margin-top: 20px;
            display: block;
            font-size: 13px !important;
        }
    </style>
</head>
<body onload="window.print()">
<center>
    <span align="center" style="margin: 1.5px !important;"> Teslim Listesi</span>
    <span style="text-align: center; margin: 1.5px !important;">{{ "now"|date("d.m.Y") }}</span>
</center>
<table width="100%">
    <thead>
    <tr>
        <th><PERSON><PERSON>ra</th>
        <th>İsim</th>
        <th>Sipariş Numarası</th>
        <th>Kargo Barkodu</th>
        <th>Sipariş Tarihi</th>
        <th>Okuma Tarihi</th>
        <th>Platform</th>
        <th>Desi</th>
    </tr>
    </thead>
    <tbody>
{% for item in json %}
        <tr>
            <td></td>
            <td>{{ item.customerName}}</td>
            <td>{{ item.orderNumber }}</td>
            <td>{{ item.trackNumber }}</td>
            <td>{{ item.orderAt }}</td>
            <td>{{ item.readAt }}</td>
            <td>{{ item.platformTitle }}</td>
            <td>{{ item.desi }}</td>
        </tr>
    {% endfor %}

    </tbody>
</table>
<span class="imza">
    Teslim Alan Kargo Görevlisi<br>
    Adı Soyadı - İmza
</span>

</body>
</html>
