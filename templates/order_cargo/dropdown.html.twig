<!-- Dropdown Menüler -->
<div class="d-flex mb-4 gap-7">
    <!-- <PERSON><PERSON><PERSON><PERSON><PERSON> -->
    <div id="printButtonWrapper">
            <button data-action="live#action" data-live-action-param="print" type="button" class="btn btn-primary w-15" id="printButton">
                <i class="fas fa-print"></i> <b>Yazd<PERSON>r</b><br>
        </button>
    </div>

    <!-- Ya<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>lar Button -->
    <div id="printedButtonWrapper">
        <button type="button" class="btn btn-success w-15" id="printedButton" data-bs-toggle="modal" data-bs-target="#printedModal">
            <i class="fas fa-file-alt"></i> <b>Yaz<PERSON><PERSON><PERSON><PERSON>lanlar</b><br>
        </button>
    </div>

    <!-- Modal -->
    {{ include('order_cargo/report/_deliveries.html.twig') }}
</div>

<!-- Script for setting default date and updating the dropdown button text -->
<script>
    // Set today's date as the default value for the date picker
    document.addEventListener('DOMContentLoaded', function() {
        var today = new Date().toISOString().split('T')[0];
        document.getElementById('datePicker').value = today;
    });

    // Update the dropdown button text when an option is selected
    document.querySelectorAll('#kargoDropdownButton + .dropdown-menu .dropdown-item').forEach(item => {
        item.addEventListener('click', function() {
            const selectedValue = this.getAttribute('data-value');
            document.getElementById('kargoDropdownButton').textContent = 'Kargo Seç: ' + selectedValue;
        });
    });
</script>

<!-- Script -->
<script>
    // Kargo Seç dropdown'u için
    document.querySelectorAll('#kargoFilterButton + .dropdown-menu a').forEach(function (element) {
        element.addEventListener('click', function (event) {
            event.preventDefault();
            const selectedText = this.getAttribute('data-value');
            document.getElementById('kargoFilterButton').innerText = 'Kargo Seç: ' + selectedText;

            // Enable the print button if a selection is made
            const printButton = document.getElementById('printButton');
            printButton.style.opacity = '1';
            printButton.style.pointerEvents = 'auto';
            printButton.removeAttribute('disabled');
        });
    });
</script>
