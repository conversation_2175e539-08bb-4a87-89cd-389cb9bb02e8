{% extends '@Shared/base/base.html.twig' %}
{% block body %}
    {{ component('OrderCargo',{orderCargo: data.orderCargo}) }}
{% endblock %}

{% block footer %}
<script>
    var errorSound = new Audio('{{ asset('bundles/shared/assets/sounds/error.mp3') }}');
    var successSound = new Audio('{{ asset('bundles/shared/assets/sounds/success.mp3') }}');
    var oppsSound = new Audio('{{ asset('bundles/shared/assets/sounds/opps.mp3') }}');

    document.addEventListener('get-sound', (event) => {
        console.log(event);
        if(event.detail.type === 'error') {
            errorSound.play();
        } else if(event.detail.type === 'opps') {
            oppsSound.play();
        } else if(event.detail.type === 'success') {
            successSound.play();
        }
    });
    document.addEventListener('print-list-empty', () => {
        alert("Liste boş");
    });
</script>
{% endblock %}