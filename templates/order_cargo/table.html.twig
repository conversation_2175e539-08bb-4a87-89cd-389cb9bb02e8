{# @var o Wb3\SharedBundle\Entity\Order #}
<div class="card bg-light shadow-sm">
    <!-- So<PERSON><PERSON> Kargolar -->
    <div class="portlet light bordered">
        <div class="portlet-title">
            <div class="caption">
                <i class="icon-bar-chart font-dark hide"></i>
                <span style="margin-left: 10px" class="text-gray-800 text-hover-primary fs-5 fw-bold text-uppercase">So<PERSON><PERSON> Kargolar</span>
            </div>
        </div>
        <div class="portlet-body">
            <table class="table table-bordered">
                <thead>
                <tr class="fw-bold fs-6 text-gray-800">
                    <th>Sıra</th>
                    <th>İsim</th>
                    <th>Sipariş Numarası</th>
                    <th>Kargo Barkodu</th>
                    <th>Açıklama </th>
                    <th>Sipariş Tarihi</th>
                    <th>Okuma Tarihi</th>
                    <th>Platform</th>
                    <th>Kargo</th>
                    <th>Desi</th>
                </tr>
                </thead>
                <tbody>
                {% for o in invalidOrders %}
                    <tr>
                        <td>{{ loop.index }}</td>
                        <td class="text-start">{{ o.customerName }}</td>
                        <td class="text-start">{{ o.orderNumber }}</td>
                        <td class="text-start">{{ o.trackNumber }}</td>
                        <td>?</td>
                        <td>{{ o.orderAt }}</td>
                        <td>{{ o.readAt }}</td>
                        <td class="text-start">{{ o.platformTitle }}</td>
                        <td class="text-start">{{ o.cargoName }}</td>
                        <td class="text-end">{{ o.desi }}</td>
                    </tr>
                {% endfor %}
                <tr></tr>
                </tbody>
            </table>
        </div>
    </div>

    <div class="separator my-10"></div>

    <!-- Tüm Kargolar -->
    <div class="portlet light bordered">
        <div class="portlet-title">
            <div class="caption">
                <i class="icon-bar-chart font-dark hide"></i>
                <span style="margin-left: 10px" class="text-gray-800 text-hover-primary fs-5 fw-bold text-uppercase">Tüm Kargolar</span>
            </div>
        </div>
        <div class="portlet-body">
            <table class="table table-bordered text-center">
                <thead>
                <tr class="fw-bold fs-6 text-gray-800">
                    <th>Sıra</th>
                    <th class="text-start">İsim</th>
                    <th class="text-start">Sipariş Numarası</th>
                    <th class="text-start">Kargo Barkodu</th>
                    <th>Sipariş Tarihi</th>
                    <th>Okuma Tarihi</th>
                    <th class="text-start">Platform</th>
                    <th class="text-start">Kargo</th>
                    <th class="text-end">Desi</th>
                </tr>
                </thead>
                <tbody>
                {% for o in validOrders %}
                    <tr>
                        <td>{{ loop.index }}</td>
                        <td class="text-start">{{ o.customerName }}</td>
                        <td class="text-start">{{ o.orderNumber }}</td>
                        <td class="text-start">{{ o.trackNumber }}</td>
                        <td>{{ o.orderAt }}</td>
                        <td>{{ o.readAt }}</td>
                        <td class="text-start">{{ o.platformTitle }}</td>
                        <td class="text-start">{{ o.cargoName }}</td>
                        <td class="text-end">{{ o.desi }}</td>
                    </tr>
                {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
