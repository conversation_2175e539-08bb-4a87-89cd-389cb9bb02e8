{"type": "project", "license": "proprietary", "minimum-stability": "stable", "prefer-stable": true, "require": {"php": ">=8.1", "ext-ctype": "*", "ext-iconv": "*", "ext-simplexml": "*", "ext-soap": "*", "ext-zip": "*", "damienharper/auditor-bundle": "^6.2", "doctrine/annotations": "*", "doctrine/doctrine-bundle": "^2.11", "doctrine/doctrine-migrations-bundle": "^3.3", "doctrine/orm": "*", "dragonmantank/cron-expression": "*", "enqueue/amqp-lib": "^0.10.19", "enqueue/async-command": "^0.10.19", "enqueue/enqueue-bundle": "^0.10.19", "fdekker/log-viewer-bundle": "^2.1", "inspector-apm/inspector-symfony": "^1.8", "phpdocumentor/reflection-docblock": "*", "phpoffice/phpspreadsheet": "^4.4", "serkancelik17/foribaapi": "^1.0", "symfony/asset": "6.4.*", "symfony/asset-mapper": "6.4.*", "symfony/console": "6.4.*", "symfony/dotenv": "6.4.*", "symfony/flex": "^2", "symfony/form": "6.4.*", "symfony/framework-bundle": "6.4.*", "symfony/http-client": "6.4.*", "symfony/messenger": "6.4.*", "symfony/mime": "6.4.*", "symfony/monolog-bundle": "^3.10", "symfony/runtime": "6.4.*", "symfony/scheduler": "6.4.*", "symfony/security-bundle": "6.4.*", "symfony/serializer": "6.4.*", "symfony/twig-bundle": "6.4.*", "symfony/ux-live-component": "^2.24", "symfony/ux-twig-component": "^2.24", "symfony/validator": "6.4.*", "symfony/webpack-encore-bundle": "^2.1", "symfony/workflow": "6.4.*", "symfony/yaml": "6.4.*"}, "config": {"allow-plugins": {"php-http/discovery": true, "symfony/flex": true, "symfony/runtime": true}, "sort-packages": true}, "autoload": {"psr-4": {"App\\": "src/", "Wb3\\SharedBundle\\": "wb3-shared-bundle/src/"}}, "autoload-dev": {"psr-4": {"App\\Tests\\": "tests/"}}, "replace": {"symfony/polyfill-ctype": "*", "symfony/polyfill-iconv": "*", "symfony/polyfill-php72": "*", "symfony/polyfill-php73": "*", "symfony/polyfill-php74": "*", "symfony/polyfill-php80": "*", "symfony/polyfill-php81": "*"}, "require-dev": {"fakerphp/faker": "^1.14", "symfony/maker-bundle": "^1.63", "symfony/stopwatch": "6.4.*", "symfony/web-profiler-bundle": "6.4.*"}, "scripts": {"auto-scripts": {"cache:clear": "symfony-cmd", "assets:install %PUBLIC_DIR%": "symfony-cmd", "importmap:install": "symfony-cmd"}, "post-install-cmd": ["@auto-scripts"], "post-update-cmd": ["@auto-scripts"]}, "conflict": {"symfony/symfony": "*"}, "extra": {"symfony": {"allow-contrib": false, "require": "6.4.*"}}}