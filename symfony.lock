{"damienharper/auditor-bundle": {"version": "6.2", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "4.0", "ref": "694b129ef686d68a4c9ca6b3cafbeb3dec0c9c2d"}}, "doctrine/annotations": {"version": "2.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.10", "ref": "64d8583af5ea57b7afa4aba4b159907f3a148b05"}}, "doctrine/deprecations": {"version": "1.1", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.0", "ref": "87424683adc81d7dc305eefec1fced883084aab9"}}, "doctrine/doctrine-bundle": {"version": "2.11", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.10", "ref": "c170ded8fc587d6bd670550c43dafcf093762245"}, "files": ["config/packages/doctrine.yaml", "src/Entity/.gitignore", "src/Repository/.gitignore"]}, "doctrine/doctrine-migrations-bundle": {"version": "3.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.1", "ref": "1d01ec03c6ecbd67c3375c5478c9a423ae5d6a33"}, "files": ["config/packages/doctrine_migrations.yaml", "migrations/.gitignore"]}, "enqueue/amqp-lib": {"version": "0.10", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "0.7", "ref": "ea8241520598c6cd918d0bd62294be11413dfd69"}}, "enqueue/async-command": {"version": "0.10", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "0.9", "ref": "33ae5f9fb18cf9a966974c8cced466cc05eac799"}}, "enqueue/enqueue-bundle": {"version": "0.10", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "0.9", "ref": "c6ec89b63f963d10ad0d7a7bea170359c119c8bc"}}, "fdekker/log-viewer-bundle": {"version": "2.1", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "1.0", "ref": "42ff59ae742719fe7c26b7a988e8d757a15b2524"}, "files": ["config/packages/fd_log_viewer.yaml", "config/routes/fd_log_viewer.yaml"]}, "inspector-apm/inspector-symfony": {"version": "1.8", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "1.4", "ref": "60ac12d92b25c43830fdf25922e53467058feab1"}}, "symfony/asset-mapper": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.4", "ref": "6c28c471640cc2c6e60812ebcb961c526ef8997f"}, "files": ["assets/app.js", "assets/styles/app.css", "config/packages/asset_mapper.yaml", "importmap.php"]}, "symfony/console": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.3", "ref": "da0c8be8157600ad34f10ff0c9cc91232522e047"}, "files": ["bin/console"]}, "symfony/flex": {"version": "2.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.0", "ref": "146251ae39e06a95be0fe3d13c807bcf3938b172"}, "files": [".env"]}, "symfony/framework-bundle": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.4", "ref": "a91c965766ad3ff2ae15981801643330eb42b6a5"}, "files": ["config/packages/cache.yaml", "config/packages/framework.yaml", "config/preload.php", "config/routes/framework.yaml", "config/services.yaml", "public/index.php", "src/Controller/.gitignore", "src/Kernel.php"]}, "symfony/lock": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.2", "ref": "8e937ff2b4735d110af1770f242c1107fdab4c8e"}, "files": ["config/packages/lock.yaml"]}, "symfony/maker-bundle": {"version": "1.63", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.0", "ref": "fadbfe33303a76e25cb63401050439aa9b1a9c7f"}}, "symfony/messenger": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.0", "ref": "ba1ac4e919baba5644d31b57a3284d6ba12d52ee"}, "files": ["config/packages/messenger.yaml"]}, "symfony/monolog-bundle": {"version": "3.10", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.7", "ref": "aff23899c4440dd995907613c1dd709b6f59503f"}, "files": ["config/packages/monolog.yaml"]}, "symfony/routing": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.2", "ref": "e0a11b4ccb8c9e70b574ff5ad3dfdcd41dec5aa6"}, "files": ["config/packages/routing.yaml", "config/routes.yaml"]}, "symfony/security-bundle": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.4", "ref": "2ae08430db28c8eb4476605894296c82a642028f"}, "files": ["config/packages/security.yaml", "config/routes/security.yaml"]}, "symfony/stimulus-bundle": {"version": "2.24", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.13", "ref": "6acd9ff4f7fd5626d2962109bd4ebab351d43c43"}, "files": ["assets/bootstrap.js", "assets/controllers.json", "assets/controllers/hello_controller.js"]}, "symfony/translation": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.3", "ref": "e28e27f53663cc34f0be2837aba18e3a1bef8e7b"}, "files": ["config/packages/translation.yaml", "translations/.gitignore"]}, "symfony/twig-bundle": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.4", "ref": "cab5fd2a13a45c266d45a7d9337e28dee6272877"}, "files": ["config/packages/twig.yaml", "templates/base.html.twig"]}, "symfony/ux-live-component": {"version": "2.24", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.6", "ref": "73e69baf18f47740d6f58688c5464b10cdacae06"}, "files": ["config/routes/ux_live_component.yaml"]}, "symfony/ux-twig-component": {"version": "2.24", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.13", "ref": "67814b5f9794798b885cec9d3f48631424449a01"}, "files": ["config/packages/twig_component.yaml"]}, "symfony/validator": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.3", "ref": "c32cfd98f714894c4f128bb99aa2530c1227603c"}, "files": ["config/packages/validator.yaml"]}, "symfony/web-profiler-bundle": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.1", "ref": "e42b3f0177df239add25373083a564e5ead4e13a"}, "files": ["config/packages/web_profiler.yaml", "config/routes/web_profiler.yaml"]}, "symfony/webpack-encore-bundle": {"version": "2.1", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.0", "ref": "082d754b3bd54b3fc669f278f1eea955cfd23cf5"}, "files": ["assets/app.js", "assets/styles/app.css", "config/packages/webpack_encore.yaml", "package.json", "webpack.config.js"]}, "symfony/workflow": {"version": "6.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.3", "ref": "3b2f8ca32a07fcb00f899649053943fa3d8bbfb6"}, "files": ["config/packages/workflow.yaml"]}, "twig/extra-bundle": {"version": "v3.19.0"}}