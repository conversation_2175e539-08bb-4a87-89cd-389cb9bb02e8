{"devDependencies": {"@babel/core": "^7.17.0", "@babel/preset-env": "^7.16.0", "@symfony/stimulus-bundle": "^2.24.0", "@symfony/ux-live-component": "^2.24.0", "@symfony/webpack-encore": "^4.6.1", "core-js": "^3.23.0", "regenerator-runtime": "^0.13.9", "webpack": "^5.74.0", "webpack-cli": "^4.10.0", "webpack-notifier": "^1.15.0"}, "license": "UNLICENSED", "private": true, "scripts": {"dev-server": "encore dev-server", "dev": "encore dev", "watch": "encore dev --watch", "build": "encore production --progress"}, "dependencies": {"Hinclude": "^1.2.0"}}