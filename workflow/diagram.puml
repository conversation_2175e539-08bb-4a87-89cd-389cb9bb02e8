@startuml
' Customer-related classes
entity OrderCustomer {
    +id : Integer [PK] not null
    +customerKorgunId : String
    name : String not null
    mail : String
    gsm : String
    tckn : String

}

entity OrderAddress {
    +id : Integer [PK] not null
    order : Order
    type : OrderAddressType
    fistName: string
    lastName: string
    company : String
    address : Text not null
    town : String
    city : String
    country : String
    phone : String
    taxOffice : String
    taxNumber : String
}
entity OrderAddressType {
+id : Integer not null
type : String not null
}

' Order-related classes
entity Order {
    +orderId : String [PK] not null
    orderNumber : String not null
    isGift : Boolean
    giftMessage : String
    customerId : Integer [FK -> orderCustomer.id] not null
    platformId : Integer [FK -> Platform.id] not null
    expireAt : DateTime
    extra : String [micro : Boolean]
    orderAt : DateTime
    *paymentType : String eft/havale
    state : String not null
    cargo : String [FK -> OrderCargo.id] not null
    isPlatformCargo : Boolean not null
    lastShippingAt : Datetime not null
   *packageNumber : Integer
    totalPrice: float
    *totalDiscount: float
    *totalVendorDiscount: float
    *totalPlatformDiscount: float
    *discountedTotalPrice: float
    *totalCommissionAmount: float

}

entity OrderItem {
    +orderId : String [FK -> Order.OrderId] not null
    +location : String [FK -> Location.id] not null
    productVariantId : String [FK ->  Pim ProductVariant.id] not null
    campaignId : [FK -> Campaign.id]
    price : Decimal not null
    *vendorDiscount: float
    *platformDiscount: float
    *totalDiscount: float
    *discountedTotalPrice: float
    *vatRate: int
    *commissionAmount: float
    currencyCode : String
    color : String
    season : String
    state : String  not null
    vendorPrice: float
}


entity OrderCommission{
    --Sonra Bakılacak???--
    commissionRate : Decimal not null
    commission : Decimal not null
    platformCommissionRate : Decimal not null
    operant : String
    campaignLogId : Integer
}
entity OrderItemPriceDiscount{
--Sonra bakılacak--
        +id : Integer not null
        orderpriceid : Integer [FK -> OrderItemPrice.id] not null
        orderPriceTypeId : Integer not null
        discount : String
        totalAmount : Decimal ??
}

entity OrderPriceDiscountType{
--Sonra Bakılacak --
+id : Integer [PK] not null
platform : String [FK -> Platform.id] not null
couponDiscount : Decimal
couponName : String
}


entity OrderCargo {
   id : integer [PK] not null
    name: String
    logo : String
    keywords: json
    isActive : Boolean
}


entity OrderTrack {
    +id : Integer not null
    +orderId : Integer [FK -> Order.id] not null
    trackingNumber : String
     trackingUrl : String
}
entity OrderKorgunInvoice {
    id : Integer [PK] not null
    fatEkod : String
    faturaNo : String
    fatTip : String
    fatTar : DateTime
    belgeNo : String
    eArsivMi : Boolean

}

entity OrderInvoice {
    id : Integer [PK] not null
    orderId : Integer [FK -> Order.id] not null
    invoiceNumber: String
    korgunInvoice: String [FK -> OrderKorgunInvoice.id]
}

entity OrderItemCheck {
item: OrderItem
isApproved: bool:false
description: string
user: User
}

entity OrderItemBuffer {
orderItem: OrderItem
completedAt: DateTime | null
}

' Relationships between entities
OrderItemPriceDiscount ||--|| OrderPriceDiscountType : "OneToOne"
OrderItem ||--|| OrderItemPriceDiscount : "OneToOne"
OrderCargo ||--o{ Order : "OneToMany"
OrderCustomer ||--|| Order : "OneToOne"
Order ||--o{ OrderItem : "OneToMany"
Order ||--o{ OrderAddress : "OneToMany"
Order ||--|| OrderCommission : "OneToOne"
Order ||--|| OrderTrack : "OneToOne"
OrderItem ||--|| OrderCommission : "OneToOne"
Order ||--|| OrderInvoice : "OneToOne"
OrderKorgunInvoice ||--|| OrderInvoice : "OneToOne"
OrderAddressType ||--o{ OrderAddress : "OneToMany"
OrderItem ||--|| OrderItemBuffer : "OneToOne"
@enduml
