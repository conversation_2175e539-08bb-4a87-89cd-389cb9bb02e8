enqueue:
    default:
        transport:
            dsn: '%env(resolve:ENQUEUE_DSN)%'
        client:
            traceable_producer:   true
            prefix:               '%env(resolve:ENQUEUE_PREFIX)%'
            separator:            _
            app_name:             wb3
            router_topic:         default
            router_queue:         default
            router_processor:     null
            redelivered_delay_time: 0
            default_queue:        default

        consumption:
            # the time in milliseconds queue consumer waits for a message (100 ms by default)
            receive_timeout:      10000
        async_commands:
            enabled: true
            timeout: 60
            command_name: ~
            queue_name: ~