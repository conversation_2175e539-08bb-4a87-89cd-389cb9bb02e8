framework:
  workflows:
    order:
      type: 'state_machine'
      marking_store:
        type: 'method'
        property: 'state'
      audit_trail:
        enabled: true
      supports:
        - Wb3\SharedBundle\Entity\Order
      initial_marking: waiting_payment
      places:
        - waiting_payment #stoktan dusulmeyecek. o arada baska bir yerde satarsa?
        - new
        - vendor_canceled
        - customer_cancel_requested #platformdan gelecek.
        - customer_canceled
        - invoiced #itemin butun statelerinden satış yapılabiliecek. item statelerine bakılmayacak.
        - in_cargo #platformdan gelecek.
        - delivered
        - undelivered
      transitions:
        to_new:
          from:  [waiting_payment]
          to: new
        to_invoiced:
          from: [ new ]
          to: invoiced
        to_in_cargo:
          from: invoiced
          to: in_cargo
        to_customer_cancel_requested:
          from: [ new, invoiced]
          to: customer_cancel_requested
        to_customer_canceled:
          from: customer_cancel_requested
          to:   customer_canceled
        to_delivered:
          from: in_cargo
          to: delivered
        to_undelivered:
          from: in_cargo
          to: undelivered
        to_vendor_canceled:
          from: new
          to: vendor_canceled
# şema için komut: php bin/console workflow:dump order | dot -Tpng -o workflow/order.png (client ten calıstırılacak.)
# Tablo yapısı
          #order

          #order-package
          #order-id

          #order-item
            #order_id
            #order_package_id