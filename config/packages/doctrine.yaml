doctrine:
    dbal:
        connections:
            default:
                url: '%env(resolve:DATABASE_URL)%'
                profiling_collect_backtrace: '%kernel.debug%'
                use_savepoints: true
            wb2:
                url: '%env(resolve:DATABASE_URL_WB2)%'
        default_connection: default
    orm:
        default_entity_manager: default
        auto_generate_proxy_classes: true
        enable_lazy_ghost_objects: true
        entity_managers:
            auto_mapping: true
            default:
                report_fields_where_declared: true
                validate_xml_mapping: true
                naming_strategy: doctrine.orm.naming_strategy.underscore_number_aware
                connection: default
                mappings:
                    App:
                        type: attribute
                        is_bundle: false
                        dir: '%kernel.project_dir%/src/Entity'
                        prefix: 'App\Entity'
                        alias: App
                    SharedBundle:
                        type: attribute
                        is_bundle: true
                        dir: '/src/Entity'
                        prefix: 'Wb3\SharedBundle\Entity'
                        alias: SharedBundle
            wb2:
                connection: wb2

when@prod:
    doctrine:
        orm:
            auto_generate_proxy_classes: false
            proxy_dir: '%kernel.build_dir%/doctrine/orm/Proxies'
            query_cache_driver:
                type: pool
                pool: doctrine.system_cache_pool
            result_cache_driver:
                type: pool
                pool: doctrine.result_cache_pool

    framework:
        cache:
            pools:
                doctrine.result_cache_pool:
                    adapter: cache.app
                doctrine.system_cache_pool:
                    adapter: cache.system
